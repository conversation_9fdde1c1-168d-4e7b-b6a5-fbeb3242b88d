import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { Document, ProcessingConfiguration, ProcessingResult } from '../types/index'
import { documentsApi, ApiException } from '../services/api'

interface DocumentsState {
  documents: Document[]
  selectedDocument: Document | null
  isLoading: boolean
  uploadProgress: number
  processingResults: Record<string, ProcessingResult>
}

interface DocumentsStore extends DocumentsState {
  // Actions
  uploadDocument: (file: File, metadata?: any) => Promise<Document>
  getUserDocuments: (limit?: number, offset?: number) => Promise<void>
  getDocument: (id: string) => Promise<Document>
  processDocument: (id: string, configuration: ProcessingConfiguration) => Promise<ProcessingResult>
  deleteDocument: (id: string) => Promise<void>
  setSelectedDocument: (document: Document | null) => void
  setLoading: (loading: boolean) => void
  setUploadProgress: (progress: number) => void
  clearDocuments: () => void
}

export const useDocumentsStore = create<DocumentsStore>()(
  persist(
    (set, get) => ({
      // State
      documents: [],
      selectedDocument: null,
      isLoading: false,
      uploadProgress: 0,
      processingResults: {},

      // Actions
      uploadDocument: async (file: File, metadata?: any) => {
        set({ isLoading: true, uploadProgress: 0 })

        try {
          const formData = new FormData()
          formData.append('file', file)

          // Configuration de processing par défaut
          const defaultConfiguration = {
            chunking: {
              strategy: 'Semantic',
              maxChunkSize: 1000,
              overlapSize: 200,
              minChunkSize: 100,
              preserveSentences: true,
              preserveParagraphs: true
            },
            embeddingModel: 'OpenAISmall',
            targetDatabases: ['MongoDB'],
            performQualityAssurance: true,
            extractNamedEntities: true,
            extractKeywords: true,
            priority: 'High',
            customOptions: {}
          }

          // Combiner les métadonnées avec la configuration
          const combinedMetadata = {
            configuration: defaultConfiguration,
            ...metadata
          }

          if (combinedMetadata) {
            formData.append('metadata', JSON.stringify(combinedMetadata))
          }

          // Simuler le progrès d'upload
          const uploadInterval = setInterval(() => {
            set(state => ({
              uploadProgress: Math.min(state.uploadProgress + 10, 90)
            }))
          }, 100)

          const document = await documentsApi.uploadDocument(formData)

          clearInterval(uploadInterval)
          set({ uploadProgress: 100 })

          // Ajouter le document à la liste
          set(state => ({
            documents: [document, ...state.documents],
            isLoading: false,
            uploadProgress: 0
          }))

          return document
        } catch (error) {
          set({ isLoading: false, uploadProgress: 0 })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      getUserDocuments: async (limit?: number, offset?: number) => {
        set({ isLoading: true })

        try {
          const documents = await documentsApi.getUserDocuments(limit, offset)
          set({ documents, isLoading: false })
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      getDocument: async (id: string) => {
        set({ isLoading: true })

        try {
          const document = await documentsApi.getDocument(id)
          set({ selectedDocument: document, isLoading: false })
          return document
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      processDocument: async (id: string, configuration: ProcessingConfiguration) => {
        set({ isLoading: true })

        try {
          const result = await documentsApi.processDocument(id, configuration)

          // Mettre à jour le résultat de traitement
          set(state => ({
            processingResults: {
              ...state.processingResults,
              [id]: result
            },
            isLoading: false
          }))

          // Mettre à jour le statut du document
          const { documents } = get()
          const updatedDocuments = documents.map(doc =>
            doc.id === id
              ? { ...doc, status: result.success ? 'Processed' : 'Failed' }
              : doc
          )
          set({ documents: updatedDocuments })

          return result
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      deleteDocument: async (id: string) => {
        set({ isLoading: true })

        try {
          await documentsApi.deleteDocument(id)

          // Supprimer le document de la liste
          set(state => ({
            documents: state.documents.filter(doc => doc.id !== id),
            selectedDocument: state.selectedDocument?.id === id ? null : state.selectedDocument,
            isLoading: false
          }))
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      setSelectedDocument: (document: Document | null) => {
        set({ selectedDocument: document })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      setUploadProgress: (progress: number) => {
        set({ uploadProgress: progress })
      },

      clearDocuments: () => {
        set({
          documents: [],
          selectedDocument: null,
          processingResults: {},
          uploadProgress: 0
        })
      }
    }),
    {
      name: 'documents-store',
      partialize: (state) => ({
        documents: state.documents,
        processingResults: state.processingResults
      })
    }
  )
)
