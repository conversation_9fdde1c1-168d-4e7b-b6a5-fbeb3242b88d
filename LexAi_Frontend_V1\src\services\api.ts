import { useAuthStore } from '../store/authStore'

// Configuration de base de l'API
const IDENTITY_API_BASE_URL = import.meta.env.VITE_IDENTITY_API_URL || 'http://localhost:5000'
const DATAPROCESSING_API_BASE_URL = import.meta.env.VITE_DATAPROCESSING_API_URL || 'http://localhost:5001'

// Interface pour les erreurs API
interface ApiError {
  message: string
  status: number
  details?: any
}

// Classe d'erreur personnalisée
export class ApiException extends Error {
  status: number
  details?: any

  constructor(message: string, status: number, details?: any) {
    super(message)
    this.name = 'ApiException'
    this.status = status
    this.details = details
  }
}

// Configuration par défaut pour fetch
const defaultHeaders = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
}

// Fonction utilitaire pour créer les headers avec authentification
const createAuthHeaders = (token?: string): HeadersInit => {
  const headers: HeadersInit = { ...defaultHeaders }

  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }

  return headers
}

// Fonction utilitaire pour gérer les réponses
const handleResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`
    let errorDetails: any = null

    try {
      const errorData = await response.json()
      errorMessage = errorData.detail || errorData.message || errorMessage
      errorDetails = errorData
    } catch {
      // Si on ne peut pas parser le JSON, on garde le message par défaut
    }

    throw new ApiException(errorMessage, response.status, errorDetails)
  }

  // Gérer les réponses vides (204 No Content)
  if (response.status === 204) {
    return {} as T
  }

  try {
    return await response.json()
  } catch {
    throw new ApiException('Invalid JSON response', response.status)
  }
}

// Service API principal
export class ApiService {
  private baseUrl: string

  constructor(baseUrl: string = IDENTITY_API_BASE_URL) {
    this.baseUrl = baseUrl
  }

  // GET request
  async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const token = localStorage.getItem('accessToken')

    // Debug log
    if (import.meta.env.VITE_DEBUG_API === 'true') {
      console.log('API GET Request:', {
        url: `${this.baseUrl}${endpoint}`,
        hasToken: !!token,
        token: token ? `${token.substring(0, 20)}...` : null
      })
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'GET',
      headers: createAuthHeaders(token),
      ...options,
    })

    return handleResponse<T>(response)
  }

  // POST request
  async post<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    const token = localStorage.getItem('accessToken')

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: createAuthHeaders(token),
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    })

    return handleResponse<T>(response)
  }

  // PUT request
  async put<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    const token = localStorage.getItem('accessToken')

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PUT',
      headers: createAuthHeaders(token),
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    })

    return handleResponse<T>(response)
  }

  // PATCH request
  async patch<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    const token = localStorage.getItem('accessToken')

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PATCH',
      headers: createAuthHeaders(token),
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    })

    return handleResponse<T>(response)
  }

  // DELETE request
  async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const token = localStorage.getItem('accessToken')

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'DELETE',
      headers: createAuthHeaders(token),
      ...options,
    })

    return handleResponse<T>(response)
  }

  // Upload de fichier
  async upload<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<T> {
    const token = localStorage.getItem('accessToken')
    const formData = new FormData()

    formData.append('file', file)

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, typeof value === 'string' ? value : JSON.stringify(value))
      })
    }

    const headers: HeadersInit = {}
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData,
    })

    return handleResponse<T>(response)
  }

  // Refresh token
  async refreshToken(refreshToken: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/auth/refresh`, {
      method: 'POST',
      headers: defaultHeaders,
      body: JSON.stringify({ refreshToken }),
    })

    return handleResponse(response)
  }
}

// Instance par défaut du service API
export const apiService = new ApiService()

// Intercepteur pour gérer automatiquement le refresh token
const originalFetch = window.fetch
window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
  const response = await originalFetch(input, init)

  // Si on reçoit une erreur 401 (Unauthorized) et qu'on a un refresh token
  if (response.status === 401) {
    const authStore = useAuthStore.getState()

    if (authStore.refreshToken && typeof input === 'string' && !input.includes('/refresh')) {
      try {
        // Tenter de rafraîchir le token
        const refreshResponse = await apiService.refreshToken(authStore.refreshToken)

        // Mettre à jour les tokens dans le store
        authStore.setTokens(refreshResponse.accessToken, refreshResponse.refreshToken)

        // Retry la requête originale avec le nouveau token
        const newHeaders = {
          ...init?.headers,
          'Authorization': `Bearer ${refreshResponse.accessToken}`
        }

        return originalFetch(input, {
          ...init,
          headers: newHeaders
        })
      } catch (refreshError) {
        // Si le refresh échoue, déconnecter l'utilisateur
        authStore.logout()
        window.location.href = '/login'
      }
    }
  }

  return response
}

// Services spécialisés
export const authApi = {
  login: (email: string, password: string) =>
    apiService.post('/api/auth/login', { email, password }),

  register: (userData: any) =>
    apiService.post('/api/auth/register', userData),

  logout: () =>
    apiService.post('/api/auth/logout'),

  refreshToken: (refreshToken: string) =>
    apiService.post('/api/auth/refresh', { refreshToken }),

  forgotPassword: (email: string) =>
    apiService.post('/api/auth/forgot-password', { email }),

  resetPassword: (token: string, newPassword: string) =>
    apiService.post('/api/auth/reset-password', { token, newPassword }),

  changePassword: (currentPassword: string, newPassword: string) =>
    apiService.post('/api/auth/change-password', { currentPassword, newPassword }),

  getCurrentUser: () =>
    apiService.get('/api/auth/me'),
}

export const usersApi = {
  getUsers: (limit?: number, offset?: number) =>
    apiService.get(`/api/users?${new URLSearchParams({
      ...(limit && { limit: limit.toString() }),
      ...(offset && { offset: offset.toString() })
    })}`),

  getUser: (id: string) =>
    apiService.get(`/api/users/${id}`),

  createUser: (userData: any) =>
    apiService.post('/api/users', userData),

  updateUser: (id: string, userData: any) =>
    apiService.put(`/api/users/${id}`, userData),

  deleteUser: (id: string) =>
    apiService.delete(`/api/users/${id}`),
}

// Service DataProcessing
const dataProcessingService = new ApiService(DATAPROCESSING_API_BASE_URL)

export const documentsApi = {
  uploadDocument: (formData: FormData) => {
    const token = localStorage.getItem('accessToken')
    const headers: HeadersInit = {}
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
    // Ne pas définir Content-Type pour FormData, le navigateur le fait automatiquement

    // Debug log
    if (import.meta.env.VITE_DEBUG_API === 'true') {
      console.log('Document Upload Request:', {
        url: `${DATAPROCESSING_API_BASE_URL}/api/documents/upload`,
        hasToken: !!token,
        formDataEntries: Array.from(formData.entries()).map(([key, value]) => ({
          key,
          value: value instanceof File ? `File: ${value.name} (${value.size} bytes)` : value
        }))
      })
    }

    return fetch(`${DATAPROCESSING_API_BASE_URL}/api/documents/upload`, {
      method: 'POST',
      headers,
      body: formData,
    }).then(handleResponse)
  },

  getUserDocuments: (limit?: number, offset?: number) =>
    dataProcessingService.get(`/api/documents?${new URLSearchParams({
      ...(limit && { limit: limit.toString() }),
      ...(offset && { offset: offset.toString() })
    })}`),

  getDocument: (id: string) =>
    dataProcessingService.get(`/api/documents/${id}`),

  processDocument: (id: string, configuration: any) =>
    dataProcessingService.post(`/api/documents/${id}/process`, configuration),

  getProcessingStatus: (id: string) =>
    dataProcessingService.get(`/api/documents/${id}/status`),

  deleteDocument: (id: string) =>
    dataProcessingService.delete(`/api/documents/${id}`),
}

export const userApi = {
  getCurrentUser: () =>
    apiService.get('/api/users/me'),

  updateProfile: (userData: any) =>
    apiService.put('/api/users/me', userData),

  changePassword: (currentPassword: string, newPassword: string) =>
    apiService.post('/api/users/change-password', { currentPassword, newPassword }),

  uploadAvatar: (file: File) =>
    apiService.upload('/api/users/avatar', file),
}

export const aiAssistantApi = {
  sendMessage: (message: string, conversationId?: string) =>
    apiService.post('/api/ai-assistant/chat', { message, conversationId }),

  getConversations: () =>
    apiService.get('/api/ai-assistant/conversations'),

  getConversation: (id: string) =>
    apiService.get(`/api/ai-assistant/conversations/${id}`),

  deleteConversation: (id: string) =>
    apiService.delete(`/api/ai-assistant/conversations/${id}`),
}

export const searchApi = {
  searchDocuments: (query: string, filters?: any) =>
    apiService.post('/api/search/documents', { query, filters }),

  searchCases: (query: string, filters?: any) =>
    apiService.post('/api/search/cases', { query, filters }),

  getSearchSuggestions: (query: string) =>
    apiService.get(`/api/search/suggestions?q=${encodeURIComponent(query)}`),
}
