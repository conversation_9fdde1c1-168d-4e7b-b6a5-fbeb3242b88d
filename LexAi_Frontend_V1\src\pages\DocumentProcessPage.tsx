import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { 
  ArrowLeft, 
  Settings, 
  Play, 
  CheckCircle, 
  AlertCircle,
  Clock,
  FileText,
  Zap,
  Database,
  Brain
} from 'lucide-react'
import { <PERSON><PERSON> } from '../components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card'
import { useDocumentsStore } from '../store/documentsStore'
import { 
  ProcessingConfiguration, 
  ChunkingStrategy, 
  EmbeddingModelType, 
  VectorDatabaseType 
} from '../types/index'

export function DocumentProcessPage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  
  const { 
    selectedDocument, 
    getDocument, 
    processDocument, 
    processingResults,
    isLoading 
  } = useDocumentsStore()

  const [configuration, setConfiguration] = useState<ProcessingConfiguration>({
    chunking: {
      strategy: ChunkingStrategy.Semantic,
      maxTokens: 1000,
      overlapTokens: 100,
      preserveStructure: true
    },
    embeddingModel: EmbeddingModelType.OpenAISmall,
    targetDatabases: [VectorDatabaseType.Pinecone],
    performQualityAssurance: true,
    extractNamedEntities: true
  })

  const [processingStatus, setProcessingStatus] = useState<'idle' | 'processing' | 'completed' | 'error'>('idle')

  useEffect(() => {
    if (id) {
      getDocument(id)
    }
  }, [id, getDocument])

  const handleProcess = async () => {
    if (!id) return

    setProcessingStatus('processing')
    try {
      await processDocument(id, configuration)
      setProcessingStatus('completed')
    } catch (error) {
      console.error('Erreur lors du traitement:', error)
      setProcessingStatus('error')
    }
  }

  const processingResult = id ? processingResults[id] : null

  if (!selectedDocument) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Document non trouvé</h2>
          <p className="mt-2 text-gray-600">Le document demandé n'existe pas ou n'est pas accessible.</p>
          <Button className="mt-4" onClick={() => navigate('/documents')}>
            Retour aux documents
          </Button>
        </div>
      </div>
    )
  }

  if (processingStatus === 'completed' && processingResult) {
    return (
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="flex items-center space-x-4">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/documents')}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Traitement terminé</h1>
            <p className="text-gray-600">{selectedDocument.originalFileName}</p>
          </div>
        </div>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
              <h2 className="mt-4 text-xl font-semibold text-gray-900">
                Document traité avec succès !
              </h2>
              <p className="mt-2 text-gray-600">
                Le traitement de votre document est terminé.
              </p>
            </div>

            <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {processingResult.chunksCreated}
                </div>
                <div className="text-sm text-blue-800">Chunks créés</div>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {Math.round(processingResult.processingTimeMs / 1000)}s
                </div>
                <div className="text-sm text-green-800">Temps de traitement</div>
              </div>
              
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {processingResult.totalTokens.toLocaleString()}
                </div>
                <div className="text-sm text-purple-800">Tokens traités</div>
              </div>
              
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">
                  ${processingResult.estimatedCost.toFixed(4)}
                </div>
                <div className="text-sm text-yellow-800">Coût estimé</div>
              </div>
            </div>

            {processingResult.vectorDatabasesUsed.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Bases de données vectorielles utilisées
                </h3>
                <div className="flex flex-wrap gap-2">
                  {processingResult.vectorDatabasesUsed.map((db, index) => (
                    <span 
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                    >
                      <Database className="h-4 w-4 mr-1" />
                      {db}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {(processingResult.errors.length > 0 || processingResult.warnings.length > 0) && (
              <div className="mt-6 space-y-3">
                {processingResult.errors.length > 0 && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <h4 className="text-sm font-medium text-red-900 mb-2">Erreurs :</h4>
                    <ul className="text-sm text-red-800 space-y-1">
                      {processingResult.errors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {processingResult.warnings.length > 0 && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <h4 className="text-sm font-medium text-yellow-900 mb-2">Avertissements :</h4>
                    <ul className="text-sm text-yellow-800 space-y-1">
                      {processingResult.warnings.map((warning, index) => (
                        <li key={index}>• {warning}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            <div className="mt-8 flex gap-3 justify-center">
              <Button onClick={() => navigate(`/documents/${id}`)}>
                Voir le document
              </Button>
              <Button variant="outline" onClick={() => navigate('/documents')}>
                Retour aux documents
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button 
          variant="ghost" 
          onClick={() => navigate('/documents')}
          className="p-2"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Traitement de document</h1>
          <p className="text-gray-600">{selectedDocument.originalFileName}</p>
        </div>
      </div>

      {/* Informations du document */}
      <Card>
        <CardHeader>
          <CardTitle>Informations du document</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nom du fichier
              </label>
              <p className="text-sm text-gray-900">{selectedDocument.originalFileName}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Taille
              </label>
              <p className="text-sm text-gray-900">
                {(selectedDocument.fileSize / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type
              </label>
              <p className="text-sm text-gray-900">{selectedDocument.mimeType}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration du traitement */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Configuration du traitement
          </CardTitle>
          <CardDescription>
            Personnalisez les paramètres de traitement selon vos besoins
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Stratégie de chunking */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              <Zap className="inline h-4 w-4 mr-1" />
              Stratégie de découpage
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {Object.values(ChunkingStrategy).map((strategy) => (
                <label key={strategy} className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="chunkingStrategy"
                    value={strategy}
                    checked={configuration.chunking.strategy === strategy}
                    onChange={(e) => setConfiguration(prev => ({
                      ...prev,
                      chunking: { ...prev.chunking, strategy: e.target.value as ChunkingStrategy }
                    }))}
                    className="text-blue-600"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{strategy}</div>
                    <div className="text-xs text-gray-500">
                      {strategy === ChunkingStrategy.Semantic && 'Découpage basé sur le sens'}
                      {strategy === ChunkingStrategy.FixedSize && 'Taille fixe'}
                      {strategy === ChunkingStrategy.Paragraph && 'Par paragraphe'}
                      {strategy === ChunkingStrategy.Sentence && 'Par phrase'}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Paramètres de chunking */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tokens maximum par chunk
              </label>
              <input
                type="number"
                min="100"
                max="4000"
                value={configuration.chunking.maxTokens}
                onChange={(e) => setConfiguration(prev => ({
                  ...prev,
                  chunking: { ...prev.chunking, maxTokens: parseInt(e.target.value) }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tokens de chevauchement
              </label>
              <input
                type="number"
                min="0"
                max="500"
                value={configuration.chunking.overlapTokens}
                onChange={(e) => setConfiguration(prev => ({
                  ...prev,
                  chunking: { ...prev.chunking, overlapTokens: parseInt(e.target.value) }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Modèle d'embedding */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              <Brain className="inline h-4 w-4 mr-1" />
              Modèle d'embedding
            </label>
            <div className="space-y-2">
              {Object.values(EmbeddingModelType).map((model) => (
                <label key={model} className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="embeddingModel"
                    value={model}
                    checked={configuration.embeddingModel === model}
                    onChange={(e) => setConfiguration(prev => ({
                      ...prev,
                      embeddingModel: e.target.value as EmbeddingModelType
                    }))}
                    className="text-blue-600"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{model}</div>
                    <div className="text-xs text-gray-500">
                      {model === EmbeddingModelType.OpenAISmall && 'Rapide et économique'}
                      {model === EmbeddingModelType.OpenAILarge && 'Haute qualité'}
                      {model === EmbeddingModelType.Azure && 'Azure OpenAI'}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Options avancées */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Options avancées
            </label>
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={configuration.performQualityAssurance}
                  onChange={(e) => setConfiguration(prev => ({
                    ...prev,
                    performQualityAssurance: e.target.checked
                  }))}
                  className="text-blue-600"
                />
                <div>
                  <div className="text-sm font-medium text-gray-900">Assurance qualité</div>
                  <div className="text-xs text-gray-500">Vérification automatique de la qualité du contenu</div>
                </div>
              </label>
              
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={configuration.extractNamedEntities}
                  onChange={(e) => setConfiguration(prev => ({
                    ...prev,
                    extractNamedEntities: e.target.checked
                  }))}
                  className="text-blue-600"
                />
                <div>
                  <div className="text-sm font-medium text-gray-900">Extraction d'entités nommées</div>
                  <div className="text-xs text-gray-500">Identification automatique des personnes, lieux, organisations</div>
                </div>
              </label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardContent className="pt-6">
          {processingStatus === 'processing' && (
            <div className="mb-4 text-center">
              <Clock className="mx-auto h-8 w-8 text-blue-500 animate-spin" />
              <p className="mt-2 text-sm text-gray-600">Traitement en cours...</p>
            </div>
          )}

          {processingStatus === 'error' && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                <p className="text-sm text-red-600">
                  Erreur lors du traitement. Veuillez réessayer.
                </p>
              </div>
            </div>
          )}

          <div className="flex gap-3">
            <Button 
              onClick={handleProcess}
              disabled={isLoading || processingStatus === 'processing'}
              className="flex-1"
            >
              <Play className="h-4 w-4 mr-2" />
              {processingStatus === 'processing' ? 'Traitement...' : 'Lancer le traitement'}
            </Button>
            <Button 
              variant="outline"
              onClick={() => navigate('/documents')}
              disabled={processingStatus === 'processing'}
            >
              Annuler
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
