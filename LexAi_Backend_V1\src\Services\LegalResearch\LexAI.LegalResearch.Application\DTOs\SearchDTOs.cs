using LexAI.LegalResearch.Domain.ValueObjects;

namespace LexAI.LegalResearch.Application.DTOs;

/// <summary>
/// Search request DTO
/// </summary>
public class SearchRequestDto
{
    /// <summary>
    /// Search query text
    /// </summary>
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// User ID performing the search
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Session ID for grouping searches
    /// </summary>
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// Search method to use
    /// </summary>
    public SearchMethod Method { get; set; } = SearchMethod.Hybrid;

    /// <summary>
    /// Legal domain filter
    /// </summary>
    public LegalDomain? DomainFilter { get; set; }

    /// <summary>
    /// Document type filter
    /// </summary>
    public DocumentType? TypeFilter { get; set; }

    /// <summary>
    /// Language filter
    /// </summary>
    public string? LanguageFilter { get; set; }

    /// <summary>
    /// Date range filter
    /// </summary>
    public DateRangeDto? DateFilter { get; set; }

    /// <summary>
    /// Maximum number of results
    /// </summary>
    public int Limit { get; set; } = 20;

    /// <summary>
    /// Result offset for pagination
    /// </summary>
    public int Offset { get; set; } = 0;

    /// <summary>
    /// Minimum relevance score threshold
    /// </summary>
    public double MinRelevanceScore { get; set; } = 0.5;

    /// <summary>
    /// Include highlights in results
    /// </summary>
    public bool IncludeHighlights { get; set; } = true;

    /// <summary>
    /// Include similar documents
    /// </summary>
    public bool IncludeSimilar { get; set; } = false;

    /// <summary>
    /// Sort order for results
    /// </summary>
    public SearchSortOrder SortOrder { get; set; } = SearchSortOrder.Relevance;

    /// <summary>
    /// Additional search parameters
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Search response DTO
/// </summary>
public class SearchResponseDto
{
    /// <summary>
    /// Search query ID
    /// </summary>
    public Guid QueryId { get; set; }

    /// <summary>
    /// Original query text
    /// </summary>
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// Processed query text
    /// </summary>
    public string ProcessedQuery { get; set; } = string.Empty;

    /// <summary>
    /// Search results
    /// </summary>
    public List<SearchResultDto> Results { get; set; } = new();

    /// <summary>
    /// Total number of results found
    /// </summary>
    public int TotalResults { get; set; }

    /// <summary>
    /// Search execution time in milliseconds
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// Search method used
    /// </summary>
    public SearchMethod Method { get; set; }

    /// <summary>
    /// Query intent detected
    /// </summary>
    public QueryIntent Intent { get; set; }

    /// <summary>
    /// Search quality score
    /// </summary>
    public double QualityScore { get; set; }

    /// <summary>
    /// Whether results were cached
    /// </summary>
    public bool IsCached { get; set; }

    /// <summary>
    /// Search suggestions for query improvement
    /// </summary>
    public List<string> Suggestions { get; set; } = new();

    /// <summary>
    /// Related search terms
    /// </summary>
    public List<string> RelatedTerms { get; set; } = new();

    /// <summary>
    /// Faceted search results
    /// </summary>
    public SearchFacetsDto? Facets { get; set; }

    /// <summary>
    /// Search metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Search result DTO
/// </summary>
public class SearchResultDto
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Document title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Document summary
    /// </summary>
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// Relevance score
    /// </summary>
    public double RelevanceScore { get; set; }

    /// <summary>
    /// Semantic similarity score
    /// </summary>
    public double SimilarityScore { get; set; }

    /// <summary>
    /// Keyword match score
    /// </summary>
    public double KeywordScore { get; set; }

    /// <summary>
    /// Document type
    /// </summary>
    public DocumentType DocumentType { get; set; }

    /// <summary>
    /// Legal domain
    /// </summary>
    public LegalDomain LegalDomain { get; set; }

    /// <summary>
    /// Document source
    /// </summary>
    public DocumentSourceDto Source { get; set; } = null!;

    /// <summary>
    /// Text highlights
    /// </summary>
    public List<TextHighlightDto> Highlights { get; set; } = new();

    /// <summary>
    /// Matched chunks
    /// </summary>
    public List<MatchedChunkDto> MatchedChunks { get; set; } = new();

    /// <summary>
    /// Document publication date
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Document effective date
    /// </summary>
    public DateTime? EffectiveDate { get; set; }

    /// <summary>
    /// Document tags
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Document URL
    /// </summary>
    public string? DocumentUrl { get; set; }

    /// <summary>
    /// Search rank
    /// </summary>
    public int Rank { get; set; }

    /// <summary>
    /// Match explanation
    /// </summary>
    public string? MatchExplanation { get; set; }

    /// <summary>
    /// Similar documents
    /// </summary>
    public List<SimilarDocumentDto> SimilarDocuments { get; set; } = new();
}

/// <summary>
/// Document source DTO
/// </summary>
public class DocumentSourceDto
{
    /// <summary>
    /// Source name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Source URL
    /// </summary>
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// Source type
    /// </summary>
    public SourceType Type { get; set; }

    /// <summary>
    /// Authority level
    /// </summary>
    public AuthorityLevel Authority { get; set; }

    /// <summary>
    /// Jurisdiction
    /// </summary>
    public string Jurisdiction { get; set; } = string.Empty;

    /// <summary>
    /// Reliability score
    /// </summary>
    public double ReliabilityScore { get; set; }
}

/// <summary>
/// Text highlight DTO
/// </summary>
public class TextHighlightDto
{
    /// <summary>
    /// Highlighted text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Start position in document
    /// </summary>
    public int StartPosition { get; set; }

    /// <summary>
    /// End position in document
    /// </summary>
    public int EndPosition { get; set; }

    /// <summary>
    /// Highlight score
    /// </summary>
    public double Score { get; set; }

    /// <summary>
    /// Highlight type
    /// </summary>
    public string Type { get; set; } = string.Empty;
}

/// <summary>
/// Matched chunk DTO
/// </summary>
public class MatchedChunkDto
{
    /// <summary>
    /// Chunk ID
    /// </summary>
    public Guid ChunkId { get; set; }

    /// <summary>
    /// Chunk content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Chunk type
    /// </summary>
    public ChunkType Type { get; set; }

    /// <summary>
    /// Similarity score
    /// </summary>
    public double SimilarityScore { get; set; }

    /// <summary>
    /// Start position in document
    /// </summary>
    public int StartPosition { get; set; }

    /// <summary>
    /// End position in document
    /// </summary>
    public int EndPosition { get; set; }
}

/// <summary>
/// Similar document DTO
/// </summary>
public class SimilarDocumentDto
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Document title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Similarity score
    /// </summary>
    public double SimilarityScore { get; set; }

    /// <summary>
    /// Document type
    /// </summary>
    public DocumentType DocumentType { get; set; }

    /// <summary>
    /// Legal domain
    /// </summary>
    public LegalDomain LegalDomain { get; set; }
}

/// <summary>
/// Date range DTO
/// </summary>
public class DateRangeDto
{
    /// <summary>
    /// Start date
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// End date
    /// </summary>
    public DateTime? EndDate { get; set; }
}

/// <summary>
/// Search facets DTO
/// </summary>
public class SearchFacetsDto
{
    /// <summary>
    /// Document type facets
    /// </summary>
    public Dictionary<DocumentType, int> DocumentTypes { get; set; } = new();

    /// <summary>
    /// Legal domain facets
    /// </summary>
    public Dictionary<LegalDomain, int> LegalDomains { get; set; } = new();

    /// <summary>
    /// Source facets
    /// </summary>
    public Dictionary<string, int> Sources { get; set; } = new();

    /// <summary>
    /// Year facets
    /// </summary>
    public Dictionary<int, int> Years { get; set; } = new();

    /// <summary>
    /// Tag facets
    /// </summary>
    public Dictionary<string, int> Tags { get; set; } = new();
}

/// <summary>
/// Search sort order enumeration
/// </summary>
public enum SearchSortOrder
{
    /// <summary>
    /// Sort by relevance score
    /// </summary>
    Relevance,

    /// <summary>
    /// Sort by publication date (newest first)
    /// </summary>
    DateDesc,

    /// <summary>
    /// Sort by publication date (oldest first)
    /// </summary>
    DateAsc,

    /// <summary>
    /// Sort by title alphabetically
    /// </summary>
    Title,

    /// <summary>
    /// Sort by authority level
    /// </summary>
    Authority
}
