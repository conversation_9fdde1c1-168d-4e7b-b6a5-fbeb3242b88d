using LexAI.AIAssistant.Domain.ValueObjects;
using Xunit;

namespace LexAI.AIAssistant.Domain.Tests.ValueObjects;

public class MessageMetadataTests
{
    [Fact]
    public void Create_WithValidUserData_ShouldCreateMessageMetadata()
    {
        // Arrange
        var messageId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var ipAddress = "***********";
        var userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
        var sessionId = "session123";
        var requestId = "request456";

        // Act
        var metadata = MessageMetadata.Create(
            messageId, 
            userId, 
            ipAddress, 
            userAgent, 
            sessionId, 
            requestId);

        // Assert
        Assert.Equal(messageId, metadata.MessageId);
        Assert.Equal(userId, metadata.UserId);
        Assert.Equal(ipAddress, metadata.IpAddress);
        Assert.Equal(userAgent, metadata.UserAgent);
        Assert.Equal(sessionId, metadata.SessionId);
        Assert.Equal(requestId, metadata.RequestId);
        Assert.Equal("Desktop", metadata.DeviceType);
        Assert.Equal("Windows", metadata.Platform);
        Assert.Equal("Chrome", metadata.Browser);
    }

    [Fact]
    public void Create_WithSystemMessage_ShouldCreateMessageMetadata()
    {
        // Arrange
        var messageId = Guid.NewGuid();
        var requestId = "request789";

        // Act
        var metadata = MessageMetadata.Create(messageId, requestId);

        // Assert
        Assert.Equal(messageId, metadata.MessageId);
        Assert.Null(metadata.UserId);
        Assert.Equal(requestId, metadata.RequestId);
        Assert.Null(metadata.IpAddress);
        Assert.Null(metadata.UserAgent);
    }

    [Fact]
    public void Create_WithEmptyMessageId_ShouldThrowArgumentException()
    {
        // Arrange
        var userId = Guid.NewGuid();

        // Act & Assert
        Assert.Throws<ArgumentException>(() => 
            MessageMetadata.Create(Guid.Empty, userId));
    }

    [Fact]
    public void Create_WithEmptyUserId_ShouldThrowArgumentException()
    {
        // Arrange
        var messageId = Guid.NewGuid();

        // Act & Assert
        Assert.Throws<ArgumentException>(() => 
            MessageMetadata.Create(messageId, Guid.Empty));
    }

    [Fact]
    public void AddProperty_WithValidKeyValue_ShouldAddProperty()
    {
        // Arrange
        var messageId = Guid.NewGuid();
        var metadata = MessageMetadata.Create(messageId);
        var key = "customProperty";
        var value = "customValue";

        // Act
        metadata.AddProperty(key, value);

        // Assert
        Assert.Equal(value, metadata.GetProperty<string>(key));
    }

    [Fact]
    public void AddProperty_WithEmptyKey_ShouldThrowArgumentException()
    {
        // Arrange
        var messageId = Guid.NewGuid();
        var metadata = MessageMetadata.Create(messageId);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => 
            metadata.AddProperty("", "value"));
    }

    [Fact]
    public void GetProperty_WithNonExistentKey_ShouldReturnDefault()
    {
        // Arrange
        var messageId = Guid.NewGuid();
        var metadata = MessageMetadata.Create(messageId);

        // Act
        var result = metadata.GetProperty<string>("nonExistent");

        // Assert
        Assert.Null(result);
    }

    [Theory]
    [InlineData("Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)", "Mobile", "iOS", "Safari")]
    [InlineData("Mozilla/5.0 (Android 10; Mobile; rv:81.0)", "Mobile", "Android", "Firefox")]
    [InlineData("Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)", "Tablet", "iOS", "Safari")]
    [InlineData("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "Desktop", "Windows", "Chrome")]
    [InlineData("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36", "Desktop", "macOS", "Chrome")]
    [InlineData("Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36", "Desktop", "Linux", "Chrome")]
    public void Create_WithDifferentUserAgents_ShouldExtractCorrectInfo(
        string userAgent, 
        string expectedDeviceType, 
        string expectedPlatform, 
        string expectedBrowser)
    {
        // Arrange
        var messageId = Guid.NewGuid();
        var userId = Guid.NewGuid();

        // Act
        var metadata = MessageMetadata.Create(messageId, userId, userAgent: userAgent);

        // Assert
        Assert.Equal(expectedDeviceType, metadata.DeviceType);
        Assert.Equal(expectedPlatform, metadata.Platform);
        Assert.Equal(expectedBrowser, metadata.Browser);
    }

    [Fact]
    public void Equality_WithSameValues_ShouldBeEqual()
    {
        // Arrange
        var messageId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var ipAddress = "***********";

        var metadata1 = MessageMetadata.Create(messageId, userId, ipAddress);
        var metadata2 = MessageMetadata.Create(messageId, userId, ipAddress);

        // Act & Assert
        Assert.Equal(metadata1, metadata2);
        Assert.True(metadata1.Equals(metadata2));
        Assert.Equal(metadata1.GetHashCode(), metadata2.GetHashCode());
    }

    [Fact]
    public void Equality_WithDifferentValues_ShouldNotBeEqual()
    {
        // Arrange
        var messageId1 = Guid.NewGuid();
        var messageId2 = Guid.NewGuid();
        var userId = Guid.NewGuid();

        var metadata1 = MessageMetadata.Create(messageId1, userId);
        var metadata2 = MessageMetadata.Create(messageId2, userId);

        // Act & Assert
        Assert.NotEqual(metadata1, metadata2);
        Assert.False(metadata1.Equals(metadata2));
    }
}
