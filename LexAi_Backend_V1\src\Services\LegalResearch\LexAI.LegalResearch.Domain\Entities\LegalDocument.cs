using LexAI.LegalResearch.Domain.ValueObjects;
using LexAI.Shared.Domain.Common;

namespace LexAI.LegalResearch.Domain.Entities;

/// <summary>
/// Represents a legal document in the system
/// </summary>
public class LegalDocument : BaseEntity
{
    /// <summary>
    /// Document title
    /// </summary>
    public string Title { get; private set; } = string.Empty;

    /// <summary>
    /// Document content/text
    /// </summary>
    public string Content { get; private set; } = string.Empty;

    /// <summary>
    /// Document summary
    /// </summary>
    public string Summary { get; private set; } = string.Empty;

    /// <summary>
    /// Document type (law, regulation, jurisprudence, etc.)
    /// </summary>
    public DocumentType Type { get; private set; }

    /// <summary>
    /// Legal domain/category
    /// </summary>
    public LegalDomain Domain { get; private set; }

    /// <summary>
    /// Document source information
    /// </summary>
    public DocumentSource Source { get; private set; } = null!;

    /// <summary>
    /// Document metadata
    /// </summary>
    public DocumentMetadata Metadata { get; private set; } = null!;

    /// <summary>
    /// Document language (ISO 639-1 code)
    /// </summary>
    public string Language { get; private set; } = "fr";

    /// <summary>
    /// Document publication date
    /// </summary>
    public DateTime? PublicationDate { get; private set; }

    /// <summary>
    /// Document effective date
    /// </summary>
    public DateTime? EffectiveDate { get; private set; }

    /// <summary>
    /// Document expiration date (if applicable)
    /// </summary>
    public DateTime? ExpirationDate { get; private set; }

    /// <summary>
    /// Document status
    /// </summary>
    public DocumentStatus Status { get; private set; }

    /// <summary>
    /// Document priority/importance level
    /// </summary>
    public DocumentPriority Priority { get; private set; }

    /// <summary>
    /// Document tags for categorization
    /// </summary>
    public List<string> Tags { get; private set; } = new();

    /// <summary>
    /// Document keywords for search
    /// </summary>
    public List<string> Keywords { get; private set; } = new();

    /// <summary>
    /// Related document references
    /// </summary>
    public List<DocumentReference> References { get; private set; } = new();

    /// <summary>
    /// Document chunks for vector search
    /// </summary>
    public List<DocumentChunk> Chunks { get; private set; } = new();

    /// <summary>
    /// Document file path or URL
    /// </summary>
    public string? FilePath { get; private set; }

    /// <summary>
    /// Document file size in bytes
    /// </summary>
    public long? FileSize { get; private set; }

    /// <summary>
    /// Document file hash for integrity
    /// </summary>
    public string? FileHash { get; private set; }

    /// <summary>
    /// Number of times this document has been accessed
    /// </summary>
    public int AccessCount { get; private set; }

    /// <summary>
    /// Last access timestamp
    /// </summary>
    public DateTime? LastAccessedAt { get; private set; }

    /// <summary>
    /// Document indexing status
    /// </summary>
    public bool IsIndexed { get; private set; }

    /// <summary>
    /// Document indexing timestamp
    /// </summary>
    public DateTime? IndexedAt { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private LegalDocument() { }

    /// <summary>
    /// Creates a new legal document
    /// </summary>
    /// <param name="title">Document title</param>
    /// <param name="content">Document content</param>
    /// <param name="type">Document type</param>
    /// <param name="domain">Legal domain</param>
    /// <param name="source">Document source</param>
    /// <param name="createdBy">User who created the document</param>
    /// <returns>New legal document instance</returns>
    public static LegalDocument Create(
        string title,
        string content,
        DocumentType type,
        LegalDomain domain,
        DocumentSource source,
        string createdBy)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        if (string.IsNullOrWhiteSpace(createdBy))
            throw new ArgumentException("CreatedBy cannot be empty", nameof(createdBy));

        var document = new LegalDocument
        {
            Id = Guid.NewGuid(),
            Title = title.Trim(),
            Content = content.Trim(),
            Type = type,
            Domain = domain,
            Source = source,
            Status = DocumentStatus.Draft,
            Priority = DocumentPriority.Normal,
            Language = "fr",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = createdBy,
            IsIndexed = false
        };

        // Generate summary from content (first 500 characters)
        document.Summary = content.Length > 500 
            ? content.Substring(0, 500) + "..." 
            : content;

        // Initialize metadata
        document.Metadata = DocumentMetadata.Create(document.Id);

        return document;
    }

    /// <summary>
    /// Updates document content and resets indexing status
    /// </summary>
    /// <param name="content">New content</param>
    /// <param name="updatedBy">User who updated the document</param>
    public void UpdateContent(string content, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        Content = content.Trim();
        Summary = content.Length > 500 ? content.Substring(0, 500) + "..." : content;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy;
        
        // Reset indexing status when content changes
        IsIndexed = false;
        IndexedAt = null;
        
        // Clear existing chunks as they need to be regenerated
        Chunks.Clear();
    }

    /// <summary>
    /// Updates document metadata
    /// </summary>
    /// <param name="title">Document title</param>
    /// <param name="type">Document type</param>
    /// <param name="domain">Legal domain</param>
    /// <param name="publicationDate">Publication date</param>
    /// <param name="effectiveDate">Effective date</param>
    /// <param name="updatedBy">User who updated the document</param>
    public void UpdateMetadata(
        string title,
        DocumentType type,
        LegalDomain domain,
        DateTime? publicationDate,
        DateTime? effectiveDate,
        string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        Title = title.Trim();
        Type = type;
        Domain = domain;
        PublicationDate = publicationDate;
        EffectiveDate = effectiveDate;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy;
    }

    /// <summary>
    /// Adds tags to the document
    /// </summary>
    /// <param name="tags">Tags to add</param>
    public void AddTags(params string[] tags)
    {
        foreach (var tag in tags.Where(t => !string.IsNullOrWhiteSpace(t)))
        {
            var normalizedTag = tag.Trim().ToLowerInvariant();
            if (!Tags.Contains(normalizedTag))
            {
                Tags.Add(normalizedTag);
            }
        }
    }

    /// <summary>
    /// Removes tags from the document
    /// </summary>
    /// <param name="tags">Tags to remove</param>
    public void RemoveTags(params string[] tags)
    {
        foreach (var tag in tags.Where(t => !string.IsNullOrWhiteSpace(t)))
        {
            var normalizedTag = tag.Trim().ToLowerInvariant();
            Tags.Remove(normalizedTag);
        }
    }

    /// <summary>
    /// Adds keywords to the document
    /// </summary>
    /// <param name="keywords">Keywords to add</param>
    public void AddKeywords(params string[] keywords)
    {
        foreach (var keyword in keywords.Where(k => !string.IsNullOrWhiteSpace(k)))
        {
            var normalizedKeyword = keyword.Trim().ToLowerInvariant();
            if (!Keywords.Contains(normalizedKeyword))
            {
                Keywords.Add(normalizedKeyword);
            }
        }
    }

    /// <summary>
    /// Adds a document reference
    /// </summary>
    /// <param name="reference">Document reference to add</param>
    public void AddReference(DocumentReference reference)
    {
        if (reference == null)
            throw new ArgumentNullException(nameof(reference));

        if (!References.Any(r => r.ReferencedDocumentId == reference.ReferencedDocumentId))
        {
            References.Add(reference);
        }
    }

    /// <summary>
    /// Adds document chunks for vector search
    /// </summary>
    /// <param name="chunks">Document chunks to add</param>
    public void AddChunks(IEnumerable<DocumentChunk> chunks)
    {
        if (chunks == null)
            throw new ArgumentNullException(nameof(chunks));

        Chunks.Clear();
        Chunks.AddRange(chunks);
    }

    /// <summary>
    /// Marks the document as indexed
    /// </summary>
    public void MarkAsIndexed()
    {
        IsIndexed = true;
        IndexedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Records document access
    /// </summary>
    public void RecordAccess()
    {
        AccessCount++;
        LastAccessedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Updates document status
    /// </summary>
    /// <param name="status">New status</param>
    /// <param name="updatedBy">User who updated the status</param>
    public void UpdateStatus(DocumentStatus status, string updatedBy)
    {
        Status = status;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy;
    }

    /// <summary>
    /// Sets document file information
    /// </summary>
    /// <param name="filePath">File path or URL</param>
    /// <param name="fileSize">File size in bytes</param>
    /// <param name="fileHash">File hash for integrity</param>
    public void SetFileInfo(string filePath, long fileSize, string fileHash)
    {
        FilePath = filePath;
        FileSize = fileSize;
        FileHash = fileHash;
    }

    /// <summary>
    /// Checks if the document is active and valid
    /// </summary>
    /// <returns>True if document is active</returns>
    public bool IsActive()
    {
        return Status == DocumentStatus.Published &&
               (ExpirationDate == null || ExpirationDate > DateTime.UtcNow);
    }

    /// <summary>
    /// Checks if the document is expired
    /// </summary>
    /// <returns>True if document is expired</returns>
    public bool IsExpired()
    {
        return ExpirationDate.HasValue && ExpirationDate <= DateTime.UtcNow;
    }
}
