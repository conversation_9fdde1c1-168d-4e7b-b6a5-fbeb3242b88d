﻿namespace LexAI.DataPreprocessing.API;

using LexAI.DataPreprocessing.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using System.IO;

public class DesignTimeDataPreprocessingDbContextFactory : IDesignTimeDbContextFactory<DataPreprocessingDbContext>
{
    public DataPreprocessingDbContext CreateDbContext(string[] args)
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .Build();

        var optionsBuilder = new DbContextOptionsBuilder<DataPreprocessingDbContext>();
        optionsBuilder.UseNpgsql(configuration.GetConnectionString("PostgreSql"));

        return new DataPreprocessingDbContext(optionsBuilder.Options);
    }
}

