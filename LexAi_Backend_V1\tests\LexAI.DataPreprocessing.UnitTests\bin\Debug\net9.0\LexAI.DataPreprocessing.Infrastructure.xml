<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.DataPreprocessing.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent">
            <summary>
            Document classification agent implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.#ctor(LexAI.DataPreprocessing.Application.Interfaces.IClassificationService,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent})">
            <summary>
            Initializes a new instance of the ClassificationAgent
            </summary>
            <param name="classificationService">Classification service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.ClassifyDocumentAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Classifies a document into legal domains
            </summary>
            <param name="document">Document to classify</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Classification result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.GetDomainConfidenceAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets classification confidence for a document
            </summary>
            <param name="text">Document text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Domain confidence scores</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.ExtractKeywordsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts keywords from document text
            </summary>
            <param name="text">Document text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extracted keywords</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.ExtractNamedEntitiesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts named entities from document text
            </summary>
            <param name="text">Document text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Named entities</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent">
            <summary>
            Chunking agent implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.SupportedStrategies">
            <summary>
            Supported chunking strategies
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.#ctor(LexAI.DataPreprocessing.Application.Interfaces.IChunkingService,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent})">
            <summary>
            Initializes a new instance of the ChunkingAgent
            </summary>
            <param name="chunkingService">Chunking service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.ChunkDocumentAsync(LexAI.DataPreprocessing.Domain.Entities.Document,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration,System.Threading.CancellationToken)">
            <summary>
            Chunks a document into smaller pieces
            </summary>
            <param name="document">Document to chunk</param>
            <param name="configuration">Chunking configuration</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Chunking result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.EstimateChunkCount(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration)">
            <summary>
            Estimates the number of chunks for a document
            </summary>
            <param name="text">Document text</param>
            <param name="configuration">Chunking configuration</param>
            <returns>Estimated chunk count</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.ValidateConfiguration(LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration)">
            <summary>
            Validates chunking configuration
            </summary>
            <param name="configuration">Configuration to validate</param>
            <returns>Validation result</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent">
            <summary>
            Text extraction agent implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.SupportedMimeTypes">
            <summary>
            Supported MIME types
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.#ctor(LexAI.DataPreprocessing.Application.Interfaces.ITextExtractionService,LexAI.DataPreprocessing.Application.Interfaces.IFileStorageService,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent})">
            <summary>
            Initializes a new instance of the ExtractionAgent
            </summary>
            <param name="textExtractionService">Text extraction service</param>
            <param name="fileStorageService">File storage service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.ExtractTextAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Extracts text from a document
            </summary>
            <param name="document">Document to extract text from</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extraction result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.CanHandle(System.String)">
            <summary>
            Checks if the agent can handle the document
            </summary>
            <param name="mimeType">Document MIME type</param>
            <returns>True if agent can handle the document</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.GetExtractionConfidenceAsync(LexAI.DataPreprocessing.Domain.Entities.Document)">
            <summary>
            Gets extraction confidence for a document
            </summary>
            <param name="document">Document to evaluate</param>
            <returns>Confidence score (0-1)</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent">
            <summary>
            Orchestration agent that coordinates the entire document processing pipeline
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.#ctor(LexAI.DataPreprocessing.Application.Interfaces.IExtractionAgent,LexAI.DataPreprocessing.Application.Interfaces.IClassificationAgent,LexAI.DataPreprocessing.Application.Interfaces.IChunkingAgent,LexAI.DataPreprocessing.Application.Interfaces.IVectorizationAgent,LexAI.DataPreprocessing.Application.Interfaces.IRoutingAgent,LexAI.DataPreprocessing.Application.Interfaces.IQualityAssuranceAgent,LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository,LexAI.DataPreprocessing.Application.Interfaces.IVectorStorageService,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent})">
            <summary>
            Initializes a new instance of the OrchestrationAgent
            </summary>
            <param name="extractionAgent">Extraction agent</param>
            <param name="classificationAgent">Classification agent</param>
            <param name="chunkingAgent">Chunking agent</param>
            <param name="vectorizationAgent">Vectorization agent</param>
            <param name="routingAgent">Routing agent</param>
            <param name="qualityAssuranceAgent">Quality assurance agent</param>
            <param name="documentRepository">Document repository</param>
            <param name="vectorStorageService">Vector storage service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.ProcessDocumentAsync(LexAI.DataPreprocessing.Domain.Entities.Document,LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto,System.Threading.CancellationToken)">
            <summary>
            Orchestrates the complete document processing pipeline
            </summary>
            <param name="document">Document to process</param>
            <param name="configuration">Processing configuration</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processing result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.RetryProcessingAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.String,System.Threading.CancellationToken)">
            <summary>
            Retries failed processing steps
            </summary>
            <param name="document">Document to retry</param>
            <param name="fromStep">Step to retry from</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Retry result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.GetProcessingStatusAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets processing status for a document
            </summary>
            <param name="documentId">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processing status</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.CancelProcessingAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Cancels document processing
            </summary>
            <param name="documentId">Document ID</param>
            <param name="reason">Cancellation reason</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Cancellation result</returns>
        </member>
    </members>
</doc>
