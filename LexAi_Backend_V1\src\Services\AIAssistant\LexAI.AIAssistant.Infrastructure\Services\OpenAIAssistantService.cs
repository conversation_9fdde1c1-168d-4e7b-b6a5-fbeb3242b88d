using LexAI.AIAssistant.Application.DTOs;
using LexAI.AIAssistant.Application.Interfaces;
using LexAI.AIAssistant.Domain.ValueObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace LexAI.AIAssistant.Infrastructure.Services;

/// <summary>
/// OpenAI-based AI assistant service implementation
/// </summary>
public class OpenAIAssistantService : IAIAssistantService
{
    private readonly HttpClient _httpClient;
    private readonly ILegalResearchIntegrationService _legalResearchService;
    private readonly IMessageProcessingService _messageProcessingService;
    private readonly ILogger<OpenAIAssistantService> _logger;
    private readonly string _apiKey;
    private readonly string _model;

    /// <summary>
    /// Initializes a new instance of the OpenAIAssistantService
    /// </summary>
    /// <param name="httpClient">HTTP client</param>
    /// <param name="legalResearchService">Legal research service</param>
    /// <param name="messageProcessingService">Message processing service</param>
    /// <param name="configuration">Configuration</param>
    /// <param name="logger">Logger</param>
    public OpenAIAssistantService(
        HttpClient httpClient,
        ILegalResearchIntegrationService legalResearchService,
        IMessageProcessingService messageProcessingService,
        IConfiguration configuration,
        ILogger<OpenAIAssistantService> logger)
    {
        _httpClient = httpClient;
        _legalResearchService = legalResearchService;
        _messageProcessingService = messageProcessingService;
        _logger = logger;
        _apiKey = configuration["OpenAI:ApiKey"] ?? throw new InvalidOperationException("OpenAI API key not configured");
        _model = configuration["OpenAI:ChatModel"] ?? "gpt-4-turbo-preview";

        // Configure HTTP client
        _httpClient.BaseAddress = new Uri("https://api.openai.com/v1/");
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _apiKey);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "LexAI-Assistant/1.0");
    }

    /// <summary>
    /// Sends a message to the AI assistant and gets a response
    /// </summary>
    /// <param name="request">Chat request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>AI response</returns>
    public async Task<ChatResponseDto> SendMessageAsync(ChatRequestDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Processing chat request for user {UserId}: {Message}",
            request.UserId, request.Message);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            // Analyze message intent and domain
            var messageAnalysis = await _messageProcessingService.AnalyzeMessageAsync(request.Message, cancellationToken);

            // Perform legal research if needed
            var relatedDocuments = new List<LegalDocumentSummaryDto>();
            var citations = new List<CitationDto>();

            if (request.IncludeLegalResearch && messageAnalysis.RequiresLegalResearch)
            {
                _logger.LogDebug("Performing legal research for message: {Message}", request.Message);

                relatedDocuments = (await _legalResearchService.SearchLegalDocumentsAsync(
                    request.Message,
                    messageAnalysis.Domain,
                    5,
                    cancellationToken)).ToList();

                // Convert documents to citations
                citations = relatedDocuments.Select(doc => new CitationDto
                {
                    Id = Guid.NewGuid(),
                    Type = CitationType.LegalDocument,
                    Title = doc.Title,
                    Url = doc.Url ?? string.Empty,
                    Source = doc.Source,
                    PublicationDate = doc.PublicationDate,
                    RelevanceScore = doc.RelevanceScore,
                    Excerpt = doc.Summary
                }).ToList();
            }

            // Build context for AI
            var context = request.Context ?? new ConversationContextDto();
            var systemPrompt = BuildSystemPrompt(context, messageAnalysis, relatedDocuments);

            // Prepare OpenAI request
            var openAIRequest = new
            {
                model = GetModelName(context.ModelType),
                messages = new[]
                {
                    new { role = "system", content = systemPrompt },
                    new { role = "user", content = request.Message }
                },
                max_tokens = context.MaxTokens,
                temperature = context.Temperature,
                presence_penalty = 0.1,
                frequency_penalty = 0.1
            };

            var json = JsonSerializer.Serialize(openAIRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Call OpenAI API
            var response = await _httpClient.PostAsync("chat/completions", content, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
            var openAIResponse = JsonSerializer.Deserialize<OpenAIChatResponse>(responseJson);

            if (openAIResponse?.Choices == null || openAIResponse.Choices.Length == 0)
            {
                throw new InvalidOperationException("No response received from OpenAI");
            }

            var aiMessage = openAIResponse.Choices[0].Message.Content;
            var tokensUsed = openAIResponse.Usage?.TotalTokens ?? 0;
            var estimatedCost = CalculateCost(context.ModelType, tokensUsed);

            stopwatch.Stop();

            // Generate follow-up questions if conversation ID is provided
            var followUpQuestions = new List<string>();
            if (request.ConversationId.HasValue)
            {
                followUpQuestions = (await _messageProcessingService.GenerateFollowUpQuestionsAsync(
                    request.ConversationId.Value, cancellationToken)).ToList();
            }

            // Build response
            var chatResponse = new ChatResponseDto
            {
                ConversationId = request.ConversationId ?? Guid.NewGuid(),
                Response = aiMessage,
                MessageId = Guid.NewGuid(),
                ResponseType = DetermineResponseType(messageAnalysis.Intent),
                DetectedIntent = messageAnalysis.Intent,
                DetectedDomain = messageAnalysis.Domain,
                ConfidenceScore = messageAnalysis.ConfidenceScore,
                Citations = citations,
                RelatedDocuments = relatedDocuments,
                FollowUpQuestions = followUpQuestions,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds,
                TokensUsed = tokensUsed,
                EstimatedCost = estimatedCost,
                Quality = AssessResponseQuality(aiMessage, messageAnalysis),
                IsCached = false
            };

            _logger.LogInformation("Chat request processed successfully. Tokens: {TokensUsed}, Cost: {Cost}, Time: {Time}ms",
                tokensUsed, estimatedCost, stopwatch.ElapsedMilliseconds);

            return chatResponse;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error calling OpenAI API");
            throw new InvalidOperationException("Failed to get response from AI service", ex);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "JSON error parsing OpenAI response");
            throw new InvalidOperationException("Failed to parse AI service response", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error processing chat request");
            throw;
        }
    }

    /// <summary>
    /// Continues an existing conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="message">User message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>AI response</returns>
    public async Task<ChatResponseDto> ContinueConversationAsync(Guid conversationId, string message, CancellationToken cancellationToken = default)
    {
        var request = new ChatRequestDto
        {
            Message = message,
            ConversationId = conversationId,
            UserId = Guid.Empty, // Will be set by the caller
            SessionId = conversationId.ToString()
        };

        return await SendMessageAsync(request, cancellationToken);
    }

    /// <summary>
    /// Analyzes a legal document
    /// </summary>
    /// <param name="request">Document analysis request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document analysis response</returns>
    public async Task<DocumentAnalysisResponseDto> AnalyzeDocumentAsync(DocumentAnalysisRequestDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Analyzing document: {DocumentName}", request.DocumentName);

        var analysisPrompt = BuildDocumentAnalysisPrompt(request);

        var chatRequest = new ChatRequestDto
        {
            Message = analysisPrompt,
            UserId = request.UserId,
            SessionId = Guid.NewGuid().ToString(),
            Context = new ConversationContextDto
            {
                Mode = ConversationMode.DocumentAnalysis,
                MaxTokens = 8000,
                Temperature = 0.2,
                IncludeLegalResearch = true,
                IncludeCitations = true
            }
        };

        var response = await SendMessageAsync(chatRequest, cancellationToken);

        return new DocumentAnalysisResponseDto
        {
            DocumentName = request.DocumentName,
            Analysis = response.Response,
            KeyFindings = ExtractKeyFindings(response.Response),
            RiskAssessment = ExtractRiskAssessment(response.Response),
            Recommendations = ExtractRecommendations(response.Response),
            Citations = response.Citations,
            ProcessingTimeMs = response.ProcessingTimeMs,
            TokensUsed = response.TokensUsed,
            EstimatedCost = response.EstimatedCost
        };
    }

    /// <summary>
    /// Performs legal research and provides insights
    /// </summary>
    /// <param name="request">Research request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Research response</returns>
    public async Task<LegalResearchResponseDto> PerformLegalResearchAsync(LegalResearchRequestDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Performing legal research: {Query}", request.Query);

        // Search for relevant documents
        var documents = await _legalResearchService.SearchLegalDocumentsAsync(
            request.Query, request.Domain, 10, cancellationToken);

        var researchPrompt = BuildLegalResearchPrompt(request, documents);

        var chatRequest = new ChatRequestDto
        {
            Message = researchPrompt,
            UserId = request.UserId,
            SessionId = Guid.NewGuid().ToString(),
            Context = new ConversationContextDto
            {
                Mode = ConversationMode.Research,
                MaxTokens = 6000,
                Temperature = 0.3,
                IncludeLegalResearch = true,
                IncludeCitations = true
            }
        };

        var response = await SendMessageAsync(chatRequest, cancellationToken);

        return new LegalResearchResponseDto
        {
            Query = request.Query,
            Research = response.Response,
            KeyInsights = ExtractKeyInsights(response.Response),
            RelevantDocuments = documents.ToList(),
            Citations = response.Citations,
            ProcessingTimeMs = response.ProcessingTimeMs,
            TokensUsed = response.TokensUsed,
            EstimatedCost = response.EstimatedCost
        };
    }

    /// <summary>
    /// Generates a legal document based on user requirements
    /// </summary>
    /// <param name="request">Document generation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generated document</returns>
    public async Task<DocumentGenerationResponseDto> GenerateDocumentAsync(DocumentGenerationRequestDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating document: {DocumentType}", request.DocumentType);

        var generationPrompt = BuildDocumentGenerationPrompt(request);

        var chatRequest = new ChatRequestDto
        {
            Message = generationPrompt,
            UserId = request.UserId,
            SessionId = Guid.NewGuid().ToString(),
            Context = new ConversationContextDto
            {
                Mode = ConversationMode.Standard,
                MaxTokens = 8000,
                Temperature = 0.4,
                IncludeLegalResearch = true,
                IncludeCitations = false
            }
        };

        var response = await SendMessageAsync(chatRequest, cancellationToken);

        return new DocumentGenerationResponseDto
        {
            DocumentType = request.DocumentType,
            GeneratedContent = response.Response,
            Metadata = new Dictionary<string, object>
            {
                ["GeneratedAt"] = DateTime.UtcNow,
                ["TokensUsed"] = response.TokensUsed,
                ["EstimatedCost"] = response.EstimatedCost
            },
            ProcessingTimeMs = response.ProcessingTimeMs,
            TokensUsed = response.TokensUsed,
            EstimatedCost = response.EstimatedCost
        };
    }

    /// <summary>
    /// Summarizes a conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Conversation summary</returns>
    public async Task<ConversationSummaryDto> SummarizeConversationAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Summarizing conversation: {ConversationId}", conversationId);

        // This would typically get the conversation messages and summarize them
        // For now, return a placeholder
        return new ConversationSummaryDto
        {
            ConversationId = conversationId,
            Summary = "Conversation summary will be implemented with conversation history",
            KeyTopics = new List<string> { "Legal consultation", "Document review" },
            MainConclusions = new List<string> { "Further research needed" },
            GeneratedAt = DateTime.UtcNow
        };
    }

    private string BuildSystemPrompt(ConversationContextDto context, MessageAnalysisDto analysis, List<LegalDocumentSummaryDto> documents)
    {
        var prompt = new StringBuilder();

        prompt.AppendLine("Vous êtes LexAI, un assistant juridique IA spécialisé dans le droit français.");
        prompt.AppendLine("Vous fournissez des conseils juridiques précis basés sur la législation française actuelle.");
        prompt.AppendLine();

        if (context.Jurisdiction != null)
        {
            prompt.AppendLine($"Juridiction: {context.Jurisdiction}");
        }

        if (context.UserRole != null)
        {
            prompt.AppendLine($"Rôle de l'utilisateur: {context.UserRole}");
        }

        if (analysis.Domain.HasValue)
        {
            prompt.AppendLine($"Domaine juridique détecté: {analysis.Domain}");
        }

        if (documents.Any())
        {
            prompt.AppendLine();
            prompt.AppendLine("Documents juridiques pertinents:");
            foreach (var doc in documents.Take(3))
            {
                prompt.AppendLine($"- {doc.Title}: {doc.Summary}");
            }
        }

        prompt.AppendLine();
        prompt.AppendLine("Instructions:");
        prompt.AppendLine("- Répondez en français de manière claire et structurée");
        prompt.AppendLine("- Citez les sources juridiques pertinentes");
        prompt.AppendLine("- Indiquez les limitations de votre conseil");
        prompt.AppendLine("- Suggérez des actions concrètes si approprié");

        return prompt.ToString();
    }

    private static string GetModelName(AIModelType modelType)
    {
        return modelType switch
        {
            AIModelType.GPT4 => "gpt-4",
            AIModelType.GPT4Turbo => "gpt-4-turbo-preview",
            AIModelType.GPT35Turbo => "gpt-3.5-turbo",
            _ => "gpt-4-turbo-preview"
        };
    }

    private static MessageType DetermineResponseType(MessageIntent intent)
    {
        return intent switch
        {
            MessageIntent.Information => MessageType.Answer,
            MessageIntent.Advice => MessageType.Answer,
            MessageIntent.Research => MessageType.Answer,
            MessageIntent.DocumentAnalysis => MessageType.Answer,
            MessageIntent.General => MessageType.Clarification,
            _ => MessageType.Answer
        };
    }

    private static ResponseQuality AssessResponseQuality(string response, MessageAnalysisDto analysis)
    {
        // Simple quality assessment based on response length and complexity
        if (response.Length > 1000 && analysis.ComplexityScore > 0.7)
            return ResponseQuality.Excellent;
        if (response.Length > 500 && analysis.ComplexityScore > 0.5)
            return ResponseQuality.High;
        if (response.Length > 200)
            return ResponseQuality.Medium;
        return ResponseQuality.Low;
    }

    private static decimal CalculateCost(AIModelType modelType, int tokens)
    {
        // Simplified cost calculation (actual rates may vary)
        var costPerToken = modelType switch
        {
            AIModelType.GPT4 => 0.00003m,
            AIModelType.GPT4Turbo => 0.00001m,
            AIModelType.GPT35Turbo => 0.000002m,
            _ => 0.00001m
        };

        return tokens * costPerToken;
    }

    private static string BuildDocumentAnalysisPrompt(DocumentAnalysisRequestDto request)
    {
        return $"Analysez le document juridique suivant:\n\nNom: {request.DocumentName}\nContenu: {request.DocumentContent}\n\nFournissez une analyse détaillée incluant les points clés, les risques potentiels et vos recommandations.";
    }

    private static string BuildLegalResearchPrompt(LegalResearchRequestDto request, IEnumerable<LegalDocumentSummaryDto> documents)
    {
        var prompt = new StringBuilder();
        prompt.AppendLine($"Effectuez une recherche juridique sur: {request.Query}");

        if (documents.Any())
        {
            prompt.AppendLine("\nDocuments pertinents trouvés:");
            foreach (var doc in documents)
            {
                prompt.AppendLine($"- {doc.Title}: {doc.Summary}");
            }
        }

        prompt.AppendLine("\nFournissez une analyse complète avec les insights clés et les implications juridiques.");
        return prompt.ToString();
    }

    private static string BuildDocumentGenerationPrompt(DocumentGenerationRequestDto request)
    {
        return $"Générez un document juridique de type '{request.DocumentType}' avec les exigences suivantes:\n{request.Requirements}\n\nLe document doit être conforme au droit français et inclure toutes les clauses nécessaires.";
    }

    private static List<string> ExtractKeyFindings(string analysis)
    {
        // Simple extraction - in production, use more sophisticated NLP
        return new List<string> { "Analyse des points clés en cours d'implémentation" };
    }

    private static string ExtractRiskAssessment(string analysis)
    {
        return "Évaluation des risques en cours d'implémentation";
    }

    private static List<string> ExtractRecommendations(string analysis)
    {
        return new List<string> { "Recommandations en cours d'implémentation" };
    }

    private static List<string> ExtractKeyInsights(string research)
    {
        return new List<string> { "Insights clés en cours d'implémentation" };
    }

    /// <summary>
    /// OpenAI chat response model
    /// </summary>
    private class OpenAIChatResponse
    {
        public OpenAIChoice[] Choices { get; set; } = Array.Empty<OpenAIChoice>();
        public OpenAIUsage? Usage { get; set; }
    }

    /// <summary>
    /// OpenAI choice model
    /// </summary>
    private class OpenAIChoice
    {
        public OpenAIMessage Message { get; set; } = new();
    }

    /// <summary>
    /// OpenAI message model
    /// </summary>
    private class OpenAIMessage
    {
        public string Content { get; set; } = string.Empty;
    }

    /// <summary>
    /// OpenAI usage model
    /// </summary>
    private class OpenAIUsage
    {
        public int TotalTokens { get; set; }
    }
}
