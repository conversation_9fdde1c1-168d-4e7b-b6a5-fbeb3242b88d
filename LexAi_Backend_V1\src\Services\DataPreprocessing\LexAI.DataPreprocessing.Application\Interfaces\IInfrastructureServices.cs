using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;

namespace LexAI.DataPreprocessing.Application.Interfaces;

/// <summary>
/// Interface for document repository
/// </summary>
public interface IDocumentRepository
{
    /// <summary>
    /// Gets a document by ID
    /// </summary>
    /// <param name="id">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document</returns>
    Task<Document?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a document by file hash
    /// </summary>
    /// <param name="fileHash">File hash</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document</returns>
    Task<Document?> GetByFileHashAsync(string fileHash, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets documents by status
    /// </summary>
    /// <param name="status">Document status</param>
    /// <param name="limit">Maximum number of documents</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Documents</returns>
    Task<IEnumerable<Document>> GetByStatusAsync(DocumentStatus status, int limit = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets documents by user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="limit">Maximum number of documents</param>
    /// <param name="offset">Offset for pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Documents</returns>
    Task<IEnumerable<Document>> GetByUserAsync(Guid userId, int limit = 20, int offset = 0, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new document
    /// </summary>
    /// <param name="document">Document to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task AddAsync(Document document, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates a document
    /// </summary>
    /// <param name="document">Document to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task UpdateAsync(Document document, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a document
    /// </summary>
    /// <param name="id">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets processing statistics
    /// </summary>
    /// <param name="fromDate">From date</param>
    /// <param name="toDate">To date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing statistics</returns>
    Task<ProcessingStatistics> GetProcessingStatisticsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for file storage service
/// </summary>
public interface IFileStorageService
{
    /// <summary>
    /// Stores a file
    /// </summary>
    /// <param name="fileName">File name</param>
    /// <param name="fileContent">File content</param>
    /// <param name="mimeType">MIME type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Storage path</returns>
    Task<string> StoreFileAsync(string fileName, byte[] fileContent, string mimeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves a file
    /// </summary>
    /// <param name="storagePath">Storage path</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File content</returns>
    Task<byte[]> RetrieveFileAsync(string storagePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a file
    /// </summary>
    /// <param name="storagePath">Storage path</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task DeleteFileAsync(string storagePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a file exists
    /// </summary>
    /// <param name="storagePath">Storage path</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if file exists</returns>
    Task<bool> FileExistsAsync(string storagePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets file metadata
    /// </summary>
    /// <param name="storagePath">Storage path</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File metadata</returns>
    Task<FileMetadata> GetFileMetadataAsync(string storagePath, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for vector storage service
/// </summary>
public interface IVectorStorageService
{
    /// <summary>
    /// Supported database types
    /// </summary>
    IEnumerable<VectorDatabaseType> SupportedDatabases { get; }

    /// <summary>
    /// Stores vectors in the database
    /// </summary>
    /// <param name="chunks">Chunks with vectors to store</param>
    /// <param name="databaseType">Target database type</param>
    /// <param name="collection">Collection name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Storage result</returns>
    Task<VectorStorageResult> StoreVectorsAsync(
        IEnumerable<DocumentChunk> chunks,
        VectorDatabaseType databaseType,
        string collection,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches for similar vectors
    /// </summary>
    /// <param name="queryVector">Query vector</param>
    /// <param name="databaseType">Database type</param>
    /// <param name="collection">Collection name</param>
    /// <param name="limit">Maximum results</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Similar vectors</returns>
    Task<IEnumerable<VectorSearchResult>> SearchSimilarAsync(
        float[] queryVector,
        VectorDatabaseType databaseType,
        string collection,
        int limit = 10,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes vectors from the database
    /// </summary>
    /// <param name="vectorIds">Vector IDs to delete</param>
    /// <param name="databaseType">Database type</param>
    /// <param name="collection">Collection name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task DeleteVectorsAsync(
        IEnumerable<string> vectorIds,
        VectorDatabaseType databaseType,
        string collection,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a collection if it doesn't exist
    /// </summary>
    /// <param name="databaseType">Database type</param>
    /// <param name="collection">Collection name</param>
    /// <param name="dimension">Vector dimension</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task EnsureCollectionExistsAsync(
        VectorDatabaseType databaseType,
        string collection,
        int dimension,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for text extraction service
/// </summary>
public interface ITextExtractionService
{
    /// <summary>
    /// Supported MIME types
    /// </summary>
    IEnumerable<string> SupportedMimeTypes { get; }

    /// <summary>
    /// Extracts text from a file
    /// </summary>
    /// <param name="fileContent">File content</param>
    /// <param name="mimeType">MIME type</param>
    /// <param name="fileName">File name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extracted text and metadata</returns>
    Task<TextExtractionResult> ExtractTextAsync(
        byte[] fileContent,
        string mimeType,
        string fileName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if the service can handle the MIME type
    /// </summary>
    /// <param name="mimeType">MIME type</param>
    /// <returns>True if supported</returns>
    bool CanHandle(string mimeType);
}

/// <summary>
/// Interface for embedding service
/// </summary>
public interface IEmbeddingService
{
    /// <summary>
    /// Supported embedding models
    /// </summary>
    IEnumerable<EmbeddingModelType> SupportedModels { get; }

    /// <summary>
    /// Generates embeddings for texts
    /// </summary>
    /// <param name="texts">Texts to embed</param>
    /// <param name="modelType">Embedding model</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Embeddings</returns>
    Task<IEnumerable<EmbeddingResult>> GenerateEmbeddingsAsync(
        IEnumerable<string> texts,
        EmbeddingModelType modelType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the dimension of an embedding model
    /// </summary>
    /// <param name="modelType">Embedding model</param>
    /// <returns>Vector dimension</returns>
    int GetEmbeddingDimension(EmbeddingModelType modelType);

    /// <summary>
    /// Estimates the cost of generating embeddings
    /// </summary>
    /// <param name="tokenCount">Number of tokens</param>
    /// <param name="modelType">Embedding model</param>
    /// <returns>Estimated cost</returns>
    decimal EstimateCost(int tokenCount, EmbeddingModelType modelType);
}

/// <summary>
/// Interface for chunking service
/// </summary>
public interface IChunkingService
{
    /// <summary>
    /// Supported chunking strategies
    /// </summary>
    IEnumerable<ChunkingStrategy> SupportedStrategies { get; }

    /// <summary>
    /// Chunks text into smaller pieces
    /// </summary>
    /// <param name="text">Text to chunk</param>
    /// <param name="configuration">Chunking configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Text chunks</returns>
    Task<IEnumerable<TextChunk>> ChunkTextAsync(
        string text,
        ChunkingConfiguration configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Estimates the number of chunks
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="configuration">Chunking configuration</param>
    /// <returns>Estimated chunk count</returns>
    int EstimateChunkCount(string text, ChunkingConfiguration configuration);
}

/// <summary>
/// Interface for classification service
/// </summary>
public interface IClassificationService
{
    /// <summary>
    /// Classifies text into legal domains
    /// </summary>
    /// <param name="text">Text to classify</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Classification result</returns>
    Task<ClassificationResult> ClassifyTextAsync(string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// Extracts keywords from text
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="maxKeywords">Maximum keywords to extract</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extracted keywords</returns>
    Task<IEnumerable<string>> ExtractKeywordsAsync(string text, int maxKeywords = 20, CancellationToken cancellationToken = default);

    /// <summary>
    /// Extracts named entities from text
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Named entities</returns>
    Task<IEnumerable<Domain.ValueObjects.NamedEntity>> ExtractNamedEntitiesAsync(string text, CancellationToken cancellationToken = default);
}

/// <summary>
/// File metadata
/// </summary>
public class FileMetadata
{
    /// <summary>
    /// File size in bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// MIME type
    /// </summary>
    public string MimeType { get; set; } = string.Empty;

    /// <summary>
    /// Last modified date
    /// </summary>
    public DateTime LastModified { get; set; }

    /// <summary>
    /// File hash
    /// </summary>
    public string Hash { get; set; } = string.Empty;
}

/// <summary>
/// Vector storage result
/// </summary>
public class VectorStorageResult
{
    /// <summary>
    /// Storage success
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Number of vectors stored
    /// </summary>
    public int VectorsStored { get; set; }

    /// <summary>
    /// Storage time
    /// </summary>
    public TimeSpan StorageTime { get; set; }

    /// <summary>
    /// Vector IDs assigned
    /// </summary>
    public List<string> VectorIds { get; set; } = new();

    /// <summary>
    /// Storage errors
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Vector search result
/// </summary>
public class VectorSearchResult
{
    /// <summary>
    /// Vector ID
    /// </summary>
    public string VectorId { get; set; } = string.Empty;

    /// <summary>
    /// Similarity score
    /// </summary>
    public double Score { get; set; }

    /// <summary>
    /// Associated metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Text extraction result
/// </summary>
public class TextExtractionResult
{
    /// <summary>
    /// Extracted text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Extraction success
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Document metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Extraction errors
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Embedding result
/// </summary>
public class EmbeddingResult
{
    /// <summary>
    /// Original text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Embedding vector
    /// </summary>
    public float[] Vector { get; set; } = Array.Empty<float>();

    /// <summary>
    /// Token count
    /// </summary>
    public int TokenCount { get; set; }

    /// <summary>
    /// Embedding success
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Text chunk
/// </summary>
public class TextChunk
{
    /// <summary>
    /// Chunk text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Start position in original text
    /// </summary>
    public int StartPosition { get; set; }

    /// <summary>
    /// End position in original text
    /// </summary>
    public int EndPosition { get; set; }

    /// <summary>
    /// Chunk type
    /// </summary>
    public ChunkType Type { get; set; }

    /// <summary>
    /// Chunk metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Processing statistics
/// </summary>
public class ProcessingStatistics
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// File name
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// File size
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// Processing status
    /// </summary>
    public DocumentStatus Status { get; set; }

    /// <summary>
    /// Number of chunks created
    /// </summary>
    public int ChunkCount { get; set; }

    /// <summary>
    /// Total tokens processed
    /// </summary>
    public int TotalTokens { get; set; }

    /// <summary>
    /// Processing time
    /// </summary>
    public TimeSpan ProcessingTime { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// Number of processing steps
    /// </summary>
    public int StepCount { get; set; }

    /// <summary>
    /// Number of errors
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// Whether document is vectorized
    /// </summary>
    public bool IsVectorized { get; set; }
}

/// <summary>
/// Classification result
/// </summary>
public class ClassificationResult
{
    /// <summary>
    /// Detected domain
    /// </summary>
    public LegalDomain DetectedDomain { get; set; }

    /// <summary>
    /// Classification confidence
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// All domain scores
    /// </summary>
    public Dictionary<LegalDomain, double> DomainScores { get; set; } = new();

    /// <summary>
    /// Classification success
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }
}
