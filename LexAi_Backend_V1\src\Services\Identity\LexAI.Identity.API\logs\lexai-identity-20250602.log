2025-06-02 12:51:28.959 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-02 12:51:29.095 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-02 12:51:29.285 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-02 12:51:30.016 +04:00 [INF] Executed DbCommand (59ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-02 12:51:30.034 +04:00 [INF] LexAI Identity Service started successfully
2025-06-02 12:51:30.087 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 12:51:30.347 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-02 12:51:30.349 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-02 12:51:30.424 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 12:51:30.427 +04:00 [INF] Hosting environment: Development
2025-06-02 12:51:30.430 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-02 12:51:32.230 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-02 12:51:32.502 +04:00 [INF] Request GET / started with correlation ID f0739182-657c-4a35-a4ce-03aec548a52f
2025-06-02 12:51:34.145 +04:00 [INF] Request GET / completed in 1636ms with status 404 (Correlation ID: f0739182-657c-4a35-a4ce-03aec548a52f)
2025-06-02 12:51:34.162 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 1955.4963ms
2025-06-02 12:51:34.171 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-02 12:56:13.063 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-02 12:56:13.126 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID cca1bf34-be36-4ebf-a2b1-77e22cdf9619
2025-06-02 12:56:13.180 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-02 12:56:13.190 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:13.215 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 1ba2e420-ab4b-465a-9f6b-a4b691b4d7e7
2025-06-02 12:56:13.219 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 34ms with status 204 (Correlation ID: cca1bf34-be36-4ebf-a2b1-77e22cdf9619)
2025-06-02 12:56:13.225 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:13.234 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 170.7043ms
2025-06-02 12:56:13.237 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 13ms with status 204 (Correlation ID: 1ba2e420-ab4b-465a-9f6b-a4b691b4d7e7)
2025-06-02 12:56:13.240 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 12:56:13.250 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 69.2704ms
2025-06-02 12:56:13.253 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 12:56:13.255 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID bcb361db-3651-4034-933d-cbf61123fa0c
2025-06-02 12:56:13.261 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID d70cfbac-d783-4ac0-b169-dd62ed7850b7
2025-06-02 12:56:13.263 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:13.266 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:13.271 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:56:13.273 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:56:13.337 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 12:56:13.338 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 12:56:13.477 +04:00 [INF] Token refresh attempt
2025-06-02 12:56:13.477 +04:00 [INF] Token refresh attempt
2025-06-02 12:56:13.629 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 12:56:13.629 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 12:56:15.229 +04:00 [INF] Executed DbCommand (84ms) [Parameters=[@__token_0='hadvgM12LTb0eC/VSk91etPoJUA4Lu1jZevwa8GP8e+cEtF5NhaoOS4My1/3Il9TwJ7qbPx56iyIjfmWC+n5jA=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 12:56:15.229 +04:00 [INF] Executed DbCommand (64ms) [Parameters=[@__token_0='hadvgM12LTb0eC/VSk91etPoJUA4Lu1jZevwa8GP8e+cEtF5NhaoOS4My1/3Il9TwJ7qbPx56iyIjfmWC+n5jA=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 12:56:15.910 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 12:56:15.995 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__id_0='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-02 12:56:15.995 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__id_0='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-02 12:56:16.039 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='hadvgM12LTb0eC/VSk91etPoJUA4Lu1jZevwa8GP8e+cEtF5NhaoOS4My1/3Il9TwJ7qbPx56iyIjfmWC+n5jA=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 12:56:16.040 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='hadvgM12LTb0eC/VSk91etPoJUA4Lu1jZevwa8GP8e+cEtF5NhaoOS4My1/3Il9TwJ7qbPx56iyIjfmWC+n5jA=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 12:56:16.474 +04:00 [INF] Executed DbCommand (30ms) [Parameters=[@p16='c8bf94c6-327d-4fab-942d-28f40987d3c6', @p0='2025-06-01T17:33:51.9166970Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-08T17:33:51.8521880Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-02T08:56:16.0464816Z' (Nullable = true) (DbType = DateTime), @p10='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p11='hadvgM12LTb0eC/VSk91etPoJUA4Lu1jZevwa8GP8e+cEtF5NhaoOS4My1/3Il9TwJ7qbPx56iyIjfmWC+n5jA==' (Nullable = false), @p12='2025-06-02T08:56:16.1840805Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p17='794' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-02 12:56:16.475 +04:00 [INF] Executed DbCommand (32ms) [Parameters=[@p16='c8bf94c6-327d-4fab-942d-28f40987d3c6', @p0='2025-06-01T17:33:51.9166970Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-08T17:33:51.8521880Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-02T08:56:16.0505090Z' (Nullable = true) (DbType = DateTime), @p10='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p11='hadvgM12LTb0eC/VSk91etPoJUA4Lu1jZevwa8GP8e+cEtF5NhaoOS4My1/3Il9TwJ7qbPx56iyIjfmWC+n5jA==' (Nullable = false), @p12='2025-06-02T08:56:16.1841449Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p17='794' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-02 12:56:17.603 +04:00 [ERR] Error during token refresh for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 316
   at LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.UpdateAsync(RefreshToken refreshToken, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\RefreshTokenRepository.cs:line 87
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 248
2025-06-02 12:56:17.614 +04:00 [INF] Refresh token updated successfully: "c8bf94c6-327d-4fab-942d-28f40987d3c6"
2025-06-02 12:56:17.988 +04:00 [WRN] Token refresh failed
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 316
   at LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.UpdateAsync(RefreshToken refreshToken, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\RefreshTokenRepository.cs:line 87
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 248
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 12:56:18.026 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 12:56:18.082 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 4723.639ms
2025-06-02 12:56:18.086 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:56:18.091 +04:00 [INF] Request POST /api/auth/refresh completed in 4827ms with status 401 (Correlation ID: bcb361db-3651-4034-933d-cbf61123fa0c)
2025-06-02 12:56:18.101 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - null null
2025-06-02 12:56:18.103 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 401 null application/json; charset=utf-8 4862.8898ms
2025-06-02 12:56:18.109 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID ab7aefe3-5d7b-4dcf-be1d-b17e32acd2ea
2025-06-02 12:56:18.125 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:18.128 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 2ms with status 204 (Correlation ID: ab7aefe3-5d7b-4dcf-be1d-b17e32acd2ea)
2025-06-02 12:56:18.138 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - 204 null null 36.2953ms
2025-06-02 12:56:18.142 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/logout - application/json 0
2025-06-02 12:56:18.157 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 0a5326e3-88df-4f17-9325-c5dcdc151150
2025-06-02 12:56:18.161 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:18.197 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='d0d4bad4-a2ad-49d7-b741-27c1f9604388', @p1='2025-06-02T08:56:18.1790681Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-09T08:56:18.1366504Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='p+Z4bjpNG+fysaj00hBtKg3aKesQo6sOH4X57SUjVtWP8PIePXEV0pykM8w2iCfKWCM1VEtD6erHbixC6vReDA==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-02 12:56:18.204 +04:00 [INF] Refresh token added successfully: "d0d4bad4-a2ad-49d7-b741-27c1f9604388"
2025-06-02 12:56:18.206 +04:00 [INF] Token refreshed successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 12:56:18.213 +04:00 [INF] Token refresh successful
2025-06-02 12:56:18.216 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-02 12:56:18.250 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:18 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 12:56:18.259 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:18 AM'.
2025-06-02 12:56:18.263 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:18 AM'.
2025-06-02 12:56:18.272 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 4913.8722ms
2025-06-02 12:56:18.275 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:56:18.277 +04:00 [INF] Request POST /api/auth/refresh completed in 5011ms with status 200 (Correlation ID: d70cfbac-d783-4ac0-b169-dd62ed7850b7)
2025-06-02 12:56:18.284 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 12:56:18.285 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 499 null application/json; charset=utf-8 5032.2611ms
2025-06-02 12:56:18.308 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 12:56:18.312 +04:00 [INF] Request POST /api/auth/logout completed in 151ms with status 401 (Correlation ID: 0a5326e3-88df-4f17-9325-c5dcdc151150)
2025-06-02 12:56:18.314 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/logout - 499 null null 172.6519ms
2025-06-02 12:56:42.010 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-02 12:56:42.018 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 125446f6-b7d2-4a27-87ed-93e7e20ba3e7
2025-06-02 12:56:42.022 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:42.024 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 2ms with status 204 (Correlation ID: 125446f6-b7d2-4a27-87ed-93e7e20ba3e7)
2025-06-02 12:56:42.032 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 22.3274ms
2025-06-02 12:56:42.036 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 12:56:42.054 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 52226397-8a5b-4ee5-b4c1-33b774785624
2025-06-02 12:56:42.059 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:42.062 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:56:42.066 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 12:56:42.067 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 12:56:42.074 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 22f78df4-b1ab-42a5-873f-95e8ce8277b2
2025-06-02 12:56:42.081 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:42.081 +04:00 [INF] Token refresh attempt
2025-06-02 12:56:42.083 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:56:42.086 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 12:56:42.092 +04:00 [INF] Token refresh attempt
2025-06-02 12:56:42.264 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 12:56:42.264 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 12:56:42.272 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='hadvgM12LTb0eC/VSk91etPoJUA4Lu1jZevwa8GP8e+cEtF5NhaoOS4My1/3Il9TwJ7qbPx56iyIjfmWC+n5jA=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 12:56:42.280 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-02 12:56:42.515 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 12:56:42.521 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 12:56:42.523 +04:00 [INF] Executed DbCommand (80ms) [Parameters=[@__token_0='hadvgM12LTb0eC/VSk91etPoJUA4Lu1jZevwa8GP8e+cEtF5NhaoOS4My1/3Il9TwJ7qbPx56iyIjfmWC+n5jA=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 12:56:42.524 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 433.0088ms
2025-06-02 12:56:42.623 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-02 12:56:42.630 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:56:42.831 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 12:56:42.836 +04:00 [INF] Request POST /api/auth/refresh completed in 754ms with status 401 (Correlation ID: 22f78df4-b1ab-42a5-873f-95e8ce8277b2)
2025-06-02 12:56:42.844 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 12:56:42.865 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 401 null application/json; charset=utf-8 798.951ms
2025-06-02 12:56:42.872 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 794.513ms
2025-06-02 12:56:42.873 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - null null
2025-06-02 12:56:42.905 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:56:42.914 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID ad5437a3-8d2c-4a11-9e51-eef6bb492bb1
2025-06-02 12:56:42.916 +04:00 [INF] Request POST /api/auth/refresh completed in 857ms with status 401 (Correlation ID: 52226397-8a5b-4ee5-b4c1-33b774785624)
2025-06-02 12:56:42.919 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:42.926 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 401 null application/json; charset=utf-8 890.2479ms
2025-06-02 12:56:42.931 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 12ms with status 204 (Correlation ID: ad5437a3-8d2c-4a11-9e51-eef6bb492bb1)
2025-06-02 12:56:42.939 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - 204 null null 65.6996ms
2025-06-02 12:56:42.943 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/logout - application/json 0
2025-06-02 12:56:42.949 +04:00 [INF] Request POST /api/auth/logout started with correlation ID e78c7ccb-28d8-439e-a88e-590c35b5317f
2025-06-02 12:56:42.952 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:42.955 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:42 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 12:56:42.964 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:42 AM'.
2025-06-02 12:56:42.965 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:42 AM'.
2025-06-02 12:56:42.968 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 12:56:42.970 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 12:56:42.972 +04:00 [INF] Request POST /api/auth/logout completed in 20ms with status 401 (Correlation ID: e78c7ccb-28d8-439e-a88e-590c35b5317f)
2025-06-02 12:56:42.978 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/logout - 401 0 null 35.1495ms
2025-06-02 12:56:43.017 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 12:56:43.025 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 9cab8e24-2add-490b-b172-0c1c7cb58849
2025-06-02 12:56:43.029 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:43.031 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:56:43.033 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 12:56:43.037 +04:00 [INF] Token refresh attempt
2025-06-02 12:56:43.040 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 12:56:43.052 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='hadvgM12LTb0eC/VSk91etPoJUA4Lu1jZevwa8GP8e+cEtF5NhaoOS4My1/3Il9TwJ7qbPx56iyIjfmWC+n5jA=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 12:56:43.057 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-02 12:56:43.184 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 12:56:43.191 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 12:56:43.197 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 160.7794ms
2025-06-02 12:56:43.201 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:56:43.204 +04:00 [INF] Request POST /api/auth/refresh completed in 174ms with status 401 (Correlation ID: 9cab8e24-2add-490b-b172-0c1c7cb58849)
2025-06-02 12:56:43.210 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 499 null application/json; charset=utf-8 193.0775ms
2025-06-02 12:57:25.356 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - null null
2025-06-02 12:57:25.383 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID af5d2a8c-5fb6-4ad2-9b15-df791aa8c197
2025-06-02 12:57:25.394 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:57:25.398 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 3ms with status 204 (Correlation ID: af5d2a8c-5fb6-4ad2-9b15-df791aa8c197)
2025-06-02 12:57:25.407 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - 204 null null 50.47ms
2025-06-02 12:57:25.412 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/logout - application/json 0
2025-06-02 12:57:25.433 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 5d01cd8d-f5b3-4d6a-b339-77e9129c9697
2025-06-02 12:57:25.435 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:57:25.439 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:57:25 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 12:57:25.445 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:57:25 AM'.
2025-06-02 12:57:25.446 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:57:25 AM'.
2025-06-02 12:57:25.450 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 12:57:25.453 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 12:57:25.455 +04:00 [INF] Request POST /api/auth/logout completed in 19ms with status 401 (Correlation ID: 5d01cd8d-f5b3-4d6a-b339-77e9129c9697)
2025-06-02 12:57:25.459 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/logout - 401 0 null 47.3779ms
2025-06-02 12:57:25.469 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-02 12:57:25.473 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID b93f2bd4-627b-4065-a8e0-7e9b68028bc4
2025-06-02 12:57:25.477 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:57:25.479 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 1ms with status 204 (Correlation ID: b93f2bd4-627b-4065-a8e0-7e9b68028bc4)
2025-06-02 12:57:25.483 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 13.8623ms
2025-06-02 12:57:25.488 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 12:57:25.493 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 3085f273-f555-49dd-b5a7-10d2225e1bde
2025-06-02 12:57:25.495 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:57:25.496 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:57:25.497 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 12:57:25.500 +04:00 [INF] Token refresh attempt
2025-06-02 12:57:25.503 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 12:57:25.508 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__token_0='hadvgM12LTb0eC/VSk91etPoJUA4Lu1jZevwa8GP8e+cEtF5NhaoOS4My1/3Il9TwJ7qbPx56iyIjfmWC+n5jA=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 12:57:25.515 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-02 12:57:25.666 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 12:57:25.671 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 12:57:25.679 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 178.7047ms
2025-06-02 12:57:25.682 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 12:57:25.683 +04:00 [INF] Request POST /api/auth/refresh completed in 188ms with status 401 (Correlation ID: 3085f273-f555-49dd-b5a7-10d2225e1bde)
2025-06-02 12:57:25.689 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 401 null application/json; charset=utf-8 201.7279ms
2025-06-02 12:57:25.696 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/logout - application/json 0
2025-06-02 12:57:25.723 +04:00 [INF] Request POST /api/auth/logout started with correlation ID bf25465f-6748-45d9-95cd-275d4bc6bb95
2025-06-02 12:57:25.730 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:57:25.735 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:57:25 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 12:57:25.741 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:57:25 AM'.
2025-06-02 12:57:25.744 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:57:25 AM'.
2025-06-02 12:57:25.750 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 12:57:25.752 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 12:57:25.756 +04:00 [INF] Request POST /api/auth/logout completed in 26ms with status 401 (Correlation ID: bf25465f-6748-45d9-95cd-275d4bc6bb95)
2025-06-02 12:57:25.796 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/logout - 401 0 null 100.3221ms
2025-06-02 12:57:56.333 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-02 12:57:56.365 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID e0892421-34f2-4baf-8507-782e7fa63ce0
2025-06-02 12:57:56.375 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:57:56.380 +04:00 [INF] Request OPTIONS /api/auth/login completed in 4ms with status 204 (Correlation ID: e0892421-34f2-4baf-8507-782e7fa63ce0)
2025-06-02 12:57:56.387 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 53.4656ms
2025-06-02 12:57:56.394 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-02 12:57:56.420 +04:00 [INF] Request POST /api/auth/login started with correlation ID c0912cdb-454b-4168-9863-81bccb0a6a24
2025-06-02 12:57:56.428 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:57:56.433 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-02 12:57:56.454 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 12:57:56.473 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-02 12:57:56.500 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-02 12:57:56.542 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 12:57:56.597 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-02 12:57:57.444 +04:00 [INF] Executed DbCommand (14ms) [Parameters=[@p0='c58d59d7-46a3-48d2-8855-7431eadf9418', @p1='Login' (Nullable = false), @p2='"Successful login from ::1"' (DbType = Object), @p3='2025-06-02T08:57:57.3182029Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-02T08:57:57.3187717Z' (DbType = DateTime), @p12='2025-06-02T08:57:57.3741860Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5' (Nullable = false), @p37='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p16='2025-06-01T17:33:42.5262650Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='0', @p21='Kevin' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-02T08:57:57.3169895Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Jules' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$eWMQSH57bq.BO5srOIQ1AutHq3dday.oDdj4wcF798QSqVq1WX93S' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='SeniorLawyer' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-02T08:57:57.3741848Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='793' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-02 12:57:57.476 +04:00 [INF] User updated successfully: "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 12:57:57.490 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='cff06395-f63b-4d11-b224-c5a357905155', @p1='2025-06-02T08:57:57.4852712Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-09T08:57:57.4833561Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-02 12:57:57.496 +04:00 [INF] Refresh token added successfully: "cff06395-f63b-4d11-b224-c5a357905155"
2025-06-02 12:57:57.497 +04:00 [INF] User "62e0a975-363b-47ac-abf8-68fcf260cbb5" logged in successfully
2025-06-02 12:57:57.499 +04:00 [INF] Login successful <NAME_EMAIL>
2025-06-02 12:57:57.500 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-02 12:57:57.502 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 1041.8076ms
2025-06-02 12:57:57.506 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-02 12:57:57.508 +04:00 [INF] Request POST /api/auth/login completed in 1079ms with status 200 (Correlation ID: c0912cdb-454b-4168-9863-81bccb0a6a24)
2025-06-02 12:57:57.512 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 200 null application/json; charset=utf-8 1117.6318ms
2025-06-02 13:52:29.865 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-02 13:52:29.906 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-02 13:52:29.913 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-02 13:52:30.496 +04:00 [INF] Executed DbCommand (65ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-02 13:52:30.511 +04:00 [INF] LexAI Identity Service started successfully
2025-06-02 13:52:30.557 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 13:52:30.845 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-02 13:52:30.848 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-02 13:52:31.037 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 13:52:31.040 +04:00 [INF] Hosting environment: Development
2025-06-02 13:52:31.042 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-02 13:52:31.919 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-02 13:52:32.050 +04:00 [INF] Request GET / started with correlation ID 8c87fe78-549a-4275-bb36-9a59ec1fe8b3
2025-06-02 13:52:33.549 +04:00 [INF] Request GET / completed in 1494ms with status 404 (Correlation ID: 8c87fe78-549a-4275-bb36-9a59ec1fe8b3)
2025-06-02 13:52:33.571 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 1673.3035ms
2025-06-02 13:52:33.582 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-02 14:03:58.739 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-02 14:03:58.780 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 0fb9f021-f686-4696-9bb2-e5bae8e416f9
2025-06-02 14:03:58.825 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-02 14:03:58.828 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:03:58.831 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID ea587cab-a5c3-4774-b8d2-3566d2ead5ab
2025-06-02 14:03:58.835 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 9ms with status 204 (Correlation ID: 0fb9f021-f686-4696-9bb2-e5bae8e416f9)
2025-06-02 14:03:58.836 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:03:58.840 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 101.4481ms
2025-06-02 14:03:58.842 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 6ms with status 204 (Correlation ID: ea587cab-a5c3-4774-b8d2-3566d2ead5ab)
2025-06-02 14:03:58.845 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 14:03:58.853 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 28.0859ms
2025-06-02 14:03:58.857 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 14:03:58.861 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 86e4848c-bab7-49c7-942e-0d4a1d2c7b07
2025-06-02 14:03:58.868 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID a83515ab-163d-4bdf-a196-ccb767db100c
2025-06-02 14:03:58.871 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:03:58.874 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:03:58.882 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:03:58.883 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:03:58.928 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 14:03:58.928 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 14:03:58.998 +04:00 [INF] Token refresh attempt
2025-06-02 14:03:58.998 +04:00 [INF] Token refresh attempt
2025-06-02 14:03:59.074 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 14:03:59.075 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 14:04:00.036 +04:00 [INF] Executed DbCommand (45ms) [Parameters=[@__token_0='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 14:04:00.036 +04:00 [INF] Executed DbCommand (45ms) [Parameters=[@__token_0='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 14:04:00.411 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 14:04:00.474 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@__id_0='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-02 14:04:00.475 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@__id_0='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-02 14:04:00.515 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 14:04:00.515 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 14:04:00.799 +04:00 [INF] Executed DbCommand (15ms) [Parameters=[@p16='cff06395-f63b-4d11-b224-c5a357905155', @p0='2025-06-02T08:57:57.4852710Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-09T08:57:57.4833560Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-02T10:04:00.5219246Z' (Nullable = true) (DbType = DateTime), @p10='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p11='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw==' (Nullable = false), @p12='2025-06-02T10:04:00.6448003Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p17='799' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-02 14:04:00.800 +04:00 [INF] Executed DbCommand (16ms) [Parameters=[@p16='cff06395-f63b-4d11-b224-c5a357905155', @p0='2025-06-02T08:57:57.4852710Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-09T08:57:57.4833560Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-02T10:04:00.5256769Z' (Nullable = true) (DbType = DateTime), @p10='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p11='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw==' (Nullable = false), @p12='2025-06-02T10:04:00.6442569Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p17='799' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-02 14:04:01.421 +04:00 [ERR] Error during token refresh for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 316
   at LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.UpdateAsync(RefreshToken refreshToken, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\RefreshTokenRepository.cs:line 87
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 248
2025-06-02 14:04:01.423 +04:00 [INF] Refresh token updated successfully: "cff06395-f63b-4d11-b224-c5a357905155"
2025-06-02 14:04:01.715 +04:00 [WRN] Token refresh failed
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 316
   at LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.UpdateAsync(RefreshToken refreshToken, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\RefreshTokenRepository.cs:line 87
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 248
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 14:04:01.755 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 14:04:01.806 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 2854.0181ms
2025-06-02 14:04:01.810 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:01.812 +04:00 [INF] Request POST /api/auth/refresh completed in 2941ms with status 401 (Correlation ID: 86e4848c-bab7-49c7-942e-0d4a1d2c7b07)
2025-06-02 14:04:01.825 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - null null
2025-06-02 14:04:01.826 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 401 null application/json; charset=utf-8 2981.2674ms
2025-06-02 14:04:01.830 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID eb0a89bd-d461-42bc-982d-78460d1afc50
2025-06-02 14:04:01.837 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:01.839 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 1ms with status 204 (Correlation ID: eb0a89bd-d461-42bc-982d-78460d1afc50)
2025-06-02 14:04:01.842 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - 204 null null 17.8293ms
2025-06-02 14:04:01.845 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/logout - application/json 0
2025-06-02 14:04:01.851 +04:00 [INF] Request POST /api/auth/logout started with correlation ID d0e683e4-5ee9-47b0-a4ef-d36be5739f34
2025-06-02 14:04:01.855 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:01.935 +04:00 [INF] Executed DbCommand (31ms) [Parameters=[@p0='ab3e84ce-f734-421e-8a2a-6a6d1638b1e9', @p1='2025-06-02T10:04:01.8935996Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-09T10:04:01.8512678Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='AvtUP+6u9UMUho1CL2Etu3d6oCN/einv8I5nl5zOM57fzwAYO6yaJIYZe1NeKDD9rl9ZFpcIwvyY7Hk8UApfEw==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-02 14:04:01.946 +04:00 [INF] Refresh token added successfully: "ab3e84ce-f734-421e-8a2a-6a6d1638b1e9"
2025-06-02 14:04:01.949 +04:00 [INF] Token refreshed successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 14:04:01.964 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:01 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 14:04:01.964 +04:00 [INF] Token refresh successful
2025-06-02 14:04:01.971 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-02 14:04:01.971 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:01 AM'.
2025-06-02 14:04:01.998 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:01 AM'.
2025-06-02 14:04:02.021 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 14:04:02.060 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 14:04:02.063 +04:00 [INF] Request POST /api/auth/logout completed in 208ms with status 401 (Correlation ID: d0e683e4-5ee9-47b0-a4ef-d36be5739f34)
2025-06-02 14:04:02.074 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/logout - 499 null null 229.286ms
2025-06-02 14:04:02.080 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 3146.3086ms
2025-06-02 14:04:02.086 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:02.091 +04:00 [INF] Request POST /api/auth/refresh completed in 3217ms with status 200 (Correlation ID: a83515ab-163d-4bdf-a196-ccb767db100c)
2025-06-02 14:04:02.104 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 499 null application/json; charset=utf-8 3246.6621ms
2025-06-02 14:04:05.125 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-02 14:04:05.141 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 77f313d3-d7c1-48ce-9c3c-094dc9a971c2
2025-06-02 14:04:05.144 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:05.145 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 1ms with status 204 (Correlation ID: 77f313d3-d7c1-48ce-9c3c-094dc9a971c2)
2025-06-02 14:04:05.151 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 26.1964ms
2025-06-02 14:04:05.158 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 14:04:05.164 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 597adb3e-c3c9-485d-a88a-843a91310f2f
2025-06-02 14:04:05.172 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:05.175 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:05.180 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 14:04:05.181 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 14:04:05.186 +04:00 [INF] Token refresh attempt
2025-06-02 14:04:05.187 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID e26cf0e2-ee96-4249-982d-377874145d6d
2025-06-02 14:04:05.193 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:05.195 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:05.197 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 14:04:05.202 +04:00 [INF] Token refresh attempt
2025-06-02 14:04:05.306 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 14:04:05.306 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 14:04:05.313 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 14:04:05.313 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 14:04:05.342 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-02 14:04:05.484 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-02 14:04:05.559 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 14:04:05.719 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 14:04:05.728 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 14:04:05.736 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 14:04:05.741 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 539.7656ms
2025-06-02 14:04:05.775 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:05.765 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 581.7955ms
2025-06-02 14:04:05.885 +04:00 [INF] Request POST /api/auth/refresh completed in 691ms with status 401 (Correlation ID: e26cf0e2-ee96-4249-982d-377874145d6d)
2025-06-02 14:04:05.970 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:05.976 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 401 null application/json; charset=utf-8 795.4178ms
2025-06-02 14:04:05.977 +04:00 [INF] Request POST /api/auth/refresh completed in 805ms with status 401 (Correlation ID: 597adb3e-c3c9-485d-a88a-843a91310f2f)
2025-06-02 14:04:05.984 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/logout - application/json 0
2025-06-02 14:04:05.990 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 401 null application/json; charset=utf-8 832.7747ms
2025-06-02 14:04:05.995 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 13e00ac2-5eb1-48de-91f8-15aca0813e59
2025-06-02 14:04:06.006 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:06.009 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:06 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 14:04:06.015 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:06 AM'.
2025-06-02 14:04:06.016 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:06 AM'.
2025-06-02 14:04:06.021 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 14:04:06.029 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 14:04:06.031 +04:00 [INF] Request POST /api/auth/logout completed in 25ms with status 401 (Correlation ID: 13e00ac2-5eb1-48de-91f8-15aca0813e59)
2025-06-02 14:04:06.036 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/logout - 401 0 null 51.0388ms
2025-06-02 14:04:06.172 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 14:04:06.444 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 5c03bd0d-f2f7-4da2-8266-ff8d399bb51c
2025-06-02 14:04:06.446 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:06.448 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:06.451 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 14:04:06.466 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 10.415ms
2025-06-02 14:04:06.471 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:20.137 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 499 null null 13964.722ms
2025-06-02 14:04:26.216 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-02 14:04:26.221 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID fae4125b-8459-436a-83f6-2618969afe35
2025-06-02 14:04:26.223 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:26.224 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 1ms with status 204 (Correlation ID: fae4125b-8459-436a-83f6-2618969afe35)
2025-06-02 14:04:26.229 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 12.4231ms
2025-06-02 14:04:26.232 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 14:04:26.239 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID e67026e1-33c8-49fe-b5cf-f8fe4319ef98
2025-06-02 14:04:26.241 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:26.243 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:26.245 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 14:04:26.249 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 14:04:26.250 +04:00 [INF] Token refresh attempt
2025-06-02 14:04:26.256 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID f95a1bad-818b-429a-8e07-5bd85b02c967
2025-06-02 14:04:26.257 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 14:04:26.260 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:26.264 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:26.266 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 14:04:26.270 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 14:04:26.270 +04:00 [INF] Token refresh attempt
2025-06-02 14:04:26.276 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-02 14:04:26.276 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 14:04:26.414 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 14:04:26.420 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 14:04:26.424 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 175.8849ms
2025-06-02 14:04:26.425 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__token_0='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 14:04:26.438 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:26.448 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-02 14:04:26.448 +04:00 [INF] Request POST /api/auth/refresh completed in 206ms with status 401 (Correlation ID: e67026e1-33c8-49fe-b5cf-f8fe4319ef98)
2025-06-02 14:04:26.568 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 14:04:26.570 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 401 null application/json; charset=utf-8 338.6364ms
2025-06-02 14:04:26.574 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 14:04:26.575 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - null null
2025-06-02 14:04:26.583 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 313.7475ms
2025-06-02 14:04:26.589 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:26.587 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID 81d8c85d-f977-4395-8c44-99eba44e2e1a
2025-06-02 14:04:26.592 +04:00 [INF] Request POST /api/auth/refresh completed in 332ms with status 401 (Correlation ID: f95a1bad-818b-429a-8e07-5bd85b02c967)
2025-06-02 14:04:26.597 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:26.604 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 401 null application/json; charset=utf-8 355.0485ms
2025-06-02 14:04:26.604 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 7ms with status 204 (Correlation ID: 81d8c85d-f977-4395-8c44-99eba44e2e1a)
2025-06-02 14:04:26.613 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - 204 null null 37.857ms
2025-06-02 14:04:26.617 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/logout - application/json 0
2025-06-02 14:04:26.624 +04:00 [INF] Request POST /api/auth/logout started with correlation ID bd6ef57e-9f94-4855-8250-94850b2aa55c
2025-06-02 14:04:26.627 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:26.630 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:26 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 14:04:26.636 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:26 AM'.
2025-06-02 14:04:26.639 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:26 AM'.
2025-06-02 14:04:26.643 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 14:04:26.647 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 14:04:26.649 +04:00 [INF] Request POST /api/auth/logout completed in 22ms with status 401 (Correlation ID: bd6ef57e-9f94-4855-8250-94850b2aa55c)
2025-06-02 14:04:26.652 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/logout - 401 0 null 35.2799ms
2025-06-02 14:04:26.752 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 14:04:26.758 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID e9a6fdfb-65ae-48a2-a324-28a63b7ff7ec
2025-06-02 14:04:26.761 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:26.763 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:26.765 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 14:04:26.768 +04:00 [INF] Token refresh attempt
2025-06-02 14:04:26.771 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 14:04:26.785 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__token_0='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 14:04:26.792 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-02 14:04:26.908 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 14:04:26.912 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 14:04:26.915 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 147.8438ms
2025-06-02 14:04:26.918 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:26.919 +04:00 [INF] Request POST /api/auth/refresh completed in 158ms with status 401 (Correlation ID: e9a6fdfb-65ae-48a2-a324-28a63b7ff7ec)
2025-06-02 14:04:26.923 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 499 null application/json; charset=utf-8 171.8108ms
2025-06-02 14:04:33.011 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - null null
2025-06-02 14:04:33.015 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID 5342256f-4b34-4ba8-b297-4d3c4a021f70
2025-06-02 14:04:33.017 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:33.019 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 1ms with status 204 (Correlation ID: 5342256f-4b34-4ba8-b297-4d3c4a021f70)
2025-06-02 14:04:33.023 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - 204 null null 12.2716ms
2025-06-02 14:04:33.026 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/logout - application/json 0
2025-06-02 14:04:33.032 +04:00 [INF] Request POST /api/auth/logout started with correlation ID bc24c12d-2c38-43f9-a4e7-8b8350a1143f
2025-06-02 14:04:33.034 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:33.037 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:33 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 14:04:33.040 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:33 AM'.
2025-06-02 14:04:33.042 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:33 AM'.
2025-06-02 14:04:33.044 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 14:04:33.046 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 14:04:33.047 +04:00 [INF] Request POST /api/auth/logout completed in 12ms with status 401 (Correlation ID: bc24c12d-2c38-43f9-a4e7-8b8350a1143f)
2025-06-02 14:04:33.050 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/logout - 401 0 null 24.5305ms
2025-06-02 14:04:33.088 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-02 14:04:33.093 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 0f9237e5-68f3-497e-8938-0f9778965859
2025-06-02 14:04:33.095 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:33.097 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 2ms with status 204 (Correlation ID: 0f9237e5-68f3-497e-8938-0f9778965859)
2025-06-02 14:04:33.101 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 12.6416ms
2025-06-02 14:04:33.103 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-02 14:04:33.111 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 83249cf9-b438-4e0e-abed-a34d66ed7c21
2025-06-02 14:04:33.113 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:33.115 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:33.117 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 14:04:33.120 +04:00 [INF] Token refresh attempt
2025-06-02 14:04:33.123 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-02 14:04:33.149 +04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__token_0='2bUtBcoJtYhekh0pyV6smD1PE1izQCpxy64jlx8DLRxUZNA4MMJhHc2MxgUzc8of2+I1qdsigdB45wl2+psJiw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-02 14:04:33.156 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-02 14:04:33.262 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-02 14:04:33.266 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 14:04:33.270 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 150.3591ms
2025-06-02 14:04:33.273 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-02 14:04:33.275 +04:00 [INF] Request POST /api/auth/refresh completed in 161ms with status 401 (Correlation ID: 83249cf9-b438-4e0e-abed-a34d66ed7c21)
2025-06-02 14:04:33.279 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 401 null application/json; charset=utf-8 176.0385ms
2025-06-02 14:04:33.284 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/logout - application/json 0
2025-06-02 14:04:33.290 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 9d505ff7-e7b2-441e-8f54-b03c3942cc5f
2025-06-02 14:04:33.294 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:33.296 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:33 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 14:04:33.300 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:33 AM'.
2025-06-02 14:04:33.301 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:33 AM'.
2025-06-02 14:04:33.303 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 14:04:33.305 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 14:04:33.307 +04:00 [INF] Request POST /api/auth/logout completed in 13ms with status 401 (Correlation ID: 9d505ff7-e7b2-441e-8f54-b03c3942cc5f)
2025-06-02 14:04:33.311 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/logout - 401 0 null 26.7594ms
2025-06-02 14:05:09.636 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-02 14:05:09.654 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 9625f6af-a66e-4242-a3bb-de136bd86d65
2025-06-02 14:05:09.667 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:05:09.673 +04:00 [INF] Request OPTIONS /api/auth/login completed in 6ms with status 204 (Correlation ID: 9625f6af-a66e-4242-a3bb-de136bd86d65)
2025-06-02 14:05:09.690 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 54.4281ms
2025-06-02 14:05:09.698 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-02 14:05:09.732 +04:00 [INF] Request POST /api/auth/login started with correlation ID 72c16846-a599-41ac-8489-73815a764767
2025-06-02 14:05:09.734 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:05:09.736 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-02 14:05:09.793 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 14:05:09.810 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-02 14:05:09.823 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-02 14:05:09.866 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 14:05:09.930 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-02 14:05:09.936 +04:00 [WRN] Login attempt with non-<NAME_EMAIL>
2025-06-02 14:05:10.112 +04:00 [WRN] Login failed <NAME_EMAIL>
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid email or password
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 83
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-02 14:05:10.119 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 14:05:10.127 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 326.7329ms
2025-06-02 14:05:10.133 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-02 14:05:10.138 +04:00 [INF] Request POST /api/auth/login completed in 403ms with status 401 (Correlation ID: 72c16846-a599-41ac-8489-73815a764767)
2025-06-02 14:05:10.153 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 455.3268ms
2025-06-02 14:05:23.994 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-02 14:05:24.007 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 4ec0b2ff-ad92-4b4c-a5a6-a7c20a89b7d7
2025-06-02 14:05:24.017 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:05:24.022 +04:00 [INF] Request OPTIONS /api/auth/login completed in 4ms with status 204 (Correlation ID: 4ec0b2ff-ad92-4b4c-a5a6-a7c20a89b7d7)
2025-06-02 14:05:24.084 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 90.4967ms
2025-06-02 14:05:24.092 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-02 14:05:24.119 +04:00 [INF] Request POST /api/auth/login started with correlation ID a61ec23d-0bfd-43a7-bd9e-9ce735dcf947
2025-06-02 14:05:24.123 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:05:24.126 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-02 14:05:24.129 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 14:05:24.135 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-02 14:05:24.140 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-02 14:05:24.150 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-02 14:05:24.897 +04:00 [INF] Executed DbCommand (15ms) [Parameters=[@p0='063e1fa5-606d-4300-b7e6-3388e71e3fe6', @p1='Login' (Nullable = false), @p2='"Successful login from ::1"' (DbType = Object), @p3='2025-06-02T10:05:24.7730360Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-02T10:05:24.7736367Z' (DbType = DateTime), @p12='2025-06-02T10:05:24.8264407Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5' (Nullable = false), @p37='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p16='2025-06-01T17:33:42.5262650Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='0', @p21='Kevin' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-02T10:05:24.7718778Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Jules' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$eWMQSH57bq.BO5srOIQ1AutHq3dday.oDdj4wcF798QSqVq1WX93S' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='SeniorLawyer' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-02T10:05:24.8264363Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='798' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-02 14:05:24.932 +04:00 [INF] User updated successfully: "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 14:05:24.941 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='ad9674aa-18fc-43ea-a561-2b9a40af3d0d', @p1='2025-06-02T10:05:24.9370113Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-09T10:05:24.9360778Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='WBaDA/53Hcsl6Ori8W+XZH9UfxBbZADtFQ6GfHONJTsAG/fYDYj+Ddu2B+aQTOrNvwVajFE8851FrPFppNcBbA==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-02 14:05:24.945 +04:00 [INF] Refresh token added successfully: "ad9674aa-18fc-43ea-a561-2b9a40af3d0d"
2025-06-02 14:05:24.946 +04:00 [INF] User "62e0a975-363b-47ac-abf8-68fcf260cbb5" logged in successfully
2025-06-02 14:05:24.947 +04:00 [INF] Login successful <NAME_EMAIL>
2025-06-02 14:05:24.949 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-02 14:05:24.951 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 817.2928ms
2025-06-02 14:05:24.954 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-02 14:05:24.956 +04:00 [INF] Request POST /api/auth/login completed in 833ms with status 200 (Correlation ID: a61ec23d-0bfd-43a7-bd9e-9ce735dcf947)
2025-06-02 14:05:24.960 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 200 null application/json; charset=utf-8 868.6481ms
2025-06-02 15:07:36.503 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-02 15:07:36.545 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-02 15:07:36.552 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-02 15:07:37.142 +04:00 [INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-02 15:07:37.176 +04:00 [INF] LexAI Identity Service started successfully
2025-06-02 15:07:37.225 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 15:07:37.978 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-02 15:07:37.981 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-02 15:07:38.096 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 15:07:38.099 +04:00 [INF] Hosting environment: Development
2025-06-02 15:07:38.103 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-02 15:07:38.708 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-02 15:07:38.960 +04:00 [INF] Request GET / started with correlation ID 4e939a4e-5651-4027-82bd-14dca32ecce9
2025-06-02 15:07:39.276 +04:00 [INF] Request GET / completed in 311ms with status 404 (Correlation ID: 4e939a4e-5651-4027-82bd-14dca32ecce9)
2025-06-02 15:07:39.293 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 596.7296ms
2025-06-02 15:07:39.306 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-02 15:09:05.859 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-02 15:09:05.953 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 1486e29e-d2d8-48dd-97ee-fea58eb28da0
2025-06-02 15:09:05.963 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:09:05.968 +04:00 [INF] Request OPTIONS /api/auth/login completed in 7ms with status 204 (Correlation ID: 1486e29e-d2d8-48dd-97ee-fea58eb28da0)
2025-06-02 15:09:05.973 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 115.1004ms
2025-06-02 15:09:05.977 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-02 15:09:05.984 +04:00 [INF] Request POST /api/auth/login started with correlation ID e3060959-e0ea-40af-b0dd-68b0c36ffab5
2025-06-02 15:09:05.988 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:09:06.008 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-02 15:09:06.050 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-02 15:09:06.124 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-02 15:09:06.228 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-02 15:09:06.567 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 15:09:06.652 +04:00 [INF] Executed DbCommand (29ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-02 15:09:07.127 +04:00 [INF] Executed DbCommand (17ms) [Parameters=[@p0='f72e6c86-daa0-43d7-b861-c9dfc032d848', @p1='Login' (Nullable = false), @p2='"Successful login from ::1"' (DbType = Object), @p3='2025-06-02T11:09:06.9740606Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-02T11:09:06.9741480Z' (DbType = DateTime), @p12='2025-06-02T11:09:07.0202407Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5' (Nullable = false), @p37='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p16='2025-06-01T17:33:42.5262650Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='0', @p21='Kevin' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-02T11:09:06.9737019Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Jules' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$eWMQSH57bq.BO5srOIQ1AutHq3dday.oDdj4wcF798QSqVq1WX93S' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='SeniorLawyer' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-02T11:09:07.0201919Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='803' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-02 15:09:07.173 +04:00 [INF] User updated successfully: "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 15:09:07.226 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='abfb7de6-2c9f-479b-8c91-26cdc4944eef', @p1='2025-06-02T11:09:07.2186061Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-09T11:09:07.2146746Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='zQb0OloDichnxNr6i7xcCIkweGr+ABvRR7f7m9iSc3A+0hd2BlU52EiPfdTU4Yg2I4wBnGcBviKsltxiP6uLOA==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-02 15:09:07.230 +04:00 [INF] Refresh token added successfully: "abfb7de6-2c9f-479b-8c91-26cdc4944eef"
2025-06-02 15:09:07.232 +04:00 [INF] User "62e0a975-363b-47ac-abf8-68fcf260cbb5" logged in successfully
2025-06-02 15:09:07.235 +04:00 [INF] Login successful <NAME_EMAIL>
2025-06-02 15:09:07.240 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-02 15:09:07.258 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 1200.67ms
2025-06-02 15:09:07.261 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-02 15:09:07.265 +04:00 [INF] Request POST /api/auth/login completed in 1278ms with status 200 (Correlation ID: e3060959-e0ea-40af-b0dd-68b0c36ffab5)
2025-06-02 15:09:07.275 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 200 null application/json; charset=utf-8 1298.8906ms
