<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.AIAssistant.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService">
            <summary>
            OpenAI-based AI assistant service implementation
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.#ctor(System.Net.Http.HttpClient,LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService,LexAI.AIAssistant.Application.Interfaces.IMessageProcessingService,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILogger{LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService})">
            <summary>
            Initializes a new instance of the OpenAIAssistantService
            </summary>
            <param name="httpClient">HTTP client</param>
            <param name="legalResearchService">Legal research service</param>
            <param name="messageProcessingService">Message processing service</param>
            <param name="configuration">Configuration</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.SendMessageAsync(LexAI.AIAssistant.Application.DTOs.ChatRequestDto,System.Threading.CancellationToken)">
            <summary>
            Sends a message to the AI assistant and gets a response
            </summary>
            <param name="request">Chat request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>AI response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.ContinueConversationAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Continues an existing conversation
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="message">User message</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>AI response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.AnalyzeDocumentAsync(LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto,System.Threading.CancellationToken)">
            <summary>
            Analyzes a legal document
            </summary>
            <param name="request">Document analysis request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document analysis response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.PerformLegalResearchAsync(LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto,System.Threading.CancellationToken)">
            <summary>
            Performs legal research and provides insights
            </summary>
            <param name="request">Research request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Research response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.GenerateDocumentAsync(LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto,System.Threading.CancellationToken)">
            <summary>
            Generates a legal document based on user requirements
            </summary>
            <param name="request">Document generation request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Generated document</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.SummarizeConversationAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Summarizes a conversation
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Conversation summary</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.OpenAIChatResponse">
            <summary>
            OpenAI chat response model
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.OpenAIChoice">
            <summary>
            OpenAI choice model
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.OpenAIMessage">
            <summary>
            OpenAI message model
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.OpenAIUsage">
            <summary>
            OpenAI usage model
            </summary>
        </member>
    </members>
</doc>
