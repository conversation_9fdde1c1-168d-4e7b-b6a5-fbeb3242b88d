using LexAI.DataPreprocessing.Application.Commands;
using LexAI.DataPreprocessing.Application.DTOs;
using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using System.Security.Claims;

namespace LexAI.DataPreprocessing.API.Controllers;

/// <summary>
/// Controller for document processing operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
[EnableRateLimiting("DocumentPolicy")]
public class DocumentsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IOrchestrationAgent _orchestrationAgent;
    private readonly IDocumentRepository _documentRepository;
    private readonly ILogger<DocumentsController> _logger;

    /// <summary>
    /// Initializes a new instance of the DocumentsController
    /// </summary>
    /// <param name="mediator">MediatR mediator</param>
    /// <param name="orchestrationAgent">Orchestration agent</param>
    /// <param name="documentRepository">Document repository</param>
    /// <param name="logger">Logger</param>
    public DocumentsController(
        IMediator mediator,
        IOrchestrationAgent orchestrationAgent,
        IDocumentRepository documentRepository,
        ILogger<DocumentsController> logger)
    {
        _mediator = mediator;
        _orchestrationAgent = orchestrationAgent;
        _documentRepository = documentRepository;
        _logger = logger;
    }

    /// <summary>
    /// Uploads a document for processing
    /// </summary>
    /// <param name="file">Document file</param>
    /// <param name="metadata">Optional metadata as JSON string</param>
    /// <returns>Upload response</returns>
    /// <response code="200">Document uploaded successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="401">User not authenticated</response>
    /// <response code="413">File too large</response>
    /// <response code="429">Rate limit exceeded</response>
    [HttpPost("upload")]
    [Consumes("multipart/form-data")]
    [ProducesResponseType(typeof(DocumentUploadResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status413PayloadTooLarge)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status429TooManyRequests)]
    [RequestSizeLimit(50 * 1024 * 1024)] // 50MB limit
    public async Task<ActionResult<DocumentUploadResponseDto>> UploadDocument([FromForm] IFormFile file, [FromForm] string? metadata = null)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (file == null || file.Length == 0)
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "File is required" });
        }

        if (file.Length > 50 * 1024 * 1024) // 50MB
        {
            return StatusCode(413, new ProblemDetails
            {
                Title = "File Too Large",
                Detail = "File size exceeds 50MB limit"
            });
        }

        _logger.LogInformation("Document upload request from user {UserId}: {FileName}", userId.Value, file.FileName);

        try
        {
            // Read file content
            using var memoryStream = new MemoryStream();
            await file.CopyToAsync(memoryStream);
            var fileBytes = memoryStream.ToArray();
            var fileContent = Convert.ToBase64String(fileBytes);

            // Parse metadata if provided
            var metadataDict = new Dictionary<string, object>();
            ProcessingConfigurationDto? configuration = null;

            if (!string.IsNullOrWhiteSpace(metadata))
            {
                try
                {
                    var metadataJson = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(metadata) ?? new();

                    // Extract processing configuration if present
                    if (metadataJson.TryGetValue("configuration", out var configValue))
                    {
                        var configJson = configValue.ToString();
                        if (!string.IsNullOrWhiteSpace(configJson))
                        {
                            // Commenter pour test
                            //configuration = System.Text.Json.JsonSerializer.Deserialize<ProcessingConfigurationDto>(configJson);
                        }
                        metadataJson.Remove("configuration"); // Remove from metadata to avoid duplication
                    }

                    metadataDict = metadataJson;
                }
                catch (System.Text.Json.JsonException ex)
                {
                    _logger.LogWarning(ex, "Invalid metadata JSON from user {UserId}", userId.Value);
                    return BadRequest(new ProblemDetails { Title = "Invalid Metadata", Detail = "Metadata must be valid JSON" });
                }
            }

            // Use default configuration if none provided
            //if (configuration == null) // Commenter pour test
            //{
            configuration = new ProcessingConfigurationDto
                {
                    Chunking = new ChunkingConfigurationDto
                    {
                        Strategy = ChunkingStrategy.Semantic,
                        MaxChunkSize = 1000,
                        OverlapSize = 200,
                        MinChunkSize = 100,
                        PreserveSentences = true,
                        PreserveParagraphs = true
                    },
                    EmbeddingModel = EmbeddingModelType.OpenAISmall,
                    TargetDatabases = new List<VectorDatabaseType> { VectorDatabaseType.MongoDB },
                    PerformQualityAssurance = true,
                    ExtractNamedEntities = true,
                    ExtractKeywords = true,
                    Priority = ProcessingPriority.High,
                    CustomOptions = new Dictionary<string, object>()
                };

                _logger.LogInformation("Using default processing configuration for user {UserId}", userId.Value);
            //}

            // Create request DTO
            var request = new DocumentUploadRequestDto
            {
                FileName = file.FileName,
                FileContent = fileContent,
                MimeType = file.ContentType,
                UserId = userId.Value,
                Configuration = configuration,
                Metadata = metadataDict
            };

            // Execute upload command
            var command = new UploadDocumentCommand { Request = request };
            var response = await _mediator.Send(command);

            _logger.LogInformation("Document uploaded successfully for user {UserId}. " +
                "Document: {DocumentId}, Processing started: {ProcessingStarted}",
                userId.Value, response.DocumentId, response.ProcessingStarted);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid upload request from user {UserId}", userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document for user {UserId}", userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Upload Error", Detail = "An error occurred while uploading the document" });
        }
    }

    /// <summary>
    /// Processes a document through the pipeline
    /// </summary>
    /// <param name="documentId">Document ID to process</param>
    /// <param name="configuration">Processing configuration</param>
    /// <returns>Processing result</returns>
    /// <response code="200">Document processed successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="404">Document not found</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("{documentId:guid}/process")]
    [ProducesResponseType(typeof(ProcessingResultDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ProcessingResultDto>> ProcessDocument(
        Guid documentId,
        [FromBody] ProcessingConfigurationDto configuration)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        _logger.LogInformation("Document processing request from user {UserId}: {DocumentId}", userId.Value, documentId);

        try
        {
            var command = new ProcessDocumentCommand
            {
                DocumentId = documentId,
                Configuration = configuration,
                UserId = userId.Value
            };

            var response = await _mediator.Send(command);

            _logger.LogInformation("Document processing completed for user {UserId}. " +
                "Document: {DocumentId}, Success: {Success}",
                userId.Value, documentId, response.Success);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid processing request from user {UserId}", userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation for document {DocumentId} by user {UserId}", documentId, userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Operation", Detail = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document {DocumentId} for user {UserId}", documentId, userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Processing Error", Detail = "An error occurred while processing the document" });
        }
    }

    /// <summary>
    /// Retries failed document processing
    /// </summary>
    /// <param name="documentId">Document ID to retry</param>
    /// <param name="request">Retry request</param>
    /// <returns>Processing result</returns>
    /// <response code="200">Document retry completed</response>
    /// <response code="400">Invalid request</response>
    /// <response code="404">Document not found</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("{documentId:guid}/retry")]
    [ProducesResponseType(typeof(ProcessingResultDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ProcessingResultDto>> RetryDocumentProcessing(
        Guid documentId,
        [FromBody] RetryProcessingRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        _logger.LogInformation("Document retry request from user {UserId}: {DocumentId}, Step: {FromStep}",
            userId.Value, documentId, request.FromStep);

        try
        {
            var command = new RetryDocumentProcessingCommand
            {
                DocumentId = documentId,
                FromStep = request.FromStep,
                UserId = userId.Value
            };

            var response = await _mediator.Send(command);

            _logger.LogInformation("Document retry completed for user {UserId}. " +
                "Document: {DocumentId}, Success: {Success}",
                userId.Value, documentId, response.Success);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid retry request from user {UserId}", userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid retry operation for document {DocumentId} by user {UserId}", documentId, userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Operation", Detail = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrying document {DocumentId} for user {UserId}", documentId, userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Retry Error", Detail = "An error occurred while retrying document processing" });
        }
    }

    /// <summary>
    /// Gets processing status for a document
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <returns>Processing status</returns>
    /// <response code="200">Status retrieved successfully</response>
    /// <response code="404">Document not found</response>
    /// <response code="401">User not authenticated</response>
    [HttpGet("{documentId:guid}/status")]
    [ProducesResponseType(typeof(ProcessingStatusDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ProcessingStatusDto>> GetProcessingStatus(Guid documentId)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        try
        {
            var status = await _orchestrationAgent.GetProcessingStatusAsync(documentId);

            _logger.LogDebug("Processing status retrieved for document {DocumentId} by user {UserId}",
                documentId, userId.Value);

            return Ok(status);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Document {DocumentId} not found for user {UserId}", documentId, userId.Value);
            return NotFound(new ProblemDetails { Title = "Document Not Found", Detail = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting status for document {DocumentId} by user {UserId}", documentId, userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Status Error", Detail = "An error occurred while retrieving document status" });
        }
    }

    /// <summary>
    /// Cancels document processing
    /// </summary>
    /// <param name="documentId">Document ID to cancel</param>
    /// <param name="request">Cancellation request</param>
    /// <returns>Cancellation result</returns>
    /// <response code="200">Processing cancelled successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="404">Document not found</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("{documentId:guid}/cancel")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult> CancelDocumentProcessing(
        Guid documentId,
        [FromBody] CancelProcessingRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        _logger.LogInformation("Document cancellation request from user {UserId}: {DocumentId}, Reason: {Reason}",
            userId.Value, documentId, request.Reason);

        try
        {
            var command = new CancelDocumentProcessingCommand
            {
                DocumentId = documentId,
                Reason = request.Reason,
                UserId = userId.Value
            };

            var result = await _mediator.Send(command);

            if (result)
            {
                _logger.LogInformation("Document processing cancelled for {DocumentId} by user {UserId}",
                    documentId, userId.Value);
                return Ok(new { message = "Document processing cancelled successfully" });
            }
            else
            {
                return BadRequest(new ProblemDetails { Title = "Cancellation Failed", Detail = "Unable to cancel document processing" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling document {DocumentId} for user {UserId}", documentId, userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Cancellation Error", Detail = "An error occurred while cancelling document processing" });
        }
    }

    /// <summary>
    /// Gets a specific document by ID
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <returns>Document details</returns>
    /// <response code="200">Document retrieved successfully</response>
    /// <response code="404">Document not found</response>
    /// <response code="401">User not authenticated</response>
    [HttpGet("{documentId:guid}")]
    [ProducesResponseType(typeof(DocumentSummaryDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<DocumentSummaryDto>> GetDocument(Guid documentId)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        // Validate documentId is not empty
        if (documentId == Guid.Empty)
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Document ID cannot be empty" });
        }

        try
        {
            var document = await _documentRepository.GetByIdAsync(documentId);

            if (document == null)
            {
                _logger.LogWarning("Document {DocumentId} not found for user {UserId}", documentId, userId.Value);
                return NotFound(new ProblemDetails { Title = "Document Not Found", Detail = "The requested document was not found" });
            }

            // Check if user owns the document
            if (document.CreatedBy != userId.Value.ToString())
            {
                _logger.LogWarning("User {UserId} attempted to access document {DocumentId} owned by {OwnerId}",
                    userId.Value, documentId, document.CreatedBy);
                return NotFound(new ProblemDetails { Title = "Document Not Found", Detail = "The requested document was not found" });
            }

            var documentSummary = MapToDocumentSummary(document);

            _logger.LogDebug("Document {DocumentId} retrieved for user {UserId}", documentId, userId.Value);

            return Ok(documentSummary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving document {DocumentId} for user {UserId}", documentId, userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Retrieval Error", Detail = "An error occurred while retrieving the document" });
        }
    }

    /// <summary>
    /// Gets user's documents
    /// </summary>
    /// <param name="limit">Maximum number of documents</param>
    /// <param name="offset">Offset for pagination</param>
    /// <returns>User documents</returns>
    /// <response code="200">Documents retrieved successfully</response>
    /// <response code="401">User not authenticated</response>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<DocumentSummaryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<IEnumerable<DocumentSummaryDto>>> GetUserDocuments(
        [FromQuery] int limit = 20,
        [FromQuery] int offset = 0)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        try
        {
            var documents = await _documentRepository.GetByUserAsync(userId.Value, limit, offset);
            var documentSummaries = documents.Select(MapToDocumentSummary);

            _logger.LogDebug("Retrieved {DocumentCount} documents for user {UserId}",
                documentSummaries.Count(), userId.Value);

            return Ok(documentSummaries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving documents for user {UserId}", userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Retrieval Error", Detail = "An error occurred while retrieving documents" });
        }
    }

    private Guid? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    private static DocumentSummaryDto MapToDocumentSummary(Domain.Entities.Document document)
    {
        return new DocumentSummaryDto
        {
            Id = document.Id,
            FileName = document.FileName,
            FileSize = document.FileSize,
            MimeType = document.MimeType,
            Status = document.Status,
            CreatedBy = document.CreatedBy,
            DetectedDomain = document.DetectedDomain,
            ClassificationConfidence = document.ClassificationConfidence,
            ChunkCount = document.ChunkCount,
            TotalTokens = document.TotalTokens,
            EstimatedCost = document.EstimatedCost,
            IsVectorized = document.IsVectorized,
            VectorDatabase = document.VectorDatabase,
            ProcessingTime = document.ProcessingTime,
            CreatedAt = document.CreatedAt,
            UpdatedAt = document.UpdatedAt
        };
    }
}

/// <summary>
/// Retry processing request DTO
/// </summary>
public class RetryProcessingRequestDto
{
    /// <summary>
    /// Step to retry from
    /// </summary>
    public string FromStep { get; set; } = string.Empty;
}

/// <summary>
/// Cancel processing request DTO
/// </summary>
public class CancelProcessingRequestDto
{
    /// <summary>
    /// Cancellation reason
    /// </summary>
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// Document summary DTO
/// </summary>
public class DocumentSummaryDto
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// File name
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// File size
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// MIME type
    /// </summary>
    public string MimeType { get; set; } = string.Empty;

    /// <summary>
    /// Processing status
    /// </summary>
    public Domain.ValueObjects.DocumentStatus Status { get; set; }

    /// <summary>
    /// User who created the document
    /// </summary>
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// Detected legal domain
    /// </summary>
    public Domain.ValueObjects.LegalDomain? DetectedDomain { get; set; }

    /// <summary>
    /// Classification confidence
    /// </summary>
    public double? ClassificationConfidence { get; set; }

    /// <summary>
    /// Number of chunks
    /// </summary>
    public int ChunkCount { get; set; }

    /// <summary>
    /// Total tokens
    /// </summary>
    public int TotalTokens { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// Whether vectorized
    /// </summary>
    public bool IsVectorized { get; set; }

    /// <summary>
    /// Vector database
    /// </summary>
    public string? VectorDatabase { get; set; }

    /// <summary>
    /// Processing time
    /// </summary>
    public TimeSpan? ProcessingTime { get; set; }

    /// <summary>
    /// Created timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Updated timestamp
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}
