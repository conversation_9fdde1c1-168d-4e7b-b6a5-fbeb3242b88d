using LexAI.Identity.Application.DTOs;
using LexAI.Identity.Application.Interfaces;
using LexAI.Shared.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace LexAI.Identity.Application.Commands;

/// <summary>
/// Command to authenticate a user
/// </summary>
public class LoginCommand : IRequest<AuthenticationResponseDto>
{
    /// <summary>
    /// User's email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's password
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Remember me flag for extended session
    /// </summary>
    public bool RememberMe { get; set; } = false;

    /// <summary>
    /// IP address of the login attempt
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// User agent of the client
    /// </summary>
    public string? UserAgent { get; set; }
}

/// <summary>
/// Handler for LoginCommand
/// </summary>
public class LoginCommandHandler : IRequestHandler<LoginCommand, AuthenticationResponseDto>
{
    private readonly IUserRepository _userRepository;
    private readonly IRefreshTokenRepository _refreshTokenRepository;
    private readonly ITokenService _tokenService;
    private readonly ILogger<LoginCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the LoginCommandHandler
    /// </summary>
    /// <param name="userRepository">User repository</param>
    /// <param name="refreshTokenRepository">Refresh token repository</param>
    /// <param name="tokenService">Token service</param>
    /// <param name="logger">Logger</param>
    public LoginCommandHandler(
        IUserRepository userRepository,
        IRefreshTokenRepository refreshTokenRepository,
        ITokenService tokenService,
        ILogger<LoginCommandHandler> logger)
    {
        _userRepository = userRepository;
        _refreshTokenRepository = refreshTokenRepository;
        _tokenService = tokenService;
        _logger = logger;
    }

    /// <summary>
    /// Handles the LoginCommand
    /// </summary>
    /// <param name="request">Login command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Authentication response</returns>
    public async Task<AuthenticationResponseDto> Handle(LoginCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Login attempt for email {Email} from IP {IpAddress}", request.Email, request.IpAddress);

        var user = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
        if (user == null)
        {
            _logger.LogWarning("Login attempt with non-existent email {Email}", request.Email);
            throw new BusinessRuleViolationException("Invalid email or password", "INVALID_CREDENTIALS");
        }

        // Check if account is locked
        if (user.IsLocked)
        {
            _logger.LogWarning("Login attempt for locked account {UserId}", user.Id);
            throw new BusinessRuleViolationException("Account is locked due to multiple failed login attempts", "ACCOUNT_LOCKED");
        }

        // Check if account is active
        if (!user.IsActive)
        {
            _logger.LogWarning("Login attempt for inactive account {UserId}", user.Id);
            throw new BusinessRuleViolationException("Account is not active", "ACCOUNT_INACTIVE");
        }

        // Verify password
        if (!user.VerifyPassword(request.Password))
        {
            _logger.LogWarning("Failed login attempt for user {UserId} - invalid password", user.Id);
            
            // Record failed login attempt
            user.RecordFailedLogin(request.IpAddress);
            await _userRepository.UpdateAsync(user, cancellationToken);
            
            throw new BusinessRuleViolationException("Invalid email or password", "INVALID_CREDENTIALS");
        }

        try
        {
            // Record successful login
            user.RecordSuccessfulLogin(request.IpAddress);
            await _userRepository.UpdateAsync(user, cancellationToken);

            // Generate tokens
            var accessToken = _tokenService.GenerateAccessToken(user);
            var refreshToken = _tokenService.GenerateRefreshToken(user.Id, request.IpAddress, request.UserAgent);

            // Save refresh token
            await _refreshTokenRepository.AddAsync(refreshToken, cancellationToken);

            _logger.LogInformation("User {UserId} logged in successfully", user.Id);

            return new AuthenticationResponseDto
            {
                AccessToken = accessToken,
                RefreshToken = refreshToken.Token,
                TokenType = "Bearer",
                ExpiresIn = 3600, // 1 hour
                User = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    FullName = user.FullName,
                    PhoneNumber = user.PhoneNumber?.Value,
                    Role = user.Role,
                    IsEmailVerified = user.IsEmailVerified,
                    IsActive = user.IsActive,
                    IsLocked = user.IsLocked,
                    LastLoginAt = user.LastLoginAt,
                    PreferredLanguage = user.PreferredLanguage,
                    TimeZone = user.TimeZone,
                    ProfilePictureUrl = user.ProfilePictureUrl,
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt
                }
            };
        }
        catch (Exception ex) when (!(ex is DomainException))
        {
            _logger.LogError(ex, "Error during login for user {UserId}", user.Id);
            throw;
        }
    }
}

/// <summary>
/// Command to refresh an access token
/// </summary>
public class RefreshTokenCommand : IRequest<AuthenticationResponseDto>
{
    /// <summary>
    /// Refresh token value
    /// </summary>
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// IP address of the refresh request
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// User agent of the client
    /// </summary>
    public string? UserAgent { get; set; }
}

/// <summary>
/// Handler for RefreshTokenCommand
/// </summary>
public class RefreshTokenCommandHandler : IRequestHandler<RefreshTokenCommand, AuthenticationResponseDto>
{
    private readonly IUserRepository _userRepository;
    private readonly IRefreshTokenRepository _refreshTokenRepository;
    private readonly ITokenService _tokenService;
    private readonly ILogger<RefreshTokenCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the RefreshTokenCommandHandler
    /// </summary>
    /// <param name="userRepository">User repository</param>
    /// <param name="refreshTokenRepository">Refresh token repository</param>
    /// <param name="tokenService">Token service</param>
    /// <param name="logger">Logger</param>
    public RefreshTokenCommandHandler(
        IUserRepository userRepository,
        IRefreshTokenRepository refreshTokenRepository,
        ITokenService tokenService,
        ILogger<RefreshTokenCommandHandler> logger)
    {
        _userRepository = userRepository;
        _refreshTokenRepository = refreshTokenRepository;
        _tokenService = tokenService;
        _logger = logger;
    }

    /// <summary>
    /// Handles the RefreshTokenCommand
    /// </summary>
    /// <param name="request">Refresh token command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Authentication response</returns>
    public async Task<AuthenticationResponseDto> Handle(RefreshTokenCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Token refresh attempt from IP {IpAddress}", request.IpAddress);

        var refreshToken = await _refreshTokenRepository.GetByTokenAsync(request.RefreshToken, cancellationToken);
        if (refreshToken == null || !refreshToken.IsActive)
        {
            _logger.LogWarning("Invalid or expired refresh token used from IP {IpAddress}", request.IpAddress);
            throw new BusinessRuleViolationException("Invalid or expired refresh token", "INVALID_REFRESH_TOKEN");
        }

        var user = await _userRepository.GetByIdAsync(refreshToken.UserId, cancellationToken);
        if (user == null || !user.IsActive)
        {
            _logger.LogWarning("Refresh token belongs to non-existent or inactive user {UserId}", refreshToken.UserId);
            throw new BusinessRuleViolationException("User not found or inactive", "USER_INACTIVE");
        }

        try
        {
            // Revoke the old refresh token
            refreshToken.Revoke(user.Id.ToString(), "Token refreshed");
            await _refreshTokenRepository.UpdateAsync(refreshToken, cancellationToken);

            // Generate new tokens
            var accessToken = _tokenService.GenerateAccessToken(user);
            var newRefreshToken = _tokenService.GenerateRefreshToken(user.Id, request.IpAddress, request.UserAgent);

            // Save new refresh token
            await _refreshTokenRepository.AddAsync(newRefreshToken, cancellationToken);

            _logger.LogInformation("Token refreshed successfully for user {UserId}", user.Id);

            return new AuthenticationResponseDto
            {
                AccessToken = accessToken,
                RefreshToken = newRefreshToken.Token,
                TokenType = "Bearer",
                ExpiresIn = 3600, // 1 hour
                User = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    FullName = user.FullName,
                    PhoneNumber = user.PhoneNumber?.Value,
                    Role = user.Role,
                    IsEmailVerified = user.IsEmailVerified,
                    IsActive = user.IsActive,
                    IsLocked = user.IsLocked,
                    LastLoginAt = user.LastLoginAt,
                    PreferredLanguage = user.PreferredLanguage,
                    TimeZone = user.TimeZone,
                    ProfilePictureUrl = user.ProfilePictureUrl,
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt
                }
            };
        }
        catch (Exception ex) when (!(ex is DomainException))
        {
            _logger.LogError(ex, "Error during token refresh for user {UserId}", user.Id);
            throw;
        }
    }
}

/// <summary>
/// Command to logout a user
/// </summary>
public class LogoutCommand : IRequest<bool>
{
    /// <summary>
    /// Refresh token to revoke
    /// </summary>
    public string? RefreshToken { get; set; }

    /// <summary>
    /// User ID (if refresh token is not provided)
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// Revoke all user tokens
    /// </summary>
    public bool RevokeAllTokens { get; set; } = false;
}

/// <summary>
/// Handler for LogoutCommand
/// </summary>
public class LogoutCommandHandler : IRequestHandler<LogoutCommand, bool>
{
    private readonly IRefreshTokenRepository _refreshTokenRepository;
    private readonly ILogger<LogoutCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the LogoutCommandHandler
    /// </summary>
    /// <param name="refreshTokenRepository">Refresh token repository</param>
    /// <param name="logger">Logger</param>
    public LogoutCommandHandler(
        IRefreshTokenRepository refreshTokenRepository,
        ILogger<LogoutCommandHandler> logger)
    {
        _refreshTokenRepository = refreshTokenRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handles the LogoutCommand
    /// </summary>
    /// <param name="request">Logout command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if logout was successful</returns>
    public async Task<bool> Handle(LogoutCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (request.RevokeAllTokens && request.UserId.HasValue)
            {
                _logger.LogInformation("Revoking all tokens for user {UserId}", request.UserId.Value);
                await _refreshTokenRepository.RevokeAllUserTokensAsync(
                    request.UserId.Value, 
                    request.UserId.Value.ToString(), 
                    "User logout - all tokens revoked", 
                    cancellationToken);
            }
            else if (!string.IsNullOrEmpty(request.RefreshToken))
            {
                _logger.LogInformation("Revoking specific refresh token");
                var refreshToken = await _refreshTokenRepository.GetByTokenAsync(request.RefreshToken, cancellationToken);
                if (refreshToken != null && refreshToken.IsActive)
                {
                    refreshToken.Revoke(refreshToken.UserId.ToString(), "User logout");
                    await _refreshTokenRepository.UpdateAsync(refreshToken, cancellationToken);
                }
            }

            _logger.LogInformation("Logout completed successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            return false;
        }
    }
}
