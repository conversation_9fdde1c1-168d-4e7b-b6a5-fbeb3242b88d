import { DocumentStatus } from '../types'

/**
 * Convertit un statut de document en libellé français
 */
export const getStatusLabel = (status: DocumentStatus): string => {
  switch (status) {
    case DocumentStatus.Uploaded:
      return 'Téléchargé'
    case DocumentStatus.Extracting:
      return 'Extraction en cours'
    case DocumentStatus.Extracted:
      return 'Texte extrait'
    case DocumentStatus.Classifying:
      return 'Classification en cours'
    case DocumentStatus.Classified:
      return 'Classifié'
    case DocumentStatus.Chunking:
      return 'Découpage en cours'
    case DocumentStatus.Chunked:
      return 'Découpé'
    case DocumentStatus.Vectorizing:
      return 'Vectorisation en cours'
    case DocumentStatus.Completed:
      return 'Traité'
    case DocumentStatus.Failed:
      return 'Échec'
    case DocumentStatus.Reprocessing:
      return 'Retraitement'
    default:
      return 'Inconnu'
  }
}

/**
 * Détermine si un statut est considéré comme "en traitement"
 */
export const isProcessingStatus = (status: DocumentStatus): boolean => {
  return [
    DocumentStatus.Extracting,
    DocumentStatus.Classifying,
    DocumentStatus.Chunking,
    DocumentStatus.Vectorizing,
    DocumentStatus.Reprocessing
  ].includes(status)
}

/**
 * Détermine si un statut est considéré comme "traité"
 */
export const isCompletedStatus = (status: DocumentStatus): boolean => {
  return status === DocumentStatus.Completed
}

/**
 * Détermine si un statut est considéré comme "en attente"
 */
export const isPendingStatus = (status: DocumentStatus): boolean => {
  return [
    DocumentStatus.Uploaded,
    DocumentStatus.Extracted,
    DocumentStatus.Classified,
    DocumentStatus.Chunked
  ].includes(status)
}

/**
 * Détermine si un statut est considéré comme "échec"
 */
export const isFailedStatus = (status: DocumentStatus): boolean => {
  return status === DocumentStatus.Failed
}

/**
 * Groupe les statuts pour l'affichage dans les filtres
 */
export const getStatusGroups = () => ({
  uploaded: {
    label: 'Téléchargé',
    statuses: [DocumentStatus.Uploaded]
  },
  processing: {
    label: 'En traitement',
    statuses: [
      DocumentStatus.Extracting,
      DocumentStatus.Classifying,
      DocumentStatus.Chunking,
      DocumentStatus.Vectorizing,
      DocumentStatus.Reprocessing
    ]
  },
  completed: {
    label: 'Traité',
    statuses: [DocumentStatus.Completed]
  },
  pending: {
    label: 'En attente',
    statuses: [
      DocumentStatus.Extracted,
      DocumentStatus.Classified,
      DocumentStatus.Chunked
    ]
  },
  failed: {
    label: 'Échec',
    statuses: [DocumentStatus.Failed]
  }
})

/**
 * Obtient la couleur associée à un statut
 */
export const getStatusColor = (status: DocumentStatus): string => {
  if (isProcessingStatus(status)) return 'text-yellow-600'
  if (isCompletedStatus(status)) return 'text-green-600'
  if (isFailedStatus(status)) return 'text-red-600'
  if (isPendingStatus(status)) return 'text-blue-600'
  return 'text-gray-600'
}
