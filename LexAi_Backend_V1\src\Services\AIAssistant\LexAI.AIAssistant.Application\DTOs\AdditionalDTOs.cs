using LexAI.AIAssistant.Domain.ValueObjects;

namespace LexAI.AIAssistant.Application.DTOs;

/// <summary>
/// Document analysis request DTO
/// </summary>
public class DocumentAnalysisRequestDto
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Document name
    /// </summary>
    public string DocumentName { get; set; } = string.Empty;

    /// <summary>
    /// Document content
    /// </summary>
    public string DocumentContent { get; set; } = string.Empty;

    /// <summary>
    /// Document type
    /// </summary>
    public string? DocumentType { get; set; }

    /// <summary>
    /// Analysis focus areas
    /// </summary>
    public List<string> FocusAreas { get; set; } = new();

    /// <summary>
    /// Additional context
    /// </summary>
    public string? Context { get; set; }
}

/// <summary>
/// Document analysis response DTO
/// </summary>
public class DocumentAnalysisResponseDto
{
    /// <summary>
    /// Document name
    /// </summary>
    public string DocumentName { get; set; } = string.Empty;

    /// <summary>
    /// Analysis result
    /// </summary>
    public string Analysis { get; set; } = string.Empty;

    /// <summary>
    /// Key findings
    /// </summary>
    public List<string> KeyFindings { get; set; } = new();

    /// <summary>
    /// Risk assessment
    /// </summary>
    public string RiskAssessment { get; set; } = string.Empty;

    /// <summary>
    /// Recommendations
    /// </summary>
    public List<string> Recommendations { get; set; } = new();

    /// <summary>
    /// Citations
    /// </summary>
    public List<CitationDto> Citations { get; set; } = new();

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// Tokens used
    /// </summary>
    public int TokensUsed { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }
}

/// <summary>
/// Legal research request DTO
/// </summary>
public class LegalResearchRequestDto
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Research query
    /// </summary>
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// Legal domain filter
    /// </summary>
    public LegalDomain? Domain { get; set; }

    /// <summary>
    /// Jurisdiction
    /// </summary>
    public string? Jurisdiction { get; set; }

    /// <summary>
    /// Research depth
    /// </summary>
    public ResearchDepth Depth { get; set; } = ResearchDepth.Standard;

    /// <summary>
    /// Include case law
    /// </summary>
    public bool IncludeCaseLaw { get; set; } = true;

    /// <summary>
    /// Include legislation
    /// </summary>
    public bool IncludeLegislation { get; set; } = true;

    /// <summary>
    /// Include academic sources
    /// </summary>
    public bool IncludeAcademicSources { get; set; } = false;
}

/// <summary>
/// Legal research response DTO
/// </summary>
public class LegalResearchResponseDto
{
    /// <summary>
    /// Research query
    /// </summary>
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// Research result
    /// </summary>
    public string Research { get; set; } = string.Empty;

    /// <summary>
    /// Key insights
    /// </summary>
    public List<string> KeyInsights { get; set; } = new();

    /// <summary>
    /// Relevant documents
    /// </summary>
    public List<LegalDocumentSummaryDto> RelevantDocuments { get; set; } = new();

    /// <summary>
    /// Citations
    /// </summary>
    public List<CitationDto> Citations { get; set; } = new();

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// Tokens used
    /// </summary>
    public int TokensUsed { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }
}

/// <summary>
/// Document generation request DTO
/// </summary>
public class DocumentGenerationRequestDto
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Document type
    /// </summary>
    public string DocumentType { get; set; } = string.Empty;

    /// <summary>
    /// Requirements and specifications
    /// </summary>
    public string Requirements { get; set; } = string.Empty;

    /// <summary>
    /// Template to use
    /// </summary>
    public string? Template { get; set; }

    /// <summary>
    /// Jurisdiction
    /// </summary>
    public string? Jurisdiction { get; set; }

    /// <summary>
    /// Additional parameters
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Document generation response DTO
/// </summary>
public class DocumentGenerationResponseDto
{
    /// <summary>
    /// Document type
    /// </summary>
    public string DocumentType { get; set; } = string.Empty;

    /// <summary>
    /// Generated content
    /// </summary>
    public string GeneratedContent { get; set; } = string.Empty;

    /// <summary>
    /// Document metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// Tokens used
    /// </summary>
    public int TokensUsed { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }
}

/// <summary>
/// Conversation summary DTO
/// </summary>
public class ConversationSummaryDto
{
    /// <summary>
    /// Conversation ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// Conversation summary
    /// </summary>
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// Key topics discussed
    /// </summary>
    public List<string> KeyTopics { get; set; } = new();

    /// <summary>
    /// Main conclusions
    /// </summary>
    public List<string> MainConclusions { get; set; } = new();

    /// <summary>
    /// Action items
    /// </summary>
    public List<string> ActionItems { get; set; } = new();

    /// <summary>
    /// Generated timestamp
    /// </summary>
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Conversation DTO
/// </summary>
public class ConversationDto
{
    /// <summary>
    /// Conversation ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Conversation title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Session ID
    /// </summary>
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// Conversation status
    /// </summary>
    public ConversationStatus Status { get; set; }

    /// <summary>
    /// Primary legal domain
    /// </summary>
    public LegalDomain? PrimaryDomain { get; set; }

    /// <summary>
    /// Conversation tags
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Messages in the conversation
    /// </summary>
    public List<MessageDto> Messages { get; set; } = new();

    /// <summary>
    /// Conversation context
    /// </summary>
    public ConversationContextDto Context { get; set; } = null!;

    /// <summary>
    /// Created timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Last activity timestamp
    /// </summary>
    public DateTime LastActivityAt { get; set; }

    /// <summary>
    /// Message count
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    /// Total tokens used
    /// </summary>
    public int TotalTokensUsed { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// User rating
    /// </summary>
    public int? UserRating { get; set; }
}

/// <summary>
/// Create conversation request DTO
/// </summary>
public class CreateConversationRequestDto
{
    /// <summary>
    /// Conversation title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Session ID
    /// </summary>
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// Conversation context
    /// </summary>
    public ConversationContextDto? Context { get; set; }
}

/// <summary>
/// Update conversation request DTO
/// </summary>
public class UpdateConversationRequestDto
{
    /// <summary>
    /// New title
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// Tags to add or update
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Updated context
    /// </summary>
    public ConversationContextDto? Context { get; set; }
}

/// <summary>
/// AI model request DTO
/// </summary>
public class AIModelRequestDto
{
    /// <summary>
    /// Model type
    /// </summary>
    public AIModelType ModelType { get; set; }

    /// <summary>
    /// Messages for the conversation
    /// </summary>
    public List<AIMessageDto> Messages { get; set; } = new();

    /// <summary>
    /// Maximum tokens
    /// </summary>
    public int MaxTokens { get; set; } = 4000;

    /// <summary>
    /// Temperature
    /// </summary>
    public double Temperature { get; set; } = 0.7;

    /// <summary>
    /// Additional parameters
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// AI model response DTO
/// </summary>
public class AIModelResponseDto
{
    /// <summary>
    /// Generated content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Tokens used
    /// </summary>
    public int TokensUsed { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// Model used
    /// </summary>
    public AIModelType ModelUsed { get; set; }

    /// <summary>
    /// Response metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// AI message DTO for model requests
/// </summary>
public class AIMessageDto
{
    /// <summary>
    /// Message role
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// Message content
    /// </summary>
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// Content moderation result DTO
/// </summary>
public class ContentModerationResultDto
{
    /// <summary>
    /// Whether content is approved
    /// </summary>
    public bool IsApproved { get; set; }

    /// <summary>
    /// Moderation reason if not approved
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// Confidence score
    /// </summary>
    public double ConfidenceScore { get; set; }

    /// <summary>
    /// Detected categories
    /// </summary>
    public List<string> DetectedCategories { get; set; } = new();
}

/// <summary>
/// Research depth enumeration
/// </summary>
public enum ResearchDepth
{
    /// <summary>
    /// Quick overview
    /// </summary>
    Quick,

    /// <summary>
    /// Standard research
    /// </summary>
    Standard,

    /// <summary>
    /// Deep comprehensive research
    /// </summary>
    Deep,

    /// <summary>
    /// Exhaustive research
    /// </summary>
    Exhaustive
}


