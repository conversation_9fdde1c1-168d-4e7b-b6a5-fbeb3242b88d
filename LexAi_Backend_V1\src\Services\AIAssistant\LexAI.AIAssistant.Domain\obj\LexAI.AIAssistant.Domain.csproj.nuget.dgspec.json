{"format": 1, "restore": {"D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj": {}}, "projects": {"D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj", "projectName": "LexAI.AIAssistant.Domain", "projectPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj": {"projectPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"System.Text.Json": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj", "projectName": "LexAI.Shared.Domain", "projectPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.9.2, )"}, "MediatR": {"target": "Package", "version": "[12.4.1, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}