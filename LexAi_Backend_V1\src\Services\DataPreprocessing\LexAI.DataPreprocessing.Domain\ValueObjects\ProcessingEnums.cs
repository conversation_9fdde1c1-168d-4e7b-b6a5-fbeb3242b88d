namespace LexAI.DataPreprocessing.Domain.ValueObjects;

/// <summary>
/// Document processing status enumeration
/// </summary>
public enum DocumentStatus
{
    /// <summary>
    /// Document has been uploaded
    /// </summary>
    Uploaded,

    /// <summary>
    /// Text extraction in progress
    /// </summary>
    Extracting,

    /// <summary>
    /// Text extraction completed
    /// </summary>
    Extracted,

    /// <summary>
    /// Domain classification in progress
    /// </summary>
    Classifying,

    /// <summary>
    /// Domain classification completed
    /// </summary>
    Classified,

    /// <summary>
    /// Document chunking in progress
    /// </summary>
    Chunking,

    /// <summary>
    /// Document chunking completed
    /// </summary>
    Chunked,

    /// <summary>
    /// Vectorization in progress
    /// </summary>
    Vectorizing,

    /// <summary>
    /// Processing completed successfully
    /// </summary>
    Completed,

    /// <summary>
    /// Processing failed
    /// </summary>
    Failed,

    /// <summary>
    /// Document is being reprocessed
    /// </summary>
    Reprocessing
}

/// <summary>
/// Legal domain enumeration
/// </summary>
public enum LegalDomain
{
    /// <summary>
    /// Civil law
    /// </summary>
    Civil,

    /// <summary>
    /// Criminal law
    /// </summary>
    Criminal,

    /// <summary>
    /// Administrative law
    /// </summary>
    Administrative,

    /// <summary>
    /// Constitutional law
    /// </summary>
    Constitutional,

    /// <summary>
    /// Commercial and business law
    /// </summary>
    Commercial,

    /// <summary>
    /// Labor and employment law
    /// </summary>
    Labor,

    /// <summary>
    /// Tax law
    /// </summary>
    Tax,

    /// <summary>
    /// Real estate law
    /// </summary>
    RealEstate,

    /// <summary>
    /// Family law
    /// </summary>
    Family,

    /// <summary>
    /// Intellectual property law
    /// </summary>
    IntellectualProperty,

    /// <summary>
    /// Environmental law
    /// </summary>
    Environmental,

    /// <summary>
    /// Health law
    /// </summary>
    Health,

    /// <summary>
    /// Immigration law
    /// </summary>
    Immigration,

    /// <summary>
    /// International law
    /// </summary>
    International,

    /// <summary>
    /// European Union law
    /// </summary>
    European,

    /// <summary>
    /// Banking and finance law
    /// </summary>
    Banking,

    /// <summary>
    /// Insurance law
    /// </summary>
    Insurance,

    /// <summary>
    /// Technology and data law
    /// </summary>
    Technology,

    /// <summary>
    /// Competition law
    /// </summary>
    Competition,

    /// <summary>
    /// Consumer protection law
    /// </summary>
    Consumer,

    /// <summary>
    /// Audit and compliance
    /// </summary>
    Audit,

    /// <summary>
    /// Strategy and governance
    /// </summary>
    Strategy,

    /// <summary>
    /// Other legal domain
    /// </summary>
    Other
}

/// <summary>
/// Document chunk type enumeration
/// </summary>
public enum ChunkType
{
    /// <summary>
    /// Document title
    /// </summary>
    Title,

    /// <summary>
    /// Section header
    /// </summary>
    Header,

    /// <summary>
    /// Regular paragraph
    /// </summary>
    Paragraph,

    /// <summary>
    /// List item
    /// </summary>
    List,

    /// <summary>
    /// Table content
    /// </summary>
    Table,

    /// <summary>
    /// Summary section
    /// </summary>
    Summary,

    /// <summary>
    /// Conclusion section
    /// </summary>
    Conclusion,

    /// <summary>
    /// Footer content
    /// </summary>
    Footer,

    /// <summary>
    /// Legal article or section
    /// </summary>
    Article,

    /// <summary>
    /// Legal definition
    /// </summary>
    Definition,

    /// <summary>
    /// Case law reference
    /// </summary>
    CaseLaw,

    /// <summary>
    /// Citation or reference
    /// </summary>
    Citation,

    /// <summary>
    /// Other content type
    /// </summary>
    Other
}

/// <summary>
/// Processing error severity enumeration
/// </summary>
public enum ErrorSeverity
{
    /// <summary>
    /// Low severity - warning
    /// </summary>
    Low,

    /// <summary>
    /// Medium severity - error but processing can continue
    /// </summary>
    Medium,

    /// <summary>
    /// High severity - critical error, processing must stop
    /// </summary>
    High,

    /// <summary>
    /// Critical severity - system error
    /// </summary>
    Critical
}

/// <summary>
/// Agent type enumeration
/// </summary>
public enum AgentType
{
    /// <summary>
    /// Text extraction agent
    /// </summary>
    Extraction,

    /// <summary>
    /// Document classification agent
    /// </summary>
    Classification,

    /// <summary>
    /// Document chunking agent
    /// </summary>
    Chunking,

    /// <summary>
    /// Vectorization agent
    /// </summary>
    Vectorization,

    /// <summary>
    /// Routing agent
    /// </summary>
    Routing,

    /// <summary>
    /// Quality assurance agent
    /// </summary>
    QualityAssurance,

    /// <summary>
    /// Orchestration agent
    /// </summary>
    Orchestration
}

/// <summary>
/// Vector database type enumeration
/// </summary>
public enum VectorDatabaseType
{
    /// <summary>
    /// MongoDB with vector search
    /// </summary>
    MongoDB,

    /// <summary>
    /// Qdrant vector database
    /// </summary>
    Qdrant,

    /// <summary>
    /// Weaviate vector database
    /// </summary>
    Weaviate,

    /// <summary>
    /// Pinecone vector database
    /// </summary>
    Pinecone,

    /// <summary>
    /// Chroma vector database
    /// </summary>
    Chroma,

    /// <summary>
    /// FAISS vector index
    /// </summary>
    FAISS
}

/// <summary>
/// Chunking strategy enumeration
/// </summary>
public enum ChunkingStrategy
{
    /// <summary>
    /// Fixed size chunks with overlap
    /// </summary>
    FixedSize,

    /// <summary>
    /// Semantic chunking based on meaning
    /// </summary>
    Semantic,

    /// <summary>
    /// Sentence-based chunking
    /// </summary>
    Sentence,

    /// <summary>
    /// Paragraph-based chunking
    /// </summary>
    Paragraph,

    /// <summary>
    /// Section-based chunking
    /// </summary>
    Section,

    /// <summary>
    /// Sliding window chunking
    /// </summary>
    SlidingWindow,

    /// <summary>
    /// Recursive chunking
    /// </summary>
    Recursive
}

/// <summary>
/// Embedding model type enumeration
/// </summary>
public enum EmbeddingModelType
{
    /// <summary>
    /// OpenAI text-embedding-3-small
    /// </summary>
    OpenAISmall,

    /// <summary>
    /// OpenAI text-embedding-3-large
    /// </summary>
    OpenAILarge,

    /// <summary>
    /// OpenAI text-embedding-ada-002
    /// </summary>
    OpenAIAda002,

    /// <summary>
    /// Cohere embed model
    /// </summary>
    Cohere,

    /// <summary>
    /// HuggingFace sentence transformers
    /// </summary>
    HuggingFace,

    /// <summary>
    /// Custom legal domain model
    /// </summary>
    CustomLegal
}

/// <summary>
/// Named entity type enumeration
/// </summary>
public enum EntityType
{
    /// <summary>
    /// Person name
    /// </summary>
    Person,

    /// <summary>
    /// Organization name
    /// </summary>
    Organization,

    /// <summary>
    /// Location or place
    /// </summary>
    Location,

    /// <summary>
    /// Date or time
    /// </summary>
    Date,

    /// <summary>
    /// Monetary amount
    /// </summary>
    Money,

    /// <summary>
    /// Legal reference
    /// </summary>
    LegalReference,

    /// <summary>
    /// Case number
    /// </summary>
    CaseNumber,

    /// <summary>
    /// Law or regulation
    /// </summary>
    Law,

    /// <summary>
    /// Court or tribunal
    /// </summary>
    Court,

    /// <summary>
    /// Contract type
    /// </summary>
    Contract,

    /// <summary>
    /// Other entity type
    /// </summary>
    Other
}
