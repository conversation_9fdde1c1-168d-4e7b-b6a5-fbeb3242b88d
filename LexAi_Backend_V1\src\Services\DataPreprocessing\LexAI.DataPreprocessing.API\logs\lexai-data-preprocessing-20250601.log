2025-06-01 00:41:47.950 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-01 00:41:48.930 +04:00 [INF] Hangfire SQL objects installed.
2025-06-01 00:41:48.944 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-01 00:41:49.393 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-01 00:41:49.411 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 00:41:49.751 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-01 00:41:49.755 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-01 00:41:49.806 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-01 00:41:49.809 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-01 00:41:49.820 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-01 00:41:49.822 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-01 00:41:49.823 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-01 00:41:49.826 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-01 00:41:49.879 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 00:41:49.883 +04:00 [INF] Hosting environment: Development
2025-06-01 00:41:49.885 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-01 00:41:49.891 +04:00 [INF] Server datapreprocessing-kevin11:37136:3a0d0999 successfully announced in 12.651 ms
2025-06-01 00:41:49.900 +04:00 [INF] Server datapreprocessing-kevin11:37136:3a0d0999 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-01 00:41:50.106 +04:00 [INF] Server datapreprocessing-kevin11:37136:3a0d0999 all the dispatchers started
2025-06-01 00:41:51.911 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-01 00:41:52.526 +04:00 [INF] Request GET / started with correlation ID 9fd80b09-d25d-4c6b-af4e-01553ba179c4
2025-06-01 00:41:52.596 +04:00 [INF] Request GET / completed in 59ms with status 404 (Correlation ID: 9fd80b09-d25d-4c6b-af4e-01553ba179c4)
2025-06-01 00:41:52.608 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 763.713ms
2025-06-01 00:41:52.617 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-01 00:44:28.273 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger - null null
2025-06-01 00:44:28.329 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger - 301 0 null 56.8388ms
2025-06-01 00:44:28.395 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/index.html - null null
2025-06-01 00:44:28.479 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/index.html - 200 null text/html;charset=utf-8 83.8467ms
2025-06-01 00:44:28.558 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/swagger-ui.css - null null
2025-06-01 00:44:28.558 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/swagger-ui-bundle.js - null null
2025-06-01 00:44:28.559 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/index.js - null null
2025-06-01 00:44:28.558 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/index.css - null null
2025-06-01 00:44:28.560 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/swagger-ui-standalone-preset.js - null null
2025-06-01 00:44:28.617 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 00:44:28.649 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/index.js - 200 null application/javascript;charset=utf-8 90.3064ms
2025-06-01 00:44:28.660 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 79.5591ms
2025-06-01 00:44:28.675 +04:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-06-01 00:44:28.734 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/index.css - 200 202 text/css 175.89ms
2025-06-01 00:44:28.759 +04:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-01 00:44:28.760 +04:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-01 00:44:28.766 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/swagger-ui-standalone-preset.js - 200 230007 text/javascript 206.2296ms
2025-06-01 00:44:28.766 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/swagger-ui.css - 200 152035 text/css 208.0412ms
2025-06-01 00:44:28.772 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 00:44:29.023 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 251.0651ms
2025-06-01 00:44:29.033 +04:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-01 00:44:29.035 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/swagger-ui-bundle.js - 200 1426001 text/javascript 476.9337ms
2025-06-01 00:44:29.286 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/v1/swagger.json - null null
2025-06-01 00:44:29.286 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/favicon-32x32.png - null null
2025-06-01 00:44:29.299 +04:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-01 00:44:29.302 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/favicon-32x32.png - 200 628 image/png 16.1726ms
2025-06-01 00:44:29.333 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 46.9755ms
2025-06-01 00:44:47.122 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire - null null
2025-06-01 00:44:47.220 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire - 401 0 null 98.1672ms
2025-06-01 00:45:11.467 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire - null null
2025-06-01 00:45:11.635 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire - 200 null text/html 168.3397ms
2025-06-01 00:45:11.699 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/css-dark18140392045746 - null null
2025-06-01 00:45:11.701 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/js181401915355278 - null null
2025-06-01 00:45:11.702 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 00:45:11.699 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/css18140194737225 - null null
2025-06-01 00:45:11.771 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 69.3156ms
2025-06-01 00:45:11.813 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/css18140194737225 - 200 null text/css 113.9799ms
2025-06-01 00:45:11.806 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 00:45:11.795 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/css-dark18140392045746 - 200 null text/css 95.4594ms
2025-06-01 00:45:11.843 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/js181401915355278 - 200 null application/javascript 141.9715ms
2025-06-01 00:45:11.886 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 80.0997ms
2025-06-01 00:45:11.903 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/fonts/glyphicons-halflings-regular/woff2 - null null
2025-06-01 00:45:11.914 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/fonts/glyphicons-halflings-regular/woff2 - 200 null font/woff2 10.4436ms
2025-06-01 00:45:13.979 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:14.011 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 31.6663ms
2025-06-01 00:45:16.023 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:16.048 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.4762ms
2025-06-01 00:45:18.057 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:18.081 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.674ms
2025-06-01 00:45:20.098 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:20.116 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.6772ms
2025-06-01 00:45:22.125 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:22.143 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.5759ms
2025-06-01 00:45:24.157 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:24.174 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.6427ms
2025-06-01 00:45:26.192 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:26.206 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.1294ms
2025-06-01 00:45:28.212 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:28.223 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.243ms
2025-06-01 00:45:30.239 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:30.254 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.9012ms
2025-06-01 00:45:32.266 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:32.280 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.9721ms
2025-06-01 00:45:34.295 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:34.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 27.2838ms
2025-06-01 00:45:36.344 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:36.361 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.119ms
2025-06-01 00:45:38.380 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:38.394 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.5462ms
2025-06-01 00:45:40.412 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:40.427 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.9307ms
2025-06-01 00:45:42.443 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:42.463 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.6784ms
2025-06-01 00:45:44.482 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:44.519 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 37.9267ms
2025-06-01 00:45:46.529 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:46.553 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.5188ms
2025-06-01 00:45:48.565 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:48.576 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.8015ms
2025-06-01 00:45:50.595 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:50.613 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.0054ms
2025-06-01 00:45:52.627 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:52.643 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.5713ms
2025-06-01 00:45:54.659 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:54.684 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.1418ms
2025-06-01 00:45:57.459 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:45:57.484 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.6968ms
2025-06-01 00:46:00.464 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:46:00.472 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.3007ms
2025-06-01 00:46:03.470 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:46:03.489 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.4768ms
2025-06-01 00:46:06.455 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:46:06.468 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.9095ms
2025-06-01 00:46:09.463 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:46:09.497 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 34.4969ms
2025-06-01 00:46:12.463 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:46:12.482 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.7297ms
2025-06-01 00:46:15.471 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:46:15.489 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.2664ms
2025-06-01 00:46:18.464 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:46:18.480 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.7547ms
2025-06-01 00:46:21.458 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:46:21.468 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.764ms
2025-06-01 00:46:24.460 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:46:24.478 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.5845ms
2025-06-01 00:46:27.468 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 00:46:27.483 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.2908ms
2025-06-01 16:26:07.349 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-01 16:26:08.141 +04:00 [INF] Hangfire SQL objects installed.
2025-06-01 16:26:08.164 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-01 16:26:09.178 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-01 16:26:09.203 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 16:26:09.567 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-01 16:26:09.574 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-01 16:26:09.796 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-01 16:26:09.797 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-01 16:26:09.798 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-01 16:26:09.799 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-01 16:26:09.800 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-01 16:26:09.801 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-01 16:26:10.348 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 16:26:10.389 +04:00 [INF] Hosting environment: Development
2025-06-01 16:26:10.394 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-01 16:26:10.429 +04:00 [INF] Server datapreprocessing-kevin11:33052:6247f7f7 successfully announced in 37.9401 ms
2025-06-01 16:26:10.444 +04:00 [INF] Server datapreprocessing-kevin11:33052:6247f7f7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-01 16:26:10.723 +04:00 [INF] Server datapreprocessing-kevin11:33052:6247f7f7 all the dispatchers started
2025-06-01 16:26:11.730 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-01 16:26:11.969 +04:00 [INF] Request GET / started with correlation ID 224b34a9-71d2-4cd6-84a9-c26150a18779
2025-06-01 16:26:13.951 +04:00 [INF] Request GET / completed in 1977ms with status 404 (Correlation ID: 224b34a9-71d2-4cd6-84a9-c26150a18779)
2025-06-01 16:26:13.964 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 2256.728ms
2025-06-01 16:26:13.971 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-01 16:26:31.819 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger - null null
2025-06-01 16:26:31.861 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger - 301 0 null 41.7588ms
2025-06-01 16:26:31.874 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/index.html - null null
2025-06-01 16:26:31.974 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/index.html - 200 null text/html;charset=utf-8 99.9012ms
2025-06-01 16:26:32.019 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/swagger-ui.css - null null
2025-06-01 16:26:32.026 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/swagger-ui-standalone-preset.js - null null
2025-06-01 16:26:32.026 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/index.css - null null
2025-06-01 16:26:32.026 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/swagger-ui-bundle.js - null null
2025-06-01 16:26:32.031 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/index.js - null null
2025-06-01 16:26:32.037 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 16:26:32.075 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/index.js - 200 null application/javascript;charset=utf-8 46.1332ms
2025-06-01 16:26:32.077 +04:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-06-01 16:26:32.109 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/index.css - 200 202 text/css 82.3395ms
2025-06-01 16:26:32.131 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 93.3883ms
2025-06-01 16:26:32.143 +04:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-01 16:26:32.143 +04:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-01 16:26:32.158 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/swagger-ui.css - 200 152035 text/css 138.9845ms
2025-06-01 16:26:32.158 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/swagger-ui-standalone-preset.js - 200 230007 text/javascript 132.2382ms
2025-06-01 16:26:32.163 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 16:26:32.217 +04:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-01 16:26:32.220 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/swagger-ui-bundle.js - 200 1426001 text/javascript 194.5359ms
2025-06-01 16:26:32.243 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 80.1159ms
2025-06-01 16:26:32.369 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/v1/swagger.json - null null
2025-06-01 16:26:32.394 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/swagger/favicon-32x32.png - null null
2025-06-01 16:26:32.404 +04:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-01 16:26:32.406 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/favicon-32x32.png - 200 628 image/png 12.9879ms
2025-06-01 16:26:32.424 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 55.363ms
2025-06-01 16:27:19.854 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire - null null
2025-06-01 16:27:19.970 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire - 401 0 null 116.9094ms
2025-06-01 16:28:02.509 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire - null null
2025-06-01 16:28:02.654 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire - 200 null text/html 144.3078ms
2025-06-01 16:28:02.720 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/css-dark18140399546018 - null null
2025-06-01 16:28:02.720 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/css181401005717134 - null null
2025-06-01 16:28:02.729 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/js18140815242681 - null null
2025-06-01 16:28:02.755 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 16:28:02.773 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/css-dark18140399546018 - 200 null text/css 52.9025ms
2025-06-01 16:28:02.773 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/css181401005717134 - 200 null text/css 52.977ms
2025-06-01 16:28:02.779 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 24.6815ms
2025-06-01 16:28:02.780 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-01 16:28:02.802 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/js18140815242681 - 200 null application/javascript 72.7939ms
2025-06-01 16:28:02.839 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 58.8076ms
2025-06-01 16:28:02.841 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/fonts/glyphicons-halflings-regular/woff2 - null null
2025-06-01 16:28:02.858 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/fonts/glyphicons-halflings-regular/woff2 - 200 null font/woff2 16.8613ms
2025-06-01 16:28:04.927 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:04.956 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 29.362ms
2025-06-01 16:28:06.965 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:06.979 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.993ms
2025-06-01 16:28:08.990 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:09.004 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.3009ms
2025-06-01 16:28:11.014 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:11.030 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.0314ms
2025-06-01 16:28:13.038 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:13.052 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.8253ms
2025-06-01 16:28:15.059 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:15.085 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.1874ms
2025-06-01 16:28:17.099 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:17.121 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.1251ms
2025-06-01 16:28:19.142 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:19.170 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 27.9325ms
2025-06-01 16:28:21.191 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:21.229 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 38.3033ms
2025-06-01 16:28:23.244 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:23.268 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.0161ms
2025-06-01 16:28:25.283 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:25.310 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 27.3265ms
2025-06-01 16:28:27.327 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-01 16:28:27.341 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.8646ms
2025-06-01 17:00:54.251 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-01 17:00:54.405 +04:00 [INF] Hangfire SQL objects installed.
2025-06-01 17:00:54.412 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-01 17:00:54.470 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-01 17:00:54.485 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 17:00:54.552 +04:00 [INF] Now listening on: http://localhost:5002
2025-06-01 17:00:54.560 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-01 17:00:54.561 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-01 17:00:54.562 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-01 17:00:54.565 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-01 17:00:54.567 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-01 17:00:54.567 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-01 17:00:54.577 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 17:00:54.578 +04:00 [INF] Hosting environment: Development
2025-06-01 17:00:54.581 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-01 17:00:54.707 +04:00 [INF] Server datapreprocessing-kevin11:49128:d0348423 successfully announced in 121.2053 ms
2025-06-01 17:00:54.711 +04:00 [INF] Server datapreprocessing-kevin11:49128:d0348423 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-01 17:00:54.718 +04:00 [INF] Server datapreprocessing-kevin11:49128:d0348423 all the dispatchers started
2025-06-01 17:00:54.722 +04:00 [INF] 1 servers were removed due to timeout
2025-06-01 17:00:55.076 +04:00 [INF] Generating processing statistics
2025-06-01 17:01:15.723 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger - null null
2025-06-01 17:01:15.916 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger - 301 0 null 193.1333ms
2025-06-01 17:01:15.926 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/index.html - null null
2025-06-01 17:01:15.990 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/index.html - 200 null text/html;charset=utf-8 63.6065ms
2025-06-01 17:01:16.010 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui.css - null null
2025-06-01 17:01:16.028 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/index.css - null null
2025-06-01 17:01:16.031 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui-bundle.js - null null
2025-06-01 17:01:16.032 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui-standalone-preset.js - null null
2025-06-01 17:01:16.036 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/index.js - null null
2025-06-01 17:01:16.048 +04:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-06-01 17:01:16.053 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/index.css - 200 202 text/css 24.799ms
2025-06-01 17:01:16.053 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/index.js - 200 null application/javascript;charset=utf-8 16.589ms
2025-06-01 17:01:16.063 +04:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-01 17:01:16.068 +04:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-01 17:01:16.070 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui.css - 200 152035 text/css 59.77ms
2025-06-01 17:01:16.071 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui-standalone-preset.js - 200 230007 text/javascript 39.1575ms
2025-06-01 17:01:16.141 +04:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-01 17:01:16.143 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui-bundle.js - 200 1426001 text/javascript 111.4077ms
2025-06-01 17:01:16.379 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/v1/swagger.json - null null
2025-06-01 17:01:16.427 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/favicon-32x32.png - null null
2025-06-01 17:01:16.434 +04:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-01 17:01:16.437 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/favicon-32x32.png - 200 628 image/png 9.6732ms
2025-06-01 17:01:16.588 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 208.9966ms
2025-06-01 17:01:19.881 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/hangfire - null null
2025-06-01 17:01:19.899 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/hangfire - 401 0 null 18.1508ms
2025-06-01 17:03:13.353 +04:00 [INF] Server datapreprocessing-kevin11:49128:d0348423 caught stopping signal...
2025-06-01 17:03:13.354 +04:00 [INF] Application is shutting down...
2025-06-01 17:03:13.356 +04:00 [INF] Server datapreprocessing-kevin11:49128:d0348423 All dispatchers stopped
2025-06-01 17:03:13.364 +04:00 [INF] Server datapreprocessing-kevin11:49128:d0348423 successfully reported itself as stopped in 6.5337 ms
2025-06-01 17:03:13.367 +04:00 [INF] Server datapreprocessing-kevin11:49128:d0348423 has been stopped in total 13.0064 ms
