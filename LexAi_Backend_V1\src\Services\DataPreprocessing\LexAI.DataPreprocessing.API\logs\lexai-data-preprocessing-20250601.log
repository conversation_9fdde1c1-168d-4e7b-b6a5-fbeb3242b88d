2025-06-01 19:10:25.128 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-01 19:10:25.397 +04:00 [INF] Hangfire SQL objects installed.
2025-06-01 19:10:25.419 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-01 19:10:25.908 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-01 19:10:25.936 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 19:10:26.274 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-01 19:10:26.288 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-01 19:10:26.391 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-01 19:10:26.397 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-01 19:10:26.401 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-01 19:10:26.962 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-01 19:10:26.965 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-01 19:10:26.968 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-01 19:10:27.038 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 19:10:27.044 +04:00 [INF] Hosting environment: Development
2025-06-01 19:10:27.049 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-01 19:10:27.107 +04:00 [INF] Server datapreprocessing-kevin11:44580:b01f3406 successfully announced in 35.4096 ms
2025-06-01 19:10:27.117 +04:00 [INF] Server datapreprocessing-kevin11:44580:b01f3406 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-01 19:10:27.217 +04:00 [INF] 1 servers were removed due to timeout
2025-06-01 19:10:27.431 +04:00 [INF] Server datapreprocessing-kevin11:44580:b01f3406 all the dispatchers started
2025-06-01 19:10:27.891 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-01 19:10:28.079 +04:00 [INF] Generating processing statistics
2025-06-01 19:10:28.102 +04:00 [INF] Request GET / started with correlation ID 60e63e9a-8ed0-4456-a39f-063da8434a19
2025-06-01 19:10:28.406 +04:00 [INF] Request GET / completed in 297ms with status 404 (Correlation ID: 60e63e9a-8ed0-4456-a39f-063da8434a19)
2025-06-01 19:10:28.416 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 528.306ms
2025-06-01 19:10:28.426 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-01 19:12:32.802 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 19:12:32.803 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 19:12:32.849 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 991051bb-ecbd-43d1-84e9-da0f23d48983
2025-06-01 19:12:32.850 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID e5fbeea5-8240-4f99-983d-150dcd1f1932
2025-06-01 19:12:32.869 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:12:32.883 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:12:32.893 +04:00 [INF] Request OPTIONS /api/documents completed in 37ms with status 204 (Correlation ID: 991051bb-ecbd-43d1-84e9-da0f23d48983)
2025-06-01 19:12:32.894 +04:00 [INF] Request OPTIONS /api/documents completed in 12ms with status 204 (Correlation ID: e5fbeea5-8240-4f99-983d-150dcd1f1932)
2025-06-01 19:12:32.899 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 97.5088ms
2025-06-01 19:12:32.917 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 114.8921ms
2025-06-01 19:12:32.916 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 19:12:32.958 +04:00 [INF] Request GET /api/documents started with correlation ID 22cc92da-8e32-4248-b6ae-bd0d76e1dea4
2025-06-01 19:12:32.962 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:12:33.083 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:12:33.111 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:12:33.216 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:12:43.959 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:43.964 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:43.970 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:43.979 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:43.984 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:43.990 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 19:12:44.584 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 19:12:44.973 +04:00 [INF] Executed DbCommand (47ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 19:12:44.993 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 19:12:45.035 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 19:12:45.044 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 11809.2481ms
2025-06-01 19:12:45.056 +04:00 [INF] Request GET /api/documents started with correlation ID bb7f07fe-f651-46f7-b5f9-47595753adcc
2025-06-01 19:12:45.064 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:12:45.064 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:12:45.075 +04:00 [INF] Request GET /api/documents completed in 12113ms with status 200 (Correlation ID: 22cc92da-8e32-4248-b6ae-bd0d76e1dea4)
2025-06-01 19:12:45.080 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:12:45.090 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:12:45.098 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:12:45.116 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 12200.5241ms
2025-06-01 19:12:45.177 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 19:12:45.181 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 19:12:45.183 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 72.1485ms
2025-06-01 19:12:45.189 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 19:12:45.192 +04:00 [INF] Request GET /api/documents completed in 127ms with status 200 (Correlation ID: bb7f07fe-f651-46f7-b5f9-47595753adcc)
2025-06-01 19:12:45.195 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 160.3533ms
2025-06-01 19:14:20.702 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-01 19:14:20.724 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 38d4b172-59c7-4274-bd5e-1db7745933e0
2025-06-01 19:14:20.732 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:14:20.737 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 5ms with status 204 (Correlation ID: 38d4b172-59c7-4274-bd5e-1db7745933e0)
2025-06-01 19:14:20.744 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 41.6745ms
2025-06-01 19:14:20.786 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryiMmF2Rt9VGpVGo5b 473870
2025-06-01 19:14:20.799 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 7539e3cd-a0a4-4a9a-9f96-5e8dd8384ee8
2025-06-01 19:14:20.801 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:14:20.804 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:14:20.812 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 19:14:20.830 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:14:20.985 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 19:14:21.040 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 203.6955ms
2025-06-01 19:14:21.046 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 19:14:21.053 +04:00 [INF] Request POST /api/documents/upload completed in 251ms with status 415 (Correlation ID: 7539e3cd-a0a4-4a9a-9f96-5e8dd8384ee8)
2025-06-01 19:14:21.077 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 415 null application/json; charset=utf-8 290.2827ms
2025-06-01 19:18:24.868 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-01 19:18:24.968 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID bfe2eca6-a837-486d-b618-805e6e208765
2025-06-01 19:18:24.976 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:18:24.979 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 3ms with status 204 (Correlation ID: bfe2eca6-a837-486d-b618-805e6e208765)
2025-06-01 19:18:24.987 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 117.9937ms
2025-06-01 19:18:24.992 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryU7tHnTRBQAFQ1ARz 473870
2025-06-01 19:18:25.043 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 310712be-3757-44b7-a0dd-6003968c8532
2025-06-01 19:18:25.045 +04:00 [INF] CORS policy execution successful.
2025-06-01 19:18:25.047 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-01 19:18:25.049 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 19:18:25.050 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 19:18:25.072 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 19:18:25.077 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 23.6987ms
2025-06-01 19:18:25.081 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 19:18:25.083 +04:00 [INF] Request POST /api/documents/upload completed in 37ms with status 415 (Correlation ID: 310712be-3757-44b7-a0dd-6003968c8532)
2025-06-01 19:18:25.087 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 415 null application/json; charset=utf-8 94.969ms
