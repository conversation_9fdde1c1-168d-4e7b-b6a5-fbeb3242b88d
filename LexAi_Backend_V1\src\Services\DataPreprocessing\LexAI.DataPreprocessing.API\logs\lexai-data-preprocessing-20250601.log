2025-06-01 21:30:41.060 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-01 21:30:42.034 +04:00 [INF] Hangfire SQL objects installed.
2025-06-01 21:30:42.049 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-01 21:30:42.606 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-01 21:30:42.630 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 21:30:42.845 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-01 21:30:42.881 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-01 21:30:42.963 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-01 21:30:42.965 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-01 21:30:42.975 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-01 21:30:42.977 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-01 21:30:42.979 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-01 21:30:42.981 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-01 21:30:43.016 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 21:30:43.018 +04:00 [INF] Hosting environment: Development
2025-06-01 21:30:43.021 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-01 21:30:43.107 +04:00 [INF] Server datapreprocessing-kevin11:46268:8f01f6cb successfully announced in 67.814 ms
2025-06-01 21:30:43.130 +04:00 [INF] Server datapreprocessing-kevin11:46268:8f01f6cb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-01 21:30:43.326 +04:00 [INF] Server datapreprocessing-kevin11:46268:8f01f6cb all the dispatchers started
2025-06-01 21:30:46.776 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-01 21:30:47.098 +04:00 [INF] Request GET / started with correlation ID a72be432-61e4-4333-bb72-90a4c81e3f13
2025-06-01 21:30:47.254 +04:00 [INF] Request GET / completed in 148ms with status 404 (Correlation ID: a72be432-61e4-4333-bb72-90a4c81e3f13)
2025-06-01 21:30:47.269 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 494.9457ms
2025-06-01 21:30:47.284 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-01 21:32:02.144 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-01 21:32:02.157 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID bb51280e-e9cd-46ff-9d0c-3a873d2f5f84
2025-06-01 21:32:02.166 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:32:02.171 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 10ms with status 204 (Correlation ID: bb51280e-e9cd-46ff-9d0c-3a873d2f5f84)
2025-06-01 21:32:02.176 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 32.1963ms
2025-06-01 21:32:02.179 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryth2kIZPgE2MEyB5K 473843
2025-06-01 21:32:02.192 +04:00 [INF] Request POST /api/documents/upload started with correlation ID a60f4a8b-3964-472f-bee7-b8e8645db933
2025-06-01 21:32:02.196 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:32:02.330 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 4:11:41 PM', Current time (UTC): '6/1/2025 5:32:02 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-01 21:32:02.338 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 4:11:41 PM', Current time (UTC): '6/1/2025 5:32:02 PM'.
2025-06-01 21:32:02.344 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 4:11:41 PM', Current time (UTC): '6/1/2025 5:32:02 PM'.
2025-06-01 21:32:02.358 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-01 21:32:02.370 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-01 21:32:02.373 +04:00 [INF] Request POST /api/documents/upload completed in 178ms with status 401 (Correlation ID: a60f4a8b-3964-472f-bee7-b8e8645db933)
2025-06-01 21:32:02.381 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 401 0 null 201.6703ms
2025-06-01 21:34:01.324 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 21:34:01.343 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 21:34:01.357 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID c32fd86e-0e9a-4a08-abc5-cc73b59e278b
2025-06-01 21:34:01.373 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 6ff8fe31-7000-4059-9142-424ef00c633d
2025-06-01 21:34:01.375 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:34:01.380 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:34:01.382 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: c32fd86e-0e9a-4a08-abc5-cc73b59e278b)
2025-06-01 21:34:01.384 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: 6ff8fe31-7000-4059-9142-424ef00c633d)
2025-06-01 21:34:01.388 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 64.003ms
2025-06-01 21:34:01.395 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 21:34:01.401 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 57.3559ms
2025-06-01 21:34:01.431 +04:00 [INF] Request GET /api/documents started with correlation ID eb979b0a-642a-47b1-8e75-e453e2d8fa67
2025-06-01 21:34:01.443 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:34:01.468 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:34:01.473 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:34:01.526 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:34:03.442 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 21:34:03.451 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 21:34:03.453 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 21:34:03.459 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 21:34:03.461 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 21:34:03.464 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-01 21:34:05.343 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 21:34:05.594 +04:00 [INF] Executed DbCommand (20ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 21:34:05.634 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 21:34:05.726 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 21:34:05.728 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 4188.3795ms
2025-06-01 21:34:05.731 +04:00 [INF] Request GET /api/documents started with correlation ID 8b2e2d5d-7f74-4804-b255-501f4c44f1ec
2025-06-01 21:34:05.733 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:34:05.734 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:34:05.738 +04:00 [INF] Request GET /api/documents completed in 4294ms with status 200 (Correlation ID: eb979b0a-642a-47b1-8e75-e453e2d8fa67)
2025-06-01 21:34:05.738 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:34:05.745 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:34:05.748 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:34:05.752 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 4357.0285ms
2025-06-01 21:34:05.792 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 21:34:05.796 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 21:34:05.799 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 48.9824ms
2025-06-01 21:34:05.800 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:34:05.802 +04:00 [INF] Request GET /api/documents completed in 67ms with status 200 (Correlation ID: 8b2e2d5d-7f74-4804-b255-501f4c44f1ec)
2025-06-01 21:34:05.805 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 79.1761ms
2025-06-01 21:35:10.626 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-01 21:35:10.666 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 991313a5-cf3a-4ff1-9148-fdc59f567529
2025-06-01 21:35:10.672 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:35:10.675 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 2ms with status 204 (Correlation ID: 991313a5-cf3a-4ff1-9148-fdc59f567529)
2025-06-01 21:35:10.682 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 56.4152ms
2025-06-01 21:35:10.689 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryOoplE04Yt2sagSm4 473843
2025-06-01 21:35:10.778 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 39c3dff8-2d34-4a7d-8b30-dfec444bf7f9
2025-06-01 21:35:10.782 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:35:10.787 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:35:10.792 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 21:35:10.808 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:35:11.025 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-01 21:35:11.059 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-01 21:35:11.099 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 21:35:11.141 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-01 21:35:11.170 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> 22853a12-fb16-46ba-86cd-c048d3dd6f12.pdf
2025-06-01 21:35:12.046 +04:00 [INF] Executed DbCommand (42ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-01 21:35:12.158 +04:00 [INF] Document added successfully: "9af3c1d0-f604-40b1-81f2-1694b11b22f8"
2025-06-01 21:35:12.161 +04:00 [INF] Document uploaded successfully: "9af3c1d0-f604-40b1-81f2-1694b11b22f8" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-01 21:35:12.168 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "9af3c1d0-f604-40b1-81f2-1694b11b22f8", Processing started: false
2025-06-01 21:35:12.173 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-01 21:35:12.188 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 1374.3745ms
2025-06-01 21:35:12.195 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-01 21:35:12.197 +04:00 [INF] Request POST /api/documents/upload completed in 1415ms with status 200 (Correlation ID: 39c3dff8-2d34-4a7d-8b30-dfec444bf7f9)
2025-06-01 21:35:12.203 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 1514.622ms
2025-06-01 21:39:16.136 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 21:39:16.155 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 21:39:16.162 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 6160e119-ff43-4b19-894b-b3bfcdeae747
2025-06-01 21:39:16.173 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 00513419-126f-4d73-bfd9-b3694edd4d2d
2025-06-01 21:39:16.178 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:39:16.182 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:39:16.184 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: 6160e119-ff43-4b19-894b-b3bfcdeae747)
2025-06-01 21:39:16.188 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: 00513419-126f-4d73-bfd9-b3694edd4d2d)
2025-06-01 21:39:16.195 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 58.1565ms
2025-06-01 21:39:16.227 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 71.8898ms
2025-06-01 21:39:16.221 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 21:39:16.282 +04:00 [INF] Request GET /api/documents started with correlation ID 4989740e-a7ce-4dae-91b6-a40896b77f16
2025-06-01 21:39:16.293 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:39:16.297 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:39:16.305 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:39:16.312 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:39:16.399 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 21:39:16.611 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 21:39:16.646 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 323.5827ms
2025-06-01 21:39:16.654 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 21:39:16.656 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:39:16.667 +04:00 [INF] Request GET /api/documents started with correlation ID 33855038-76ba-434d-a632-b20d3032cf37
2025-06-01 21:39:16.673 +04:00 [INF] Request GET /api/documents completed in 380ms with status 200 (Correlation ID: 4989740e-a7ce-4dae-91b6-a40896b77f16)
2025-06-01 21:39:16.678 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:39:16.686 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 464.9233ms
2025-06-01 21:39:16.689 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:39:16.709 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:39:16.714 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:39:16.735 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 21:39:16.748 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 21:39:16.756 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 35.3381ms
2025-06-01 21:39:16.763 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:39:16.769 +04:00 [INF] Request GET /api/documents completed in 91ms with status 200 (Correlation ID: 33855038-76ba-434d-a632-b20d3032cf37)
2025-06-01 21:39:16.780 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 126.7175ms
2025-06-01 21:39:56.286 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - null null
2025-06-01 21:39:56.290 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - null null
2025-06-01 21:39:56.330 +04:00 [INF] Request OPTIONS /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 started with correlation ID 980ccc0b-7977-4956-912c-81bfec5e26c8
2025-06-01 21:39:56.348 +04:00 [INF] Request OPTIONS /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 started with correlation ID 64a6ffd6-9e30-4224-88c3-8afb72f61cac
2025-06-01 21:39:56.356 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:39:56.363 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:39:56.366 +04:00 [INF] Request OPTIONS /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 completed in 10ms with status 204 (Correlation ID: 980ccc0b-7977-4956-912c-81bfec5e26c8)
2025-06-01 21:39:56.368 +04:00 [INF] Request OPTIONS /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 completed in 4ms with status 204 (Correlation ID: 64a6ffd6-9e30-4224-88c3-8afb72f61cac)
2025-06-01 21:39:56.373 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - 204 null null 87.0794ms
2025-06-01 21:39:56.383 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - application/json null
2025-06-01 21:39:56.389 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - 204 null null 98.3068ms
2025-06-01 21:39:56.410 +04:00 [INF] Request GET /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 started with correlation ID 3903c710-1228-4fef-b59b-7d901743090d
2025-06-01 21:39:56.416 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:39:56.418 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:39:56.419 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-01 21:39:56.428 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:39:56.459 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 21:39:56.493 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-01 21:39:56.538 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-01 21:39:56.544 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 112.3597ms
2025-06-01 21:39:56.551 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - application/json null
2025-06-01 21:39:56.554 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-01 21:39:56.565 +04:00 [INF] Request GET /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 started with correlation ID 476ae644-5485-48dd-a6ba-a20bcf320844
2025-06-01 21:39:56.570 +04:00 [INF] Request GET /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 completed in 154ms with status 200 (Correlation ID: 3903c710-1228-4fef-b59b-7d901743090d)
2025-06-01 21:39:56.579 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:39:56.588 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - 200 null application/json; charset=utf-8 204.5579ms
2025-06-01 21:39:56.589 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:39:56.599 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-01 21:39:56.601 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:39:56.617 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-01 21:39:56.623 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-01 21:39:56.627 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 22.3295ms
2025-06-01 21:39:56.630 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-01 21:39:56.632 +04:00 [INF] Request GET /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 completed in 53ms with status 200 (Correlation ID: 476ae644-5485-48dd-a6ba-a20bcf320844)
2025-06-01 21:39:56.636 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - 200 null application/json; charset=utf-8 84.2787ms
2025-06-01 21:40:56.091 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 21:40:56.091 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 21:40:56.105 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 598383be-3958-4a91-b219-b276efd9c746
2025-06-01 21:40:56.108 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 3d75eb41-1186-4b20-8194-b7b621ee71aa
2025-06-01 21:40:56.110 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:40:56.112 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:40:56.114 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 598383be-3958-4a91-b219-b276efd9c746)
2025-06-01 21:40:56.115 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: 3d75eb41-1186-4b20-8194-b7b621ee71aa)
2025-06-01 21:40:56.117 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 26.202ms
2025-06-01 21:40:56.157 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 21:40:56.178 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 87.0272ms
2025-06-01 21:40:56.206 +04:00 [INF] Request GET /api/documents started with correlation ID 65ff7d41-24cf-45bb-8933-47c1011a4afc
2025-06-01 21:40:56.226 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:40:56.230 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:40:56.236 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:40:56.240 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:40:56.281 +04:00 [INF] Executed DbCommand (14ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 21:40:56.312 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 21:40:56.324 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 72.8207ms
2025-06-01 21:40:56.329 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 21:40:56.335 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:40:56.344 +04:00 [INF] Request GET /api/documents started with correlation ID f782dc52-e62e-4dd2-aed7-fd496fddd158
2025-06-01 21:40:56.345 +04:00 [INF] Request GET /api/documents completed in 119ms with status 200 (Correlation ID: 65ff7d41-24cf-45bb-8933-47c1011a4afc)
2025-06-01 21:40:56.350 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:40:56.358 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 201.2534ms
2025-06-01 21:40:56.360 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:40:56.378 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:40:56.385 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:40:56.408 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 21:40:56.422 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 21:40:56.426 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 32.7586ms
2025-06-01 21:40:56.428 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:40:56.431 +04:00 [INF] Request GET /api/documents completed in 80ms with status 200 (Correlation ID: f782dc52-e62e-4dd2-aed7-fd496fddd158)
2025-06-01 21:40:56.434 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 105.2419ms
2025-06-01 21:42:05.831 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 21:42:05.868 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 21:42:05.873 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 82e8479a-e85c-4765-8619-9ddde41dff9e
2025-06-01 21:42:05.880 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 180e7448-9b4d-464a-80f9-817b037a7693
2025-06-01 21:42:05.883 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:05.886 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:05.887 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 82e8479a-e85c-4765-8619-9ddde41dff9e)
2025-06-01 21:42:05.888 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: 180e7448-9b4d-464a-80f9-817b037a7693)
2025-06-01 21:42:05.892 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 60.7307ms
2025-06-01 21:42:05.898 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 29.5948ms
2025-06-01 21:42:05.895 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 21:42:05.934 +04:00 [INF] Request GET /api/documents started with correlation ID a91f1d42-ebd3-4fe5-9707-cbdcc23f4d31
2025-06-01 21:42:05.938 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:05.942 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:42:05.945 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:05.947 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:42:05.959 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 21:42:05.967 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 21:42:05.970 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 19.0781ms
2025-06-01 21:42:05.972 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:05.974 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 21:42:05.976 +04:00 [INF] Request GET /api/documents completed in 38ms with status 200 (Correlation ID: a91f1d42-ebd3-4fe5-9707-cbdcc23f4d31)
2025-06-01 21:42:05.980 +04:00 [INF] Request GET /api/documents started with correlation ID c731fbc6-c3a7-46c3-9dfb-36e0ad37ff55
2025-06-01 21:42:05.983 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 87.9604ms
2025-06-01 21:42:05.987 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:05.993 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:42:05.994 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:05.996 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:42:06.021 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 21:42:06.041 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 21:42:06.046 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 39.8299ms
2025-06-01 21:42:06.049 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:06.051 +04:00 [INF] Request GET /api/documents completed in 64ms with status 200 (Correlation ID: c731fbc6-c3a7-46c3-9dfb-36e0ad37ff55)
2025-06-01 21:42:06.055 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 81.5485ms
2025-06-01 21:42:09.378 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - null null
2025-06-01 21:42:09.379 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - null null
2025-06-01 21:42:09.387 +04:00 [INF] Request OPTIONS /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 started with correlation ID cff4d579-7f3e-4fc2-aeb7-5f9d14b31937
2025-06-01 21:42:09.391 +04:00 [INF] Request OPTIONS /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 started with correlation ID 45aba4a5-6f43-4211-a932-c68c095260aa
2025-06-01 21:42:09.392 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:09.395 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:09.397 +04:00 [INF] Request OPTIONS /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 completed in 4ms with status 204 (Correlation ID: cff4d579-7f3e-4fc2-aeb7-5f9d14b31937)
2025-06-01 21:42:09.398 +04:00 [INF] Request OPTIONS /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 completed in 3ms with status 204 (Correlation ID: 45aba4a5-6f43-4211-a932-c68c095260aa)
2025-06-01 21:42:09.401 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - 204 null null 22.888ms
2025-06-01 21:42:09.404 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - application/json null
2025-06-01 21:42:09.404 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - 204 null null 25.5141ms
2025-06-01 21:42:09.414 +04:00 [INF] Request GET /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 started with correlation ID 4616271b-109c-4cc6-8bf8-728498b13401
2025-06-01 21:42:09.420 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:09.421 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:42:09.423 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:09.424 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:42:09.435 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-01 21:42:09.442 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-01 21:42:09.444 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 16.7835ms
2025-06-01 21:42:09.449 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:09.450 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - application/json null
2025-06-01 21:42:09.451 +04:00 [INF] Request GET /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 completed in 31ms with status 200 (Correlation ID: 4616271b-109c-4cc6-8bf8-728498b13401)
2025-06-01 21:42:09.456 +04:00 [INF] Request GET /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 started with correlation ID bb5b46dc-a000-4c10-9022-1ffe515c9024
2025-06-01 21:42:09.460 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - 200 null application/json; charset=utf-8 56.2631ms
2025-06-01 21:42:09.463 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:09.479 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:42:09.484 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:09.492 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:42:09.518 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-01 21:42:09.525 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-01 21:42:09.529 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 22.5737ms
2025-06-01 21:42:09.531 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:09.532 +04:00 [INF] Request GET /api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 completed in 69ms with status 200 (Correlation ID: bb5b46dc-a000-4c10-9022-1ffe515c9024)
2025-06-01 21:42:09.535 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/9af3c1d0-f604-40b1-81f2-1694b11b22f8 - 200 null application/json; charset=utf-8 85.5665ms
2025-06-01 21:42:21.637 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 21:42:21.639 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-01 21:42:21.646 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID d9472968-316b-478b-a4a8-fbbfc30fe043
2025-06-01 21:42:21.652 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID c88dfbad-c034-4d4f-88f2-073cf966a6cb
2025-06-01 21:42:21.656 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:21.658 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:21.660 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: d9472968-316b-478b-a4a8-fbbfc30fe043)
2025-06-01 21:42:21.663 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: c88dfbad-c034-4d4f-88f2-073cf966a6cb)
2025-06-01 21:42:21.668 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 31.0635ms
2025-06-01 21:42:21.670 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 21:42:21.672 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 32.9211ms
2025-06-01 21:42:21.700 +04:00 [INF] Request GET /api/documents started with correlation ID 6cac2d4a-82e6-4a3a-9d71-e48fe9bea2ab
2025-06-01 21:42:21.712 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:21.715 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:42:21.716 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:21.717 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:42:21.727 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 21:42:21.733 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 21:42:21.735 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 15.1702ms
2025-06-01 21:42:21.737 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-01 21:42:21.737 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:21.742 +04:00 [INF] Request GET /api/documents started with correlation ID 95ea3df4-3d90-4789-85ec-ade98890efcd
2025-06-01 21:42:21.743 +04:00 [INF] Request GET /api/documents completed in 30ms with status 200 (Correlation ID: 6cac2d4a-82e6-4a3a-9d71-e48fe9bea2ab)
2025-06-01 21:42:21.746 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:42:21.750 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 79.5136ms
2025-06-01 21:42:21.751 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-01 21:42:21.758 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:21.760 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-01 21:42:21.771 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-01 21:42:21.778 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-01 21:42:21.782 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 18.0118ms
2025-06-01 21:42:21.791 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-01 21:42:21.794 +04:00 [INF] Request GET /api/documents completed in 47ms with status 200 (Correlation ID: 95ea3df4-3d90-4789-85ec-ade98890efcd)
2025-06-01 21:42:21.797 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 60.816ms
