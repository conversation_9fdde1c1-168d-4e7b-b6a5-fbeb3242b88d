using LexAI.AIAssistant.Domain.ValueObjects;
using Xunit;

namespace LexAI.AIAssistant.Domain.Tests.ValueObjects;

public class CitationTests
{
    [Fact]
    public void Create_WithValidData_ShouldCreateCitation()
    {
        // Arrange
        var type = CitationType.LegalDocument;
        var title = "Code Civil";
        var url = "https://www.legifrance.gouv.fr/codes/texte_lc/LEGITEXT000006070721";
        var source = "Légifrance";
        var relevanceScore = 0.85;

        // Act
        var citation = Citation.Create(type, title, url, source, relevanceScore);

        // Assert
        Assert.NotEqual(Guid.Empty, citation.Id);
        Assert.Equal(type, citation.Type);
        Assert.Equal(title, citation.Title);
        Assert.Equal(url, citation.Url);
        Assert.Equal(source, citation.Source);
        Assert.Equal(relevanceScore, citation.RelevanceScore);
    }

    [Fact]
    public void Create_WithEmptyTitle_ShouldThrowArgumentException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => 
            Citation.Create(CitationType.LegalDocument, "", "url", "source"));
    }

    [Fact]
    public void Create_WithEmptySource_ShouldThrowArgumentException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => 
            Citation.Create(CitationType.LegalDocument, "title", "url", ""));
    }

    [Fact]
    public void Create_WithInvalidRelevanceScore_ShouldThrowArgumentException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => 
            Citation.Create(CitationType.LegalDocument, "title", "url", "source", -0.1));
        
        Assert.Throws<ArgumentException>(() => 
            Citation.Create(CitationType.LegalDocument, "title", "url", "source", 1.1));
    }

    [Fact]
    public void CreateLegalDocument_WithValidData_ShouldCreateLegalDocumentCitation()
    {
        // Arrange
        var title = "Décret n° 2020-1310";
        var url = "https://www.legifrance.gouv.fr/jorf/id/JORFTEXT000042528531";
        var source = "Journal Officiel";
        var jurisdiction = "France";
        var relevanceScore = 0.9;

        // Act
        var citation = Citation.CreateLegalDocument(title, url, source, jurisdiction, relevanceScore);

        // Assert
        Assert.Equal(CitationType.LegalDocument, citation.Type);
        Assert.Equal(title, citation.Title);
        Assert.Equal(jurisdiction, citation.Jurisdiction);
    }

    [Fact]
    public void CreateCaseLaw_WithValidData_ShouldCreateCaseLawCitation()
    {
        // Arrange
        var title = "Cour de cassation, civile, Chambre civile 1, 15 avril 2021, 19-20.321";
        var url = "https://www.legifrance.gouv.fr/juri/id/JURITEXT000043123456";
        var court = "Cour de cassation";
        var caseNumber = "19-20.321";
        var jurisdiction = "France";
        var relevanceScore = 0.95;

        // Act
        var citation = Citation.CreateCaseLaw(title, url, court, caseNumber, jurisdiction, relevanceScore);

        // Assert
        Assert.Equal(CitationType.CaseLaw, citation.Type);
        Assert.Equal(title, citation.Title);
        Assert.Equal(court, citation.Court);
        Assert.Equal(court, citation.Source); // Court becomes the source
        Assert.Equal(caseNumber, citation.CaseNumber);
        Assert.Equal(jurisdiction, citation.Jurisdiction);
    }

    [Fact]
    public void SetPublicationInfo_WithValidData_ShouldSetPublicationInfo()
    {
        // Arrange
        var citation = Citation.Create(CitationType.LegalDocument, "title", "url", "source");
        var publicationDate = new DateTime(2021, 4, 15);
        var authors = "Jean Dupont, Marie Martin";
        var pageReference = "123-145";

        // Act
        citation.SetPublicationInfo(publicationDate, authors, pageReference);

        // Assert
        Assert.Equal(publicationDate, citation.PublicationDate);
        Assert.Equal(authors, citation.Authors);
        Assert.Equal(pageReference, citation.PageReference);
    }

    [Fact]
    public void SetExcerpt_WithValidText_ShouldSetExcerpt()
    {
        // Arrange
        var citation = Citation.Create(CitationType.LegalDocument, "title", "url", "source");
        var excerpt = "Ceci est un extrait du document juridique qui contient des informations importantes.";

        // Act
        citation.SetExcerpt(excerpt);

        // Assert
        Assert.Equal(excerpt, citation.Excerpt);
    }

    [Fact]
    public void SetExcerpt_WithLongText_ShouldTruncateExcerpt()
    {
        // Arrange
        var citation = Citation.Create(CitationType.LegalDocument, "title", "url", "source");
        var longExcerpt = new string('A', 600); // 600 caractères
        var maxLength = 500;

        // Act
        citation.SetExcerpt(longExcerpt, maxLength);

        // Assert
        Assert.Equal(maxLength, citation.Excerpt!.Length);
        Assert.EndsWith("...", citation.Excerpt);
    }

    [Fact]
    public void SetExcerpt_WithEmptyText_ShouldSetExcerptToNull()
    {
        // Arrange
        var citation = Citation.Create(CitationType.LegalDocument, "title", "url", "source");

        // Act
        citation.SetExcerpt("");

        // Assert
        Assert.Null(citation.Excerpt);
    }

    [Fact]
    public void AddMetadata_WithValidKeyValue_ShouldAddMetadata()
    {
        // Arrange
        var citation = Citation.Create(CitationType.LegalDocument, "title", "url", "source");
        var key = "customKey";
        var value = "customValue";

        // Act
        citation.AddMetadata(key, value);

        // Assert
        Assert.Equal(value, citation.GetMetadata<string>(key));
    }

    [Fact]
    public void AddMetadata_WithEmptyKey_ShouldThrowArgumentException()
    {
        // Arrange
        var citation = Citation.Create(CitationType.LegalDocument, "title", "url", "source");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => 
            citation.AddMetadata("", "value"));
    }

    [Fact]
    public void IsLegalCitation_WithLegalTypes_ShouldReturnTrue()
    {
        // Arrange
        var legalDocument = Citation.Create(CitationType.LegalDocument, "title", "url", "source");
        var caseLaw = Citation.Create(CitationType.CaseLaw, "title", "url", "source");
        var statute = Citation.Create(CitationType.Statute, "title", "url", "source");
        var regulation = Citation.Create(CitationType.Regulation, "title", "url", "source");

        // Act & Assert
        Assert.True(legalDocument.IsLegalCitation());
        Assert.True(caseLaw.IsLegalCitation());
        Assert.True(statute.IsLegalCitation());
        Assert.True(regulation.IsLegalCitation());
    }

    [Fact]
    public void IsLegalCitation_WithNonLegalTypes_ShouldReturnFalse()
    {
        // Arrange
        var academicPaper = Citation.Create(CitationType.AcademicPaper, "title", "url", "source");
        var newsArticle = Citation.Create(CitationType.NewsArticle, "title", "url", "source");

        // Act & Assert
        Assert.False(academicPaper.IsLegalCitation());
        Assert.False(newsArticle.IsLegalCitation());
    }

    [Fact]
    public void GetFormattedCitation_WithCompleteInfo_ShouldReturnFormattedString()
    {
        // Arrange
        var citation = Citation.Create(CitationType.LegalDocument, "Code Civil", "url", "Légifrance");
        citation.SetPublicationInfo(new DateTime(2021, 1, 1), "Législateur français", "Art. 1234");

        // Act
        var formatted = citation.GetFormattedCitation();

        // Assert
        Assert.Contains("Législateur français", formatted);
        Assert.Contains("Code Civil", formatted);
        Assert.Contains("Légifrance", formatted);
        Assert.Contains("2021", formatted);
        Assert.Contains("p. Art. 1234", formatted);
    }

    [Fact]
    public void Equality_WithSameValues_ShouldBeEqual()
    {
        // Arrange
        var id = Guid.NewGuid();
        var citation1 = Citation.Create(CitationType.LegalDocument, "title", "url", "source");
        var citation2 = Citation.Create(CitationType.LegalDocument, "title", "url", "source");

        // Utiliser la réflexion pour définir le même ID (car c'est privé)
        var idProperty = typeof(Citation).GetProperty("Id");
        idProperty!.SetValue(citation1, id);
        idProperty!.SetValue(citation2, id);

        // Act & Assert
        Assert.Equal(citation1, citation2);
    }

    [Fact]
    public void Equality_WithDifferentValues_ShouldNotBeEqual()
    {
        // Arrange
        var citation1 = Citation.Create(CitationType.LegalDocument, "title1", "url", "source");
        var citation2 = Citation.Create(CitationType.LegalDocument, "title2", "url", "source");

        // Act & Assert
        Assert.NotEqual(citation1, citation2);
    }
}
