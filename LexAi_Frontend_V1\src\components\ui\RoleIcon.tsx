import { Shield, Users } from 'lucide-react'
import { UserRole } from '../../types'

interface RoleIconProps {
  role: UserRole | number
  className?: string
}

export function RoleIcon({ role, className = 'h-4 w-4' }: RoleIconProps) {
  const roleValue = typeof role === 'number' ? role : role

  const getIconAndColor = () => {
    switch (roleValue) {
      case UserRole.Administrator:
      case 1:
        return { Icon: Shield, color: 'text-red-500' }
      case UserRole.SeniorLawyer:
      case 2:
        return { Icon: Users, color: 'text-purple-500' }
      case UserRole.Lawyer:
      case 3:
        return { Icon: Users, color: 'text-blue-500' }
      case UserRole.LegalAssistant:
      case 4:
        return { Icon: Users, color: 'text-green-500' }
      case UserRole.Client:
      case 5:
        return { Icon: Users, color: 'text-gray-500' }
      case UserRole.Guest:
      case 6:
        return { Icon: Users, color: 'text-yellow-500' }
      default:
        return { Icon: Users, color: 'text-gray-500' }
    }
  }

  const { Icon, color } = getIconAndColor()

  return <Icon className={`${className} ${color}`} />
}
