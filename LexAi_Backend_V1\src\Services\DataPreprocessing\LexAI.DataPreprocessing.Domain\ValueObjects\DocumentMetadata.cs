using LexAI.Shared.Domain.Common;

namespace LexAI.DataPreprocessing.Domain.ValueObjects;

/// <summary>
/// Document metadata value object
/// </summary>
public class DocumentMetadata : ValueObject
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; private set; }

    /// <summary>
    /// Original file name
    /// </summary>
    public string FileName { get; private set; }

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long FileSize { get; private set; }

    /// <summary>
    /// MIME type
    /// </summary>
    public string MimeType { get; private set; }

    /// <summary>
    /// Document language (ISO 639-1)
    /// </summary>
    public string? Language { get; private set; }

    /// <summary>
    /// Document title extracted from content
    /// </summary>
    public string? Title { get; private set; }

    /// <summary>
    /// Document author if available
    /// </summary>
    public string? Author { get; private set; }

    /// <summary>
    /// Document creation date if available
    /// </summary>
    public DateTime? DocumentCreatedAt { get; private set; }

    /// <summary>
    /// Document modification date if available
    /// </summary>
    public DateTime? DocumentModifiedAt { get; private set; }

    /// <summary>
    /// Number of pages in the document
    /// </summary>
    public int? PageCount { get; private set; }

    /// <summary>
    /// Number of words in the document
    /// </summary>
    public int? WordCount { get; private set; }

    /// <summary>
    /// Number of characters in the document
    /// </summary>
    public int? CharacterCount { get; private set; }

    /// <summary>
    /// Number of paragraphs in the document
    /// </summary>
    public int? ParagraphCount { get; private set; }

    /// <summary>
    /// Document tags
    /// </summary>
    public List<string> Tags { get; private set; }

    /// <summary>
    /// Document keywords
    /// </summary>
    public List<string> Keywords { get; private set; }

    /// <summary>
    /// Additional custom metadata
    /// </summary>
    public Dictionary<string, object> CustomMetadata { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private DocumentMetadata()
    {
        FileName = string.Empty;
        MimeType = string.Empty;
        Tags = new List<string>();
        Keywords = new List<string>();
        CustomMetadata = new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates document metadata
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="fileName">File name</param>
    /// <param name="fileSize">File size</param>
    /// <param name="mimeType">MIME type</param>
    /// <returns>Document metadata</returns>
    public static DocumentMetadata Create(Guid documentId, string fileName, long fileSize, string mimeType)
    {
        if (documentId == Guid.Empty)
            throw new ArgumentException("Document ID cannot be empty", nameof(documentId));

        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be empty", nameof(fileName));

        if (fileSize <= 0)
            throw new ArgumentException("File size must be positive", nameof(fileSize));

        if (string.IsNullOrWhiteSpace(mimeType))
            throw new ArgumentException("MIME type cannot be empty", nameof(mimeType));

        return new DocumentMetadata
        {
            DocumentId = documentId,
            FileName = fileName.Trim(),
            FileSize = fileSize,
            MimeType = mimeType.Trim().ToLowerInvariant(),
            Tags = new List<string>(),
            Keywords = new List<string>(),
            CustomMetadata = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// Updates metadata with text statistics
    /// </summary>
    /// <param name="text">Extracted text</param>
    /// <returns>Updated metadata</returns>
    public DocumentMetadata WithTextStatistics(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return this;

        var words = text.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
        var paragraphs = text.Split(new[] { "\n\n", "\r\n\r\n" }, StringSplitOptions.RemoveEmptyEntries);

        return new DocumentMetadata
        {
            DocumentId = DocumentId,
            FileName = FileName,
            FileSize = FileSize,
            MimeType = MimeType,
            Language = Language,
            Title = Title,
            Author = Author,
            DocumentCreatedAt = DocumentCreatedAt,
            DocumentModifiedAt = DocumentModifiedAt,
            PageCount = PageCount,
            WordCount = words.Length,
            CharacterCount = text.Length,
            ParagraphCount = paragraphs.Length,
            Tags = new List<string>(Tags),
            Keywords = new List<string>(Keywords),
            CustomMetadata = new Dictionary<string, object>(CustomMetadata)
        };
    }

    /// <summary>
    /// Updates metadata with document properties
    /// </summary>
    /// <param name="title">Document title</param>
    /// <param name="author">Document author</param>
    /// <param name="language">Document language</param>
    /// <param name="createdAt">Document creation date</param>
    /// <param name="modifiedAt">Document modification date</param>
    /// <param name="pageCount">Page count</param>
    /// <returns>Updated metadata</returns>
    public DocumentMetadata WithDocumentProperties(
        string? title = null,
        string? author = null,
        string? language = null,
        DateTime? createdAt = null,
        DateTime? modifiedAt = null,
        int? pageCount = null)
    {
        return new DocumentMetadata
        {
            DocumentId = DocumentId,
            FileName = FileName,
            FileSize = FileSize,
            MimeType = MimeType,
            Language = language?.Trim() ?? Language,
            Title = title?.Trim() ?? Title,
            Author = author?.Trim() ?? Author,
            DocumentCreatedAt = createdAt ?? DocumentCreatedAt,
            DocumentModifiedAt = modifiedAt ?? DocumentModifiedAt,
            PageCount = pageCount ?? PageCount,
            WordCount = WordCount,
            CharacterCount = CharacterCount,
            ParagraphCount = ParagraphCount,
            Tags = new List<string>(Tags),
            Keywords = new List<string>(Keywords),
            CustomMetadata = new Dictionary<string, object>(CustomMetadata)
        };
    }

    /// <summary>
    /// Adds tags to the metadata
    /// </summary>
    /// <param name="tags">Tags to add</param>
    /// <returns>Updated metadata</returns>
    public DocumentMetadata WithTags(params string[] tags)
    {
        var newTags = new List<string>(Tags);

        foreach (var tag in tags.Where(t => !string.IsNullOrWhiteSpace(t)))
        {
            var normalizedTag = tag.Trim().ToLowerInvariant();
            if (!newTags.Contains(normalizedTag))
            {
                newTags.Add(normalizedTag);
            }
        }

        return new DocumentMetadata
        {
            DocumentId = DocumentId,
            FileName = FileName,
            FileSize = FileSize,
            MimeType = MimeType,
            Language = Language,
            Title = Title,
            Author = Author,
            DocumentCreatedAt = DocumentCreatedAt,
            DocumentModifiedAt = DocumentModifiedAt,
            PageCount = PageCount,
            WordCount = WordCount,
            CharacterCount = CharacterCount,
            ParagraphCount = ParagraphCount,
            Tags = newTags,
            Keywords = new List<string>(Keywords),
            CustomMetadata = new Dictionary<string, object>(CustomMetadata)
        };
    }

    /// <summary>
    /// Adds keywords to the metadata
    /// </summary>
    /// <param name="keywords">Keywords to add</param>
    /// <returns>Updated metadata</returns>
    public DocumentMetadata WithKeywords(params string[] keywords)
    {
        var newKeywords = new List<string>(Keywords);

        foreach (var keyword in keywords.Where(k => !string.IsNullOrWhiteSpace(k)))
        {
            var normalizedKeyword = keyword.Trim().ToLowerInvariant();
            if (!newKeywords.Contains(normalizedKeyword))
            {
                newKeywords.Add(normalizedKeyword);
            }
        }

        return new DocumentMetadata
        {
            DocumentId = DocumentId,
            FileName = FileName,
            FileSize = FileSize,
            MimeType = MimeType,
            Language = Language,
            Title = Title,
            Author = Author,
            DocumentCreatedAt = DocumentCreatedAt,
            DocumentModifiedAt = DocumentModifiedAt,
            PageCount = PageCount,
            WordCount = WordCount,
            CharacterCount = CharacterCount,
            ParagraphCount = ParagraphCount,
            Tags = new List<string>(Tags),
            Keywords = newKeywords,
            CustomMetadata = new Dictionary<string, object>(CustomMetadata)
        };
    }

    /// <summary>
    /// Adds custom metadata
    /// </summary>
    /// <param name="key">Metadata key</param>
    /// <param name="value">Metadata value</param>
    /// <returns>Updated metadata</returns>
    public DocumentMetadata WithCustomMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        var newCustomMetadata = new Dictionary<string, object>(CustomMetadata)
        {
            [key] = value
        };

        return new DocumentMetadata
        {
            DocumentId = DocumentId,
            FileName = FileName,
            FileSize = FileSize,
            MimeType = MimeType,
            Language = Language,
            Title = Title,
            Author = Author,
            DocumentCreatedAt = DocumentCreatedAt,
            DocumentModifiedAt = DocumentModifiedAt,
            PageCount = PageCount,
            WordCount = WordCount,
            CharacterCount = CharacterCount,
            ParagraphCount = ParagraphCount,
            Tags = new List<string>(Tags),
            Keywords = new List<string>(Keywords),
            CustomMetadata = newCustomMetadata
        };
    }

    /// <summary>
    /// Gets a custom metadata value
    /// </summary>
    /// <typeparam name="T">Metadata type</typeparam>
    /// <param name="key">Metadata key</param>
    /// <param name="defaultValue">Default value if not found</param>
    /// <returns>Metadata value</returns>
    public T GetCustomMetadata<T>(string key, T defaultValue = default!)
    {
        if (CustomMetadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return DocumentId;
        yield return FileName;
        yield return FileSize;
        yield return MimeType;
        yield return Language ?? string.Empty;
        yield return Title ?? string.Empty;
        yield return Author ?? string.Empty;
        yield return WordCount ?? 0;
        yield return CharacterCount ?? 0;
    }
}

/// <summary>
/// Chunk metadata value object
/// </summary>
public class ChunkMetadata : ValueObject
{
    /// <summary>
    /// Chunk ID
    /// </summary>
    public Guid ChunkId { get; private set; }

    /// <summary>
    /// Chunk type
    /// </summary>
    public ChunkType Type { get; private set; }

    /// <summary>
    /// Token count
    /// </summary>
    public int TokenCount { get; private set; }

    /// <summary>
    /// Character count
    /// </summary>
    public int CharacterCount { get; private set; }

    /// <summary>
    /// Embedding model used
    /// </summary>
    public string? EmbeddingModel { get; private set; }

    /// <summary>
    /// Vector dimension
    /// </summary>
    public int? VectorDimension { get; private set; }

    /// <summary>
    /// Chunk language
    /// </summary>
    public string? Language { get; private set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> AdditionalMetadata { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private ChunkMetadata()
    {
        AdditionalMetadata = new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates chunk metadata
    /// </summary>
    /// <param name="chunkId">Chunk ID</param>
    /// <param name="type">Chunk type</param>
    /// <param name="tokenCount">Token count</param>
    /// <param name="characterCount">Character count</param>
    /// <returns>Chunk metadata</returns>
    public static ChunkMetadata Create(Guid chunkId, ChunkType type, int tokenCount, int characterCount)
    {
        if (chunkId == Guid.Empty)
            throw new ArgumentException("Chunk ID cannot be empty", nameof(chunkId));

        if (tokenCount < 0)
            throw new ArgumentException("Token count cannot be negative", nameof(tokenCount));

        if (characterCount < 0)
            throw new ArgumentException("Character count cannot be negative", nameof(characterCount));

        return new ChunkMetadata
        {
            ChunkId = chunkId,
            Type = type,
            TokenCount = tokenCount,
            CharacterCount = characterCount,
            AdditionalMetadata = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// Updates metadata with embedding information
    /// </summary>
    /// <param name="model">Embedding model</param>
    /// <param name="dimension">Vector dimension</param>
    /// <returns>Updated metadata</returns>
    public ChunkMetadata WithEmbedding(string model, int dimension)
    {
        if (string.IsNullOrWhiteSpace(model))
            throw new ArgumentException("Model cannot be empty", nameof(model));

        if (dimension <= 0)
            throw new ArgumentException("Dimension must be positive", nameof(dimension));

        return new ChunkMetadata
        {
            ChunkId = ChunkId,
            Type = Type,
            TokenCount = TokenCount,
            CharacterCount = CharacterCount,
            EmbeddingModel = model.Trim(),
            VectorDimension = dimension,
            Language = Language,
            AdditionalMetadata = new Dictionary<string, object>(AdditionalMetadata)
        };
    }

    /// <summary>
    /// Adds custom metadata
    /// </summary>
    /// <param name="key">Metadata key</param>
    /// <param name="value">Metadata value</param>
    /// <returns>Updated metadata</returns>
    public ChunkMetadata WithCustomMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        var newAdditionalMetadata = new Dictionary<string, object>(AdditionalMetadata)
        {
            [key] = value
        };

        return new ChunkMetadata
        {
            ChunkId = ChunkId,
            Type = Type,
            TokenCount = TokenCount,
            CharacterCount = CharacterCount,
            EmbeddingModel = EmbeddingModel,
            VectorDimension = VectorDimension,
            Language = Language,
            AdditionalMetadata = newAdditionalMetadata
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return ChunkId;
        yield return Type;
        yield return TokenCount;
        yield return CharacterCount;
        yield return EmbeddingModel ?? string.Empty;
        yield return VectorDimension ?? 0;
    }
}
