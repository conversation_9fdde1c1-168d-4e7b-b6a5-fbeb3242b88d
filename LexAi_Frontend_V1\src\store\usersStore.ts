import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { User, UserRole } from '../types/index'
import { usersApi, ApiException } from '../services/api'

interface UsersState {
  users: User[]
  selectedUser: User | null
  isLoading: boolean
  totalUsers: number
  currentPage: number
  pageSize: number
}

interface UsersStore extends UsersState {
  // Actions
  getUsers: (limit?: number, offset?: number) => Promise<void>
  getUser: (id: string) => Promise<User>
  createUser: (userData: any) => Promise<User>
  updateUser: (id: string, userData: any) => Promise<User>
  deleteUser: (id: string) => Promise<void>
  setSelectedUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  setPage: (page: number) => void
  clearUsers: () => void
}

export const useUsersStore = create<UsersStore>()(
  persist(
    (set, get) => ({
      // State
      users: [],
      selectedUser: null,
      isLoading: false,
      totalUsers: 0,
      currentPage: 1,
      pageSize: 20,

      // Actions
      getUsers: async (limit?: number, offset?: number) => {
        set({ isLoading: true })
        
        try {
          const response = await usersApi.getUsers(limit, offset)
          set({ 
            users: response.users || response, // Adapter selon la structure de réponse
            totalUsers: response.total || response.length,
            isLoading: false 
          })
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      getUser: async (id: string) => {
        set({ isLoading: true })
        
        try {
          const user = await usersApi.getUser(id)
          set({ selectedUser: user, isLoading: false })
          return user
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      createUser: async (userData: any) => {
        set({ isLoading: true })
        
        try {
          const newUser = await usersApi.createUser(userData)
          
          // Ajouter le nouvel utilisateur à la liste
          set(state => ({
            users: [newUser, ...state.users],
            totalUsers: state.totalUsers + 1,
            isLoading: false
          }))

          return newUser
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      updateUser: async (id: string, userData: any) => {
        set({ isLoading: true })
        
        try {
          const updatedUser = await usersApi.updateUser(id, userData)
          
          // Mettre à jour l'utilisateur dans la liste
          set(state => ({
            users: state.users.map(user => 
              user.id === id ? updatedUser : user
            ),
            selectedUser: state.selectedUser?.id === id ? updatedUser : state.selectedUser,
            isLoading: false
          }))

          return updatedUser
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      deleteUser: async (id: string) => {
        set({ isLoading: true })
        
        try {
          await usersApi.deleteUser(id)
          
          // Supprimer l'utilisateur de la liste
          set(state => ({
            users: state.users.filter(user => user.id !== id),
            selectedUser: state.selectedUser?.id === id ? null : state.selectedUser,
            totalUsers: state.totalUsers - 1,
            isLoading: false
          }))
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      setSelectedUser: (user: User | null) => {
        set({ selectedUser: user })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      setPage: (page: number) => {
        set({ currentPage: page })
      },

      clearUsers: () => {
        set({
          users: [],
          selectedUser: null,
          totalUsers: 0,
          currentPage: 1
        })
      }
    }),
    {
      name: 'users-store',
      partialize: (state) => ({
        users: state.users,
        totalUsers: state.totalUsers,
        currentPage: state.currentPage
      })
    }
  )
)
