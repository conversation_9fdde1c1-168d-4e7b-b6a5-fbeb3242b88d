import React from 'react';

interface ChatPanelProps {
  // Add props as needed (e.g., messages, onSend, etc.)
}

export const ChatPanel: React.FC<ChatPanelProps> = () => {
  return (
    <section className="flex flex-col h-full w-full bg-white dark:bg-gray-950">
      {/* Header */}
      <header className="p-4 border-b border-border flex items-center justify-between">
        <div className="font-bold text-lg">New Chat</div>
        {/* Add actions/settings if needed */}
      </header>
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* Example message */}
        <div className="flex items-start gap-2">
          <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700" />
          <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg max-w-xl">
            <div className="text-sm">Hello, how can I help you today?</div>
          </div>
        </div>
        {/* ...more messages */}
      </div>
      {/* Input */}
      <footer className="p-4 border-t border-border flex items-center gap-2">
        <input
          type="text"
          className="flex-1 rounded border border-border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
          placeholder="Type your message here"
        />
        <button className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90">Send</button>
      </footer>
    </section>
  );
};
