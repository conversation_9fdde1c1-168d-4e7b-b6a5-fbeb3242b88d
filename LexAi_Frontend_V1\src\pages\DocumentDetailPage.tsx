import React, { useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom'
import {
  ArrowLeft,
  FileText,
  Download,
  Trash2,
  Play,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Calendar,
  User,
  HardDrive,
  Tag,
  Eye
} from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card'
import { useDocumentsStore } from '../store/documentsStore'
import { DocumentStatus, ProcessingStatus } from '../types/index'
import { getStatusLabel, isProcessingStatus, isCompletedStatus, isFailedStatus, isPendingStatus } from '../utils/statusUtils'

export function DocumentDetailPage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  const {
    selectedDocument,
    getDocument,
    deleteDocument,
    processingResults,
    isLoading
  } = useDocumentsStore()

  useEffect(() => {
    if (id) {
      getDocument(id)
    }
  }, [id, getDocument])

  const handleDeleteDocument = async () => {
    if (!id) return

    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) {
      try {
        await deleteDocument(id)
        navigate('/documents')
      } catch (error) {
        console.error('Erreur lors de la suppression:', error)
      }
    }
  }

  const getStatusIcon = (status: DocumentStatus) => {
    if (isProcessingStatus(status)) {
      return <AlertCircle className="h-5 w-5 text-yellow-500" />
    }
    if (isCompletedStatus(status)) {
      return <CheckCircle className="h-5 w-5 text-green-500" />
    }
    if (isFailedStatus(status)) {
      return <XCircle className="h-5 w-5 text-red-500" />
    }
    if (isPendingStatus(status) || status === DocumentStatus.Uploaded) {
      return <Clock className="h-5 w-5 text-blue-500" />
    }
    return <FileText className="h-5 w-5 text-gray-500" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const processingResult = id ? processingResults[id] : null

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Chargement du document...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!selectedDocument) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h2 className="mt-4 text-xl font-semibold text-gray-900">Document non trouvé</h2>
              <p className="mt-2 text-gray-600">Le document demandé n'existe pas ou n'est pas accessible.</p>
              <Button className="mt-4" onClick={() => navigate('/documents')}>
                Retour aux documents
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/documents')}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {selectedDocument.metadata?.title || selectedDocument.fileName}
            </h1>
            <div className="flex items-center space-x-2 mt-1">
              {getStatusIcon(selectedDocument.status)}
              <span className="text-sm text-gray-600">
                {getStatusLabel(selectedDocument.status)}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {selectedDocument.status === DocumentStatus.Uploaded && (
            <Link to={`/documents/${selectedDocument.id}/process`}>
              <Button>
                <Play className="h-4 w-4 mr-2" />
                Traiter
              </Button>
            </Link>
          )}

          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Télécharger
          </Button>

          <Button
            variant="outline"
            onClick={handleDeleteDocument}
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Supprimer
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Informations principales */}
        <div className="lg:col-span-2 space-y-6">
          {/* Détails du fichier */}
          <Card>
            <CardHeader>
              <CardTitle>Détails du fichier</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nom original
                  </label>
                  <p className="text-sm text-gray-900">{selectedDocument.fileName}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Taille
                  </label>
                  <p className="text-sm text-gray-900">{formatFileSize(selectedDocument.fileSize)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type MIME
                  </label>
                  <p className="text-sm text-gray-900">{selectedDocument.mimeType}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date de téléchargement
                  </label>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <p className="text-sm text-gray-900">
                      {new Date(selectedDocument.createdAt).toLocaleDateString('fr-FR')} à{' '}
                      {new Date(selectedDocument.createdAt).toLocaleTimeString('fr-FR')}
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Téléchargé par
                  </label>
                  <div className="flex items-center space-x-1">
                    <User className="h-4 w-4 text-gray-400" />
                    <p className="text-sm text-gray-900">{selectedDocument.uploadedBy || 'Utilisateur'}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Métadonnées */}
          {selectedDocument.metadata && (
            <Card>
              <CardHeader>
                <CardTitle>Métadonnées</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedDocument.metadata.title && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Titre
                    </label>
                    <p className="text-sm text-gray-900">{selectedDocument.metadata.title}</p>
                  </div>
                )}

                {selectedDocument.metadata.author && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Auteur
                    </label>
                    <p className="text-sm text-gray-900">{selectedDocument.metadata.author}</p>
                  </div>
                )}

                {selectedDocument.metadata.subject && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Sujet
                    </label>
                    <p className="text-sm text-gray-900">{selectedDocument.metadata.subject}</p>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {selectedDocument.metadata.pageCount && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Pages
                      </label>
                      <p className="text-sm text-gray-900">{selectedDocument.metadata.pageCount}</p>
                    </div>
                  )}

                  {selectedDocument.metadata.wordCount && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Mots
                      </label>
                      <p className="text-sm text-gray-900">{selectedDocument.metadata.wordCount.toLocaleString()}</p>
                    </div>
                  )}

                  {selectedDocument.metadata.characterCount && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Caractères
                      </label>
                      <p className="text-sm text-gray-900">{selectedDocument.metadata.characterCount.toLocaleString()}</p>
                    </div>
                  )}
                </div>

                {selectedDocument.metadata.keywords && selectedDocument.metadata.keywords.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Mots-clés
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {selectedDocument.metadata.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                        >
                          <Tag className="h-3 w-3 mr-1" />
                          {keyword}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Résultats de traitement */}
          {processingResult && (
            <Card>
              <CardHeader>
                <CardTitle>Résultats du traitement</CardTitle>
                <CardDescription>
                  Informations sur le dernier traitement effectué
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-lg font-bold text-blue-600">
                      {processingResult.chunksCreated}
                    </div>
                    <div className="text-xs text-blue-800">Chunks créés</div>
                  </div>

                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-lg font-bold text-green-600">
                      {Math.round(processingResult.processingTimeMs / 1000)}s
                    </div>
                    <div className="text-xs text-green-800">Temps</div>
                  </div>

                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <div className="text-lg font-bold text-purple-600">
                      {processingResult.totalTokens.toLocaleString()}
                    </div>
                    <div className="text-xs text-purple-800">Tokens</div>
                  </div>

                  <div className="text-center p-3 bg-yellow-50 rounded-lg">
                    <div className="text-lg font-bold text-yellow-600">
                      ${processingResult.estimatedCost.toFixed(4)}
                    </div>
                    <div className="text-xs text-yellow-800">Coût</div>
                  </div>
                </div>

                {processingResult.vectorDatabasesUsed.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bases de données utilisées
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {processingResult.vectorDatabasesUsed.map((db, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800"
                        >
                          <HardDrive className="h-3 w-3 mr-1" />
                          {db}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Actions rapides */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {selectedDocument.status === DocumentStatus.Uploaded && (
                <Link to={`/documents/${selectedDocument.id}/process`}>
                  <Button className="w-full">
                    <Play className="h-4 w-4 mr-2" />
                    Traiter le document
                  </Button>
                </Link>
              )}

              <Button variant="outline" className="w-full">
                <Eye className="h-4 w-4 mr-2" />
                Prévisualiser
              </Button>

              <Button variant="outline" className="w-full">
                <Download className="h-4 w-4 mr-2" />
                Télécharger
              </Button>

              <Button
                variant="outline"
                className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={handleDeleteDocument}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Supprimer
              </Button>
            </CardContent>
          </Card>

          {/* Statut */}
          <Card>
            <CardHeader>
              <CardTitle>Statut</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-3">
                {getStatusIcon(selectedDocument.status)}
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {getStatusLabel(selectedDocument.status)}
                  </p>
                  <p className="text-xs text-gray-500">
                    {selectedDocument.status === DocumentStatus.Uploaded && 'Prêt pour le traitement'}
                    {isProcessingStatus(selectedDocument.status) && 'Traitement en cours...'}
                    {isCompletedStatus(selectedDocument.status) && 'Traitement terminé'}
                    {isFailedStatus(selectedDocument.status) && 'Erreur lors du traitement'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
