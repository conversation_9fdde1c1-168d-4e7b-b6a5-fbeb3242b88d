using LexAI.DataPreprocessing.Domain.ValueObjects;

namespace LexAI.DataPreprocessing.Application.DTOs;

/// <summary>
/// Document upload request DTO
/// </summary>
public class DocumentUploadRequestDto
{
    /// <summary>
    /// File name
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// File content (base64 encoded)
    /// </summary>
    public string FileContent { get; set; } = string.Empty;

    /// <summary>
    /// MIME type
    /// </summary>
    public string MimeType { get; set; } = string.Empty;

    /// <summary>
    /// Processing configuration
    /// </summary>
    public ProcessingConfigurationDto? Configuration { get; set; }

    /// <summary>
    /// User ID who uploaded the document
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Document upload response DTO
/// </summary>
public class DocumentUploadResponseDto
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// File name
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// File size
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// Upload status
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Storage path
    /// </summary>
    public string StoragePath { get; set; } = string.Empty;

    /// <summary>
    /// Processing started
    /// </summary>
    public bool ProcessingStarted { get; set; }

    /// <summary>
    /// Estimated processing time
    /// </summary>
    public TimeSpan? EstimatedProcessingTime { get; set; }

    /// <summary>
    /// Upload timestamp
    /// </summary>
    public DateTime UploadedAt { get; set; }
}

/// <summary>
/// Processing configuration DTO
/// </summary>
public class ProcessingConfigurationDto
{
    /// <summary>
    /// Chunking configuration
    /// </summary>
    public ChunkingConfigurationDto Chunking { get; set; } = new();

    /// <summary>
    /// Embedding model to use
    /// </summary>
    public EmbeddingModelType EmbeddingModel { get; set; } = EmbeddingModelType.OpenAISmall;

    /// <summary>
    /// Target vector databases
    /// </summary>
    public List<VectorDatabaseType> TargetDatabases { get; set; } = new();

    /// <summary>
    /// Whether to perform quality assurance
    /// </summary>
    public bool PerformQualityAssurance { get; set; } = true;

    /// <summary>
    /// Whether to extract named entities
    /// </summary>
    public bool ExtractNamedEntities { get; set; } = true;

    /// <summary>
    /// Whether to extract keywords
    /// </summary>
    public bool ExtractKeywords { get; set; } = true;

    /// <summary>
    /// Processing priority
    /// </summary>
    public ProcessingPriority Priority { get; set; } = ProcessingPriority.Normal;

    /// <summary>
    /// Custom processing options
    /// </summary>
    public Dictionary<string, object> CustomOptions { get; set; } = new();
}

/// <summary>
/// Chunking configuration DTO
/// </summary>
public class ChunkingConfigurationDto
{
    /// <summary>
    /// Chunking strategy
    /// </summary>
    public ChunkingStrategy Strategy { get; set; } = ChunkingStrategy.Semantic;

    /// <summary>
    /// Maximum chunk size
    /// </summary>
    public int MaxChunkSize { get; set; } = 1000;

    /// <summary>
    /// Overlap size
    /// </summary>
    public int OverlapSize { get; set; } = 200;

    /// <summary>
    /// Minimum chunk size
    /// </summary>
    public int MinChunkSize { get; set; } = 100;

    /// <summary>
    /// Preserve sentence boundaries
    /// </summary>
    public bool PreserveSentences { get; set; } = true;

    /// <summary>
    /// Preserve paragraph boundaries
    /// </summary>
    public bool PreserveParagraphs { get; set; } = true;

    /// <summary>
    /// Custom separators
    /// </summary>
    public List<string> CustomSeparators { get; set; } = new();
}

/// <summary>
/// Extraction result DTO
/// </summary>
public class ExtractionResultDto
{
    /// <summary>
    /// Extracted text
    /// </summary>
    public string ExtractedText { get; set; } = string.Empty;

    /// <summary>
    /// Extraction success
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Extraction time
    /// </summary>
    public TimeSpan ExtractionTime { get; set; }

    /// <summary>
    /// Agent used for extraction
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// Extraction confidence
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// Document metadata extracted
    /// </summary>
    public DocumentMetadataDto? Metadata { get; set; }

    /// <summary>
    /// Extraction errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Extraction warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Document metadata DTO
/// </summary>
public class DocumentMetadataDto
{
    /// <summary>
    /// Document title
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// Document author
    /// </summary>
    public string? Author { get; set; }

    /// <summary>
    /// Document language
    /// </summary>
    public string? Language { get; set; }

    /// <summary>
    /// Creation date
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Modification date
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Page count
    /// </summary>
    public int? PageCount { get; set; }

    /// <summary>
    /// Word count
    /// </summary>
    public int? WordCount { get; set; }

    /// <summary>
    /// Character count
    /// </summary>
    public int? CharacterCount { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> AdditionalMetadata { get; set; } = new();
}

/// <summary>
/// Classification result DTO
/// </summary>
public class ClassificationResultDto
{
    /// <summary>
    /// Detected legal domain
    /// </summary>
    public LegalDomain DetectedDomain { get; set; }

    /// <summary>
    /// Classification confidence
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// All domain scores
    /// </summary>
    public Dictionary<LegalDomain, double> DomainScores { get; set; } = new();

    /// <summary>
    /// Classification success
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Classification time
    /// </summary>
    public TimeSpan ClassificationTime { get; set; }

    /// <summary>
    /// Agent used for classification
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// Extracted keywords
    /// </summary>
    public List<string> Keywords { get; set; } = new();

    /// <summary>
    /// Named entities
    /// </summary>
    public List<NamedEntityDto> NamedEntities { get; set; } = new();

    /// <summary>
    /// Classification errors
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Named entity DTO
/// </summary>
public class NamedEntityDto
{
    /// <summary>
    /// Entity text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Entity type
    /// </summary>
    public EntityType Type { get; set; }

    /// <summary>
    /// Start position
    /// </summary>
    public int StartPosition { get; set; }

    /// <summary>
    /// End position
    /// </summary>
    public int EndPosition { get; set; }

    /// <summary>
    /// Confidence score
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Chunking result DTO
/// </summary>
public class ChunkingResultDto
{
    /// <summary>
    /// Created chunks
    /// </summary>
    public List<DocumentChunkDto> Chunks { get; set; } = new();

    /// <summary>
    /// Chunking success
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Chunking time
    /// </summary>
    public TimeSpan ChunkingTime { get; set; }

    /// <summary>
    /// Agent used for chunking
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// Chunking strategy used
    /// </summary>
    public ChunkingStrategy Strategy { get; set; }

    /// <summary>
    /// Total chunks created
    /// </summary>
    public int TotalChunks { get; set; }

    /// <summary>
    /// Average chunk size
    /// </summary>
    public int AverageChunkSize { get; set; }

    /// <summary>
    /// Chunking errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Chunking warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Document chunk DTO
/// </summary>
public class DocumentChunkDto
{
    /// <summary>
    /// Chunk ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Sequence number
    /// </summary>
    public int SequenceNumber { get; set; }

    /// <summary>
    /// Chunk content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Chunk type
    /// </summary>
    public ChunkType Type { get; set; }

    /// <summary>
    /// Start position
    /// </summary>
    public int StartPosition { get; set; }

    /// <summary>
    /// End position
    /// </summary>
    public int EndPosition { get; set; }

    /// <summary>
    /// Token count
    /// </summary>
    public int TokenCount { get; set; }

    /// <summary>
    /// Character count
    /// </summary>
    public int CharacterCount { get; set; }

    /// <summary>
    /// Quality score
    /// </summary>
    public double QualityScore { get; set; }

    /// <summary>
    /// Importance score
    /// </summary>
    public double ImportanceScore { get; set; }

    /// <summary>
    /// Keywords
    /// </summary>
    public List<string> Keywords { get; set; } = new();

    /// <summary>
    /// Named entities
    /// </summary>
    public List<NamedEntityDto> NamedEntities { get; set; } = new();

    /// <summary>
    /// Domain relevance scores
    /// </summary>
    public Dictionary<LegalDomain, double> DomainRelevance { get; set; } = new();

    /// <summary>
    /// Whether vectorized
    /// </summary>
    public bool IsVectorized { get; set; }

    /// <summary>
    /// Vector ID
    /// </summary>
    public string? VectorId { get; set; }
}

/// <summary>
/// Vectorization result DTO
/// </summary>
public class VectorizationResultDto
{
    /// <summary>
    /// Vectorized chunks
    /// </summary>
    public List<VectorizedChunkDto> VectorizedChunks { get; set; } = new();

    /// <summary>
    /// Vectorization success
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Vectorization time
    /// </summary>
    public TimeSpan VectorizationTime { get; set; }

    /// <summary>
    /// Agent used for vectorization
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// Embedding model used
    /// </summary>
    public EmbeddingModelType EmbeddingModel { get; set; }

    /// <summary>
    /// Vector dimension
    /// </summary>
    public int VectorDimension { get; set; }

    /// <summary>
    /// Total tokens processed
    /// </summary>
    public int TotalTokens { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// Vectorization errors
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Vectorized chunk DTO
/// </summary>
public class VectorizedChunkDto
{
    /// <summary>
    /// Chunk ID
    /// </summary>
    public Guid ChunkId { get; set; }

    /// <summary>
    /// Vector ID in database
    /// </summary>
    public string VectorId { get; set; } = string.Empty;

    /// <summary>
    /// Embedding vector
    /// </summary>
    public float[] EmbeddingVector { get; set; } = Array.Empty<float>();

    /// <summary>
    /// Vector metadata
    /// </summary>
    public VectorMetadataDto Metadata { get; set; } = new();
}

/// <summary>
/// Vector metadata DTO
/// </summary>
public class VectorMetadataDto
{
    /// <summary>
    /// Vector database type
    /// </summary>
    public VectorDatabaseType DatabaseType { get; set; }

    /// <summary>
    /// Collection name
    /// </summary>
    public string Collection { get; set; } = string.Empty;

    /// <summary>
    /// Vector dimension
    /// </summary>
    public int Dimension { get; set; }

    /// <summary>
    /// Embedding model
    /// </summary>
    public string EmbeddingModel { get; set; } = string.Empty;

    /// <summary>
    /// Legal domain
    /// </summary>
    public LegalDomain Domain { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> AdditionalMetadata { get; set; } = new();
}

/// <summary>
/// Routing result DTO
/// </summary>
public class RoutingResultDto
{
    /// <summary>
    /// Routed chunks
    /// </summary>
    public List<RoutedChunkDto> RoutedChunks { get; set; } = new();

    /// <summary>
    /// Routing success
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Routing time
    /// </summary>
    public TimeSpan RoutingTime { get; set; }

    /// <summary>
    /// Agent used for routing
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// Routing errors
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Routed chunk DTO
/// </summary>
public class RoutedChunkDto
{
    /// <summary>
    /// Chunk ID
    /// </summary>
    public Guid ChunkId { get; set; }

    /// <summary>
    /// Target database
    /// </summary>
    public VectorDatabaseType TargetDatabase { get; set; }

    /// <summary>
    /// Target collection
    /// </summary>
    public string TargetCollection { get; set; } = string.Empty;

    /// <summary>
    /// Routing reason
    /// </summary>
    public string RoutingReason { get; set; } = string.Empty;

    /// <summary>
    /// Routing confidence
    /// </summary>
    public double Confidence { get; set; }
}

/// <summary>
/// Quality assessment DTO
/// </summary>
public class QualityAssessmentDto
{
    /// <summary>
    /// Overall quality score (0-1)
    /// </summary>
    public double OverallScore { get; set; }

    /// <summary>
    /// Quality passed
    /// </summary>
    public bool QualityPassed { get; set; }

    /// <summary>
    /// Assessment time
    /// </summary>
    public TimeSpan AssessmentTime { get; set; }

    /// <summary>
    /// Agent used for assessment
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// Quality metrics
    /// </summary>
    public Dictionary<string, double> QualityMetrics { get; set; } = new();

    /// <summary>
    /// Quality issues found
    /// </summary>
    public List<QualityIssueDto> Issues { get; set; } = new();

    /// <summary>
    /// Recommendations
    /// </summary>
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Quality issue DTO
/// </summary>
public class QualityIssueDto
{
    /// <summary>
    /// Issue type
    /// </summary>
    public string IssueType { get; set; } = string.Empty;

    /// <summary>
    /// Issue description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Issue severity
    /// </summary>
    public ErrorSeverity Severity { get; set; }

    /// <summary>
    /// Suggested fix
    /// </summary>
    public string? SuggestedFix { get; set; }

    /// <summary>
    /// Issue location
    /// </summary>
    public string? Location { get; set; }
}

/// <summary>
/// Pipeline validation DTO
/// </summary>
public class PipelineValidationDto
{
    /// <summary>
    /// Validation passed
    /// </summary>
    public bool ValidationPassed { get; set; }

    /// <summary>
    /// Validation time
    /// </summary>
    public TimeSpan ValidationTime { get; set; }

    /// <summary>
    /// Agent used for validation
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// Validation results by step
    /// </summary>
    public Dictionary<string, bool> StepValidations { get; set; } = new();

    /// <summary>
    /// Validation errors
    /// </summary>
    public List<string> ValidationErrors { get; set; } = new();

    /// <summary>
    /// Validation warnings
    /// </summary>
    public List<string> ValidationWarnings { get; set; } = new();

    /// <summary>
    /// Performance metrics
    /// </summary>
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
}

/// <summary>
/// Improvement suggestion DTO
/// </summary>
public class ImprovementSuggestionDto
{
    /// <summary>
    /// Suggestion type
    /// </summary>
    public string SuggestionType { get; set; } = string.Empty;

    /// <summary>
    /// Suggestion description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Expected improvement
    /// </summary>
    public string ExpectedImprovement { get; set; } = string.Empty;

    /// <summary>
    /// Implementation effort
    /// </summary>
    public string ImplementationEffort { get; set; } = string.Empty;

    /// <summary>
    /// Priority
    /// </summary>
    public ProcessingPriority Priority { get; set; }

    /// <summary>
    /// Affected components
    /// </summary>
    public List<string> AffectedComponents { get; set; } = new();
}

/// <summary>
/// Processing result DTO
/// </summary>
public class ProcessingResultDto
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Processing success
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Final status
    /// </summary>
    public DocumentStatus FinalStatus { get; set; }

    /// <summary>
    /// Total processing time
    /// </summary>
    public TimeSpan TotalProcessingTime { get; set; }

    /// <summary>
    /// Processing steps completed
    /// </summary>
    public List<ProcessingStepResultDto> CompletedSteps { get; set; } = new();

    /// <summary>
    /// Total chunks created
    /// </summary>
    public int TotalChunks { get; set; }

    /// <summary>
    /// Total tokens processed
    /// </summary>
    public int TotalTokens { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// Vector databases used
    /// </summary>
    public List<string> VectorDatabasesUsed { get; set; } = new();

    /// <summary>
    /// Processing errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Processing warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Quality assessment
    /// </summary>
    public QualityAssessmentDto? QualityAssessment { get; set; }
}

/// <summary>
/// Processing step result DTO
/// </summary>
public class ProcessingStepResultDto
{
    /// <summary>
    /// Step name
    /// </summary>
    public string StepName { get; set; } = string.Empty;

    /// <summary>
    /// Step success
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Step duration
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// Agent used
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// Step metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Step errors
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Processing status DTO
/// </summary>
public class ProcessingStatusDto
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Current status
    /// </summary>
    public DocumentStatus Status { get; set; }

    /// <summary>
    /// Current step
    /// </summary>
    public string? CurrentStep { get; set; }

    /// <summary>
    /// Progress percentage (0-100)
    /// </summary>
    public int ProgressPercentage { get; set; }

    /// <summary>
    /// Estimated time remaining
    /// </summary>
    public TimeSpan? EstimatedTimeRemaining { get; set; }

    /// <summary>
    /// Processing started at
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// Last updated at
    /// </summary>
    public DateTime LastUpdatedAt { get; set; }

    /// <summary>
    /// Completed steps
    /// </summary>
    public List<string> CompletedSteps { get; set; } = new();

    /// <summary>
    /// Remaining steps
    /// </summary>
    public List<string> RemainingSteps { get; set; } = new();

    /// <summary>
    /// Current agent
    /// </summary>
    public string? CurrentAgent { get; set; }

    /// <summary>
    /// Processing errors
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Processing priority enumeration
/// </summary>
public enum ProcessingPriority
{
    /// <summary>
    /// Low priority
    /// </summary>
    Low,

    /// <summary>
    /// Normal priority
    /// </summary>
    Normal,

    /// <summary>
    /// High priority
    /// </summary>
    High,

    /// <summary>
    /// Critical priority
    /// </summary>
    Critical
}
