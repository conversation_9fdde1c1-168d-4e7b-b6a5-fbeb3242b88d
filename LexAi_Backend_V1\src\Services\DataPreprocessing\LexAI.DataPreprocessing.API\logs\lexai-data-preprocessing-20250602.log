2025-06-02 15:07:36.085 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-02 15:07:36.208 +04:00 [INF] Hangfire SQL objects installed.
2025-06-02 15:07:36.223 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-02 15:07:36.621 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-02 15:07:36.654 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 15:07:36.863 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-02 15:07:36.867 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-02 15:07:36.925 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-02 15:07:36.929 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-02 15:07:36.938 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-02 15:07:36.940 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-02 15:07:36.941 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-02 15:07:36.944 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-02 15:07:36.978 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 15:07:36.981 +04:00 [INF] Hosting environment: Development
2025-06-02 15:07:36.985 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-02 15:07:37.024 +04:00 [INF] Server datapreprocessing-kevin11:46332:fff9452f successfully announced in 23.996 ms
2025-06-02 15:07:37.075 +04:00 [INF] Server datapreprocessing-kevin11:46332:fff9452f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-02 15:07:37.290 +04:00 [INF] Server datapreprocessing-kevin11:46332:fff9452f all the dispatchers started
2025-06-02 15:07:38.702 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-02 15:07:38.960 +04:00 [INF] Request GET / started with correlation ID 3d10af98-cd82-4726-832d-761d9d3ed3c2
2025-06-02 15:07:39.311 +04:00 [INF] Request GET / completed in 346ms with status 404 (Correlation ID: 3d10af98-cd82-4726-832d-761d9d3ed3c2)
2025-06-02 15:07:39.333 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 630.1429ms
2025-06-02 15:07:39.350 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-02 15:09:13.252 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:09:13.252 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:09:13.304 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 0acab212-4e43-4046-9c46-a8dfa3e5f8cc
2025-06-02 15:09:13.305 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID f55adcae-d5ca-444c-8128-216c0403d5fb
2025-06-02 15:09:13.314 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:09:13.316 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:09:13.329 +04:00 [INF] Request OPTIONS /api/documents completed in 13ms with status 204 (Correlation ID: f55adcae-d5ca-444c-8128-216c0403d5fb)
2025-06-02 15:09:13.329 +04:00 [INF] Request OPTIONS /api/documents completed in 19ms with status 204 (Correlation ID: 0acab212-4e43-4046-9c46-a8dfa3e5f8cc)
2025-06-02 15:09:13.338 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 86.753ms
2025-06-02 15:09:13.343 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:09:13.343 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 90.9693ms
2025-06-02 15:09:13.405 +04:00 [INF] Request GET /api/documents started with correlation ID 45d3e87e-fb82-4b08-a356-33d069893baa
2025-06-02 15:09:13.423 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:09:13.544 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:09:13.560 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:09:13.589 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:09:15.913 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:09:15.920 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:09:15.922 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:09:15.926 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:09:15.928 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:09:15.933 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:09:17.305 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 15:09:17.477 +04:00 [INF] Executed DbCommand (27ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:09:17.496 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:09:17.531 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 3934.5763ms
2025-06-02 15:09:17.537 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:09:17.538 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:09:17.543 +04:00 [INF] Request GET /api/documents started with correlation ID 37a91a7f-d394-4aa5-bdf3-ee50426def74
2025-06-02 15:09:17.553 +04:00 [INF] Request GET /api/documents completed in 4129ms with status 200 (Correlation ID: 45d3e87e-fb82-4b08-a356-33d069893baa)
2025-06-02 15:09:17.556 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:09:17.571 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:09:17.576 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:09:17.582 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 4239.021ms
2025-06-02 15:09:17.583 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:09:17.684 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:09:17.691 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:09:17.693 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 100.9555ms
2025-06-02 15:09:17.695 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:09:17.697 +04:00 [INF] Request GET /api/documents completed in 141ms with status 200 (Correlation ID: 37a91a7f-d394-4aa5-bdf3-ee50426def74)
2025-06-02 15:09:17.700 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 163.2138ms
2025-06-02 15:09:54.157 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 15:09:54.171 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 901a302f-acac-4301-a4c3-71199778dcee
2025-06-02 15:09:54.179 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:09:54.183 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 4ms with status 204 (Correlation ID: 901a302f-acac-4301-a4c3-71199778dcee)
2025-06-02 15:09:54.236 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 76.4905ms
2025-06-02 15:09:54.241 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryCxfyhsGfGhxjK2tp 474181
2025-06-02 15:09:54.271 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 29d8cb85-7624-42f7-b31e-6f51d3fdb8d9
2025-06-02 15:09:54.275 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:09:54.278 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:09:54.285 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 15:09:54.293 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:09:54.377 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 15:09:54.389 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 15:09:54.394 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 15:09:54.406 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 15:09:54.421 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 15:09:59.065 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> 98e7254c-1a8e-488c-bb96-2630e3ad2541.pdf
2025-06-02 15:09:59.299 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-02 15:09:59.323 +04:00 [INF] Document added successfully: "4b9f453f-1209-4da9-9ab3-aaea73af0ef2"
2025-06-02 15:09:59.324 +04:00 [INF] Document uploaded successfully: "4b9f453f-1209-4da9-9ab3-aaea73af0ef2" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 15:10:58.304 +04:00 [INF] Background processing started for document "4b9f453f-1209-4da9-9ab3-aaea73af0ef2"
2025-06-02 15:10:58.354 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "4b9f453f-1209-4da9-9ab3-aaea73af0ef2", Processing started: true
2025-06-02 15:11:07.169 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 15:11:07.169 +04:00 [INF] Starting orchestrated processing for document "4b9f453f-1209-4da9-9ab3-aaea73af0ef2": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 15:11:12.013 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 77717.7539ms
2025-06-02 15:11:12.016 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 15:11:12.017 +04:00 [INF] Request POST /api/documents/upload completed in 77742ms with status 200 (Correlation ID: 29d8cb85-7624-42f7-b31e-6f51d3fdb8d9)
2025-06-02 15:11:12.027 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 77785.9651ms
2025-06-02 15:11:12.284 +04:00 [ERR] Error updating document with new context "4b9f453f-1209-4da9-9ab3-aaea73af0ef2"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsyncWithNewContext(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 206
2025-06-02 15:11:12.394 +04:00 [ERR] Error in extraction step for document "4b9f453f-1209-4da9-9ab3-aaea73af0ef2"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsyncWithNewContext(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 206
   at LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.ExecuteExtractionStep(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Agents\OrchestrationAgent.cs:line 396
2025-06-02 15:12:01.742 +04:00 [ERR] Error updating document with new context "4b9f453f-1209-4da9-9ab3-aaea73af0ef2"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsyncWithNewContext(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 206
2025-06-02 15:12:01.818 +04:00 [ERR] Error updating document "4b9f453f-1209-4da9-9ab3-aaea73af0ef2" with error
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsyncWithNewContext(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 206
   at LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.UpdateDocumentWithError(Document document, String step, String errorMessage) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Agents\OrchestrationAgent.cs:line 707
2025-06-02 15:12:12.152 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:12:12.155 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:12:12.159 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 685dd990-c8a8-4b8e-8284-e411ed14e860
2025-06-02 15:12:12.163 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 70eca1c9-c42c-40ea-93bd-d27a6c5ce144
2025-06-02 15:12:12.166 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:12.170 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:12.172 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: 685dd990-c8a8-4b8e-8284-e411ed14e860)
2025-06-02 15:12:12.174 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: 70eca1c9-c42c-40ea-93bd-d27a6c5ce144)
2025-06-02 15:12:12.178 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 25.3619ms
2025-06-02 15:12:12.181 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:12:12.182 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 27.0278ms
2025-06-02 15:12:12.200 +04:00 [INF] Request GET /api/documents started with correlation ID d95ef280-0e56-41c5-bdcd-143e3d462c5e
2025-06-02 15:12:12.221 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:12.227 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:12:12.232 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:12.252 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:12:12.268 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:12:12.467 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:12:12.481 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 225.898ms
2025-06-02 15:12:12.484 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:12:12.484 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:12.488 +04:00 [INF] Request GET /api/documents started with correlation ID 19e79e81-a06f-4ca0-a7a7-444bcc9d29d8
2025-06-02 15:12:12.490 +04:00 [INF] Request GET /api/documents completed in 269ms with status 200 (Correlation ID: d95ef280-0e56-41c5-bdcd-143e3d462c5e)
2025-06-02 15:12:12.493 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:12.500 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 318.321ms
2025-06-02 15:12:12.501 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:12:12.508 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:12.510 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:12:12.523 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:12:12.531 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:12:12.535 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 21.1816ms
2025-06-02 15:12:12.538 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:12.541 +04:00 [INF] Request GET /api/documents completed in 47ms with status 200 (Correlation ID: 19e79e81-a06f-4ca0-a7a7-444bcc9d29d8)
2025-06-02 15:12:12.546 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 62.1826ms
2025-06-02 15:12:23.231 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 - null null
2025-06-02 15:12:23.231 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 - null null
2025-06-02 15:12:23.238 +04:00 [INF] Request OPTIONS /api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 started with correlation ID 8e31d45b-f6d0-4b37-bc96-363979a315bb
2025-06-02 15:12:23.245 +04:00 [INF] Request OPTIONS /api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 started with correlation ID a8a09f7a-4394-4f97-807c-f6c7c1ba8a1a
2025-06-02 15:12:23.249 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:23.252 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:23.254 +04:00 [INF] Request OPTIONS /api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 completed in 4ms with status 204 (Correlation ID: 8e31d45b-f6d0-4b37-bc96-363979a315bb)
2025-06-02 15:12:23.257 +04:00 [INF] Request OPTIONS /api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 completed in 4ms with status 204 (Correlation ID: a8a09f7a-4394-4f97-807c-f6c7c1ba8a1a)
2025-06-02 15:12:23.263 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 - 204 null null 32.2437ms
2025-06-02 15:12:23.268 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 - application/json null
2025-06-02 15:12:23.294 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 - 204 null null 63.0789ms
2025-06-02 15:12:23.305 +04:00 [INF] Request GET /api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 started with correlation ID b7156a54-6e48-4c1b-87ff-6416083e504f
2025-06-02 15:12:23.312 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:23.314 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:12:23.316 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:23.339 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:12:23.378 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 15:12:23.400 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 15:12:23.426 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 15:12:23.428 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 85.5561ms
2025-06-02 15:12:23.431 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:23.431 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 - application/json null
2025-06-02 15:12:23.433 +04:00 [INF] Request GET /api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 completed in 120ms with status 200 (Correlation ID: b7156a54-6e48-4c1b-87ff-6416083e504f)
2025-06-02 15:12:23.437 +04:00 [INF] Request GET /api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 started with correlation ID 63a6800a-9282-46c9-8bf6-9226e47b21ca
2025-06-02 15:12:23.440 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 - 200 null application/json; charset=utf-8 172.5917ms
2025-06-02 15:12:23.443 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:23.449 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:12:23.451 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:23.453 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:12:23.465 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 15:12:23.472 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 15:12:23.476 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 18.8492ms
2025-06-02 15:12:23.478 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:23.480 +04:00 [INF] Request GET /api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 completed in 36ms with status 200 (Correlation ID: 63a6800a-9282-46c9-8bf6-9226e47b21ca)
2025-06-02 15:12:23.484 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/4b9f453f-1209-4da9-9ab3-aaea73af0ef2 - 200 null application/json; charset=utf-8 52.783ms
2025-06-02 15:12:47.183 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:12:47.183 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:12:47.211 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 4fca1c29-6084-44a3-b524-634841855e86
2025-06-02 15:12:47.239 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:47.222 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID b1e0d12b-dc69-4c0f-b898-f302141aebd5
2025-06-02 15:12:47.258 +04:00 [INF] Request OPTIONS /api/documents completed in 18ms with status 204 (Correlation ID: 4fca1c29-6084-44a3-b524-634841855e86)
2025-06-02 15:12:47.268 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:47.276 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 92.5797ms
2025-06-02 15:12:47.278 +04:00 [INF] Request OPTIONS /api/documents completed in 10ms with status 204 (Correlation ID: b1e0d12b-dc69-4c0f-b898-f302141aebd5)
2025-06-02 15:12:47.280 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:12:47.298 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 114.446ms
2025-06-02 15:12:47.303 +04:00 [INF] Request GET /api/documents started with correlation ID a666ce0b-ac77-4452-99e8-1cca063d8ce0
2025-06-02 15:12:47.313 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:47.316 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:12:47.319 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:47.322 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:12:47.337 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:12:47.343 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:12:47.346 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 19.9748ms
2025-06-02 15:12:47.348 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:12:47.349 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:47.353 +04:00 [INF] Request GET /api/documents started with correlation ID f22ec328-d2ef-47f5-b9bb-d359054ee763
2025-06-02 15:12:47.354 +04:00 [INF] Request GET /api/documents completed in 41ms with status 200 (Correlation ID: a666ce0b-ac77-4452-99e8-1cca063d8ce0)
2025-06-02 15:12:47.357 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:12:47.360 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 79.3486ms
2025-06-02 15:12:47.362 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:12:47.368 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:47.370 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:12:47.385 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:12:47.393 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:12:47.396 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 21.9357ms
2025-06-02 15:12:47.398 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:12:47.401 +04:00 [INF] Request GET /api/documents completed in 43ms with status 200 (Correlation ID: f22ec328-d2ef-47f5-b9bb-d359054ee763)
2025-06-02 15:12:47.404 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 56.3289ms
2025-06-02 15:17:32.416 +04:00 [INF] Server datapreprocessing-kevin11:46332:fff9452f caught stopping signal...
2025-06-02 15:17:32.436 +04:00 [INF] Application is shutting down...
2025-06-02 15:17:32.742 +04:00 [INF] Server datapreprocessing-kevin11:46332:fff9452f All dispatchers stopped
2025-06-02 15:17:32.757 +04:00 [INF] Server datapreprocessing-kevin11:46332:fff9452f successfully reported itself as stopped in 3.1061 ms
2025-06-02 15:17:32.758 +04:00 [INF] Server datapreprocessing-kevin11:46332:fff9452f has been stopped in total 335.0786 ms
2025-06-02 15:37:05.392 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-02 15:37:05.585 +04:00 [INF] Hangfire SQL objects installed.
2025-06-02 15:37:05.595 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-02 15:37:05.902 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-02 15:37:05.920 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 15:37:06.072 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-02 15:37:06.075 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-02 15:37:06.080 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-02 15:37:06.081 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-02 15:37:06.085 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-02 15:37:06.086 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-02 15:37:06.088 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-02 15:37:06.090 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-02 15:37:06.101 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 15:37:06.104 +04:00 [INF] Hosting environment: Development
2025-06-02 15:37:06.107 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-02 15:37:06.130 +04:00 [INF] Server datapreprocessing-kevin11:20464:498f40be successfully announced in 19.9033 ms
2025-06-02 15:37:06.134 +04:00 [INF] Server datapreprocessing-kevin11:20464:498f40be is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-02 15:37:06.143 +04:00 [INF] Server datapreprocessing-kevin11:20464:498f40be all the dispatchers started
2025-06-02 15:37:34.249 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:37:34.249 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:37:34.290 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 59311941-0a80-42c3-ab22-5139e76de20d
2025-06-02 15:37:34.290 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 79ba440c-f1b9-4d6c-b098-fd39b615cd52
2025-06-02 15:37:34.294 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:37:34.295 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:37:34.297 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: 59311941-0a80-42c3-ab22-5139e76de20d)
2025-06-02 15:37:34.297 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: 79ba440c-f1b9-4d6c-b098-fd39b615cd52)
2025-06-02 15:37:34.301 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 52.0035ms
2025-06-02 15:37:34.301 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 52.0422ms
2025-06-02 15:37:34.307 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:37:34.315 +04:00 [INF] Request GET /api/documents started with correlation ID 87023649-69d8-46fc-8599-72db463b2601
2025-06-02 15:37:34.316 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:37:34.410 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:37:34.418 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:37:34.480 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:37:35.389 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:37:35.391 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:37:35.392 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:37:35.395 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:37:35.397 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:37:35.401 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 15:37:36.028 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 15:37:36.176 +04:00 [INF] Executed DbCommand (20ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:37:36.399 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:37:36.440 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:37:36.441 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 1941.6826ms
2025-06-02 15:37:36.445 +04:00 [INF] Request GET /api/documents started with correlation ID 81e015d6-7956-450e-b74e-eb056f3447f4
2025-06-02 15:37:36.448 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:37:36.449 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:37:36.453 +04:00 [INF] Request GET /api/documents completed in 2137ms with status 200 (Correlation ID: 87023649-69d8-46fc-8599-72db463b2601)
2025-06-02 15:37:36.458 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:37:36.464 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:37:36.466 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:37:36.467 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 2160.6344ms
2025-06-02 15:37:36.516 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:37:36.526 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:37:36.534 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 60.9447ms
2025-06-02 15:37:36.539 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:37:36.543 +04:00 [INF] Request GET /api/documents completed in 93ms with status 200 (Correlation ID: 81e015d6-7956-450e-b74e-eb056f3447f4)
2025-06-02 15:37:36.548 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 107.8778ms
2025-06-02 15:40:29.283 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:40:29.283 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:40:29.287 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 9ee47143-2861-4a26-94ee-08dc8be7252a
2025-06-02 15:40:29.290 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 099dec93-2a51-4e67-9489-870a9c2109b2
2025-06-02 15:40:29.293 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:40:29.294 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:40:29.296 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 9ee47143-2861-4a26-94ee-08dc8be7252a)
2025-06-02 15:40:29.297 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: 099dec93-2a51-4e67-9489-870a9c2109b2)
2025-06-02 15:40:29.300 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 16.5864ms
2025-06-02 15:40:29.303 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 19.9013ms
2025-06-02 15:40:29.303 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:40:29.318 +04:00 [INF] Request GET /api/documents started with correlation ID 1328e445-9ad4-4a55-879e-44e123bd0ee4
2025-06-02 15:40:29.321 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:40:29.332 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:40:29.342 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:40:29.345 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:40:29.504 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:40:29.513 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:40:29.519 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 169.2607ms
2025-06-02 15:40:29.523 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:40:29.524 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:40:29.534 +04:00 [INF] Request GET /api/documents started with correlation ID c0f9ee6e-132c-4f1a-8d14-d1445c998a0e
2025-06-02 15:40:29.537 +04:00 [INF] Request GET /api/documents completed in 216ms with status 200 (Correlation ID: 1328e445-9ad4-4a55-879e-44e123bd0ee4)
2025-06-02 15:40:29.540 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:40:29.545 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 241.9444ms
2025-06-02 15:40:29.548 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:40:29.556 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:40:29.562 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:40:29.581 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:40:29.588 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:40:29.590 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 21.3974ms
2025-06-02 15:40:29.594 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:40:29.599 +04:00 [INF] Request GET /api/documents completed in 59ms with status 200 (Correlation ID: c0f9ee6e-132c-4f1a-8d14-d1445c998a0e)
2025-06-02 15:40:29.613 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 89.6299ms
2025-06-02 15:41:04.748 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 15:41:04.755 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID f17f4b56-aa35-4df2-8e1f-271e25d8f49f
2025-06-02 15:41:04.763 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:41:04.767 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 4ms with status 204 (Correlation ID: f17f4b56-aa35-4df2-8e1f-271e25d8f49f)
2025-06-02 15:41:04.781 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 33.2661ms
2025-06-02 15:41:04.791 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryn2mA8Y8rqmPZekSG 474174
2025-06-02 15:41:04.815 +04:00 [INF] Request POST /api/documents/upload started with correlation ID aaed4f0c-6c12-4584-a9c9-3b6a9fe6a258
2025-06-02 15:41:04.820 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:41:04.829 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:41:04.835 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 15:41:04.853 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:41:04.944 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 15:41:04.967 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 15:41:04.984 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 15:41:05.034 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 15:41:05.077 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 15:41:05.095 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> d36bbfca-c1dc-46a1-b79f-f398254baa4a.pdf
2025-06-02 15:41:05.521 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-02 15:41:05.583 +04:00 [INF] Document added successfully: "72de9710-b602-44cb-a552-e705bc3ea3b4"
2025-06-02 15:41:05.586 +04:00 [INF] Document uploaded successfully: "72de9710-b602-44cb-a552-e705bc3ea3b4" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 15:41:05.591 +04:00 [INF] Background processing started for document "72de9710-b602-44cb-a552-e705bc3ea3b4"
2025-06-02 15:41:05.594 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "72de9710-b602-44cb-a552-e705bc3ea3b4", Processing started: true
2025-06-02 15:41:05.606 +04:00 [INF] Starting orchestrated processing for document "72de9710-b602-44cb-a552-e705bc3ea3b4": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 15:41:05.619 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 15:41:05.643 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 782.9517ms
2025-06-02 15:41:05.657 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 15:41:05.667 +04:00 [INF] Request POST /api/documents/upload completed in 847ms with status 200 (Correlation ID: aaed4f0c-6c12-4584-a9c9-3b6a9fe6a258)
2025-06-02 15:41:05.676 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 884.7612ms
2025-06-02 15:41:06.228 +04:00 [INF] Starting text extraction for document "72de9710-b602-44cb-a552-e705bc3ea3b4": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 15:41:06.254 +04:00 [INF] Extracting text from INTRODUCTION AU DROIT DU TRAVAIL.pdf (MIME: application/pdf)
2025-06-02 15:41:06.261 +04:00 [INF] Text extraction successful for INTRODUCTION AU DROIT DU TRAVAIL.pdf. Length: 179
2025-06-02 15:41:06.271 +04:00 [INF] Text extraction completed for document "72de9710-b602-44cb-a552-e705bc3ea3b4". Length: 179, Confidence: 0.70, Time: 39ms
2025-06-02 15:41:06.438 +04:00 [INF] Starting classification for document "72de9710-b602-44cb-a552-e705bc3ea3b4": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 15:41:06.444 +04:00 [INF] Classifying text into legal domains. Text length: 179
2025-06-02 15:41:06.477 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-02 15:41:06.482 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-02 15:41:09.198 +04:00 [INF] Received HTTP response headers after 2709.6235ms - 429
2025-06-02 15:41:09.201 +04:00 [INF] End processing HTTP request after 2725.5015ms - 429
2025-06-02 15:41:09.209 +04:00 [WRN] AI classification failed, using rule-based result: "Labor" (0.33)
2025-06-02 15:41:09.228 +04:00 [INF] Classification completed for document "72de9710-b602-44cb-a552-e705bc3ea3b4". Domain: "Labor", Confidence: 0.33, Keywords: 10, Entities: 0, Time: 2786ms
2025-06-02 15:41:09.287 +04:00 [INF] Starting chunking for document "72de9710-b602-44cb-a552-e705bc3ea3b4" with strategy "Semantic"
2025-06-02 15:41:09.295 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 179
2025-06-02 15:41:09.304 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-02 15:41:09.320 +04:00 [INF] Chunking completed for document "72de9710-b602-44cb-a552-e705bc3ea3b4". Chunks: 1, Average size: 179, Time: 29ms
2025-06-02 15:41:09.361 +04:00 [INF] Starting vectorization with model "OpenAISmall" for 1 chunks
2025-06-02 15:41:09.374 +04:00 [INF] Generating embeddings for 1 texts using model "OpenAISmall"
2025-06-02 15:41:09.383 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/embeddings
2025-06-02 15:41:09.385 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/embeddings
2025-06-02 15:41:13.121 +04:00 [INF] Received HTTP response headers after 3732.8742ms - 429
2025-06-02 15:41:13.122 +04:00 [INF] End processing HTTP request after 3739.097ms - 429
2025-06-02 15:41:13.126 +04:00 [ERR] OpenAI API error: "TooManyRequests" - {
    "error": {
        "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.",
        "type": "insufficient_quota",
        "param": null,
        "code": "insufficient_quota"
    }
}

2025-06-02 15:41:13.131 +04:00 [INF] Generated 0/1 embeddings successfully
2025-06-02 15:41:13.134 +04:00 [ERR] All embeddings failed for vectorization
2025-06-02 15:41:27.045 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:41:27.045 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 15:41:27.054 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 2ab87954-53d7-42c9-82a6-15977470483e
2025-06-02 15:41:27.060 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 1229f0d1-4aff-4844-8d65-044b8ad284d4
2025-06-02 15:41:27.063 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:41:27.067 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:41:27.070 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: 2ab87954-53d7-42c9-82a6-15977470483e)
2025-06-02 15:41:27.070 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 1229f0d1-4aff-4844-8d65-044b8ad284d4)
2025-06-02 15:41:27.075 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 29.8259ms
2025-06-02 15:41:27.080 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:41:27.081 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 35.7461ms
2025-06-02 15:41:27.096 +04:00 [INF] Request GET /api/documents started with correlation ID 09f9e358-21fc-4cef-8caa-35698108f6de
2025-06-02 15:41:27.106 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:41:27.107 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:41:27.108 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:41:27.113 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:41:27.128 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:41:27.178 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:41:27.185 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 68.4261ms
2025-06-02 15:41:27.188 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 15:41:27.190 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:41:27.200 +04:00 [INF] Request GET /api/documents started with correlation ID 718dae64-d103-400d-9454-d98c2f13454e
2025-06-02 15:41:27.201 +04:00 [INF] Request GET /api/documents completed in 94ms with status 200 (Correlation ID: 09f9e358-21fc-4cef-8caa-35698108f6de)
2025-06-02 15:41:27.204 +04:00 [INF] CORS policy execution successful.
2025-06-02 15:41:27.207 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 126.9476ms
2025-06-02 15:41:27.210 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 15:41:27.221 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:41:27.228 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 15:41:27.251 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 15:41:27.304 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 15:41:27.308 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 73.6663ms
2025-06-02 15:41:27.311 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 15:41:27.315 +04:00 [INF] Request GET /api/documents completed in 110ms with status 200 (Correlation ID: 718dae64-d103-400d-9454-d98c2f13454e)
2025-06-02 15:41:27.319 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 131.491ms
2025-06-02 15:42:42.658 +04:00 [INF] Server datapreprocessing-kevin11:20464:498f40be caught stopping signal...
2025-06-02 15:42:42.660 +04:00 [INF] Application is shutting down...
2025-06-02 15:42:42.663 +04:00 [INF] Server datapreprocessing-kevin11:20464:498f40be All dispatchers stopped
2025-06-02 15:42:42.673 +04:00 [INF] Server datapreprocessing-kevin11:20464:498f40be successfully reported itself as stopped in 7.1374 ms
2025-06-02 15:42:42.675 +04:00 [INF] Server datapreprocessing-kevin11:20464:498f40be has been stopped in total 14.13 ms
