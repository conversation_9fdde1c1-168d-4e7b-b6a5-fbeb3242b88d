2025-06-02 12:51:28.352 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-02 12:51:28.480 +04:00 [INF] Hangfire SQL objects installed.
2025-06-02 12:51:28.494 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-02 12:51:29.640 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-02 12:51:29.678 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 12:51:30.149 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-02 12:51:30.151 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-02 12:51:30.301 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-02 12:51:30.309 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-02 12:51:30.312 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-02 12:51:30.314 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-02 12:51:30.315 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-02 12:51:30.316 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-02 12:51:30.375 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 12:51:30.380 +04:00 [INF] Hosting environment: Development
2025-06-02 12:51:30.387 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-02 12:51:30.513 +04:00 [INF] Server datapreprocessing-kevin11:48488:2475f161 successfully announced in 86.7092 ms
2025-06-02 12:51:30.561 +04:00 [INF] Server datapreprocessing-kevin11:48488:2475f161 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-02 12:51:30.710 +04:00 [INF] 1 servers were removed due to timeout
2025-06-02 12:51:30.834 +04:00 [INF] Server datapreprocessing-kevin11:48488:2475f161 all the dispatchers started
2025-06-02 12:51:32.150 +04:00 [INF] Generating processing statistics
2025-06-02 12:51:32.205 +04:00 [INF] Starting cleanup of failed documents
2025-06-02 12:51:32.228 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-02 12:51:32.501 +04:00 [INF] Request GET / started with correlation ID df488b6f-199c-44fc-a9d0-f4730ede7458
2025-06-02 12:51:34.170 +04:00 [INF] Request GET / completed in 1664ms with status 404 (Correlation ID: df488b6f-199c-44fc-a9d0-f4730ede7458)
2025-06-02 12:51:34.180 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 1957.2478ms
2025-06-02 12:51:34.188 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-02 12:56:12.555 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 12:56:12.555 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 12:56:12.574 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID aef9bff9-a7e1-4668-9397-0ea4d9c14ab9
2025-06-02 12:56:12.574 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 0c4e4698-dac7-4382-9eb3-1af807faf93a
2025-06-02 12:56:12.586 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:12.586 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:12.593 +04:00 [INF] Request OPTIONS /api/documents completed in 13ms with status 204 (Correlation ID: aef9bff9-a7e1-4668-9397-0ea4d9c14ab9)
2025-06-02 12:56:12.593 +04:00 [INF] Request OPTIONS /api/documents completed in 9ms with status 204 (Correlation ID: 0c4e4698-dac7-4382-9eb3-1af807faf93a)
2025-06-02 12:56:12.599 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 43.8904ms
2025-06-02 12:56:12.611 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 12:56:12.614 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 58.4258ms
2025-06-02 12:56:12.628 +04:00 [INF] Request GET /api/documents started with correlation ID dfe707aa-c1cf-4ef3-9402-339d680ac185
2025-06-02 12:56:12.633 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:12.915 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:12 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 12:56:12.930 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:12 AM'.
2025-06-02 12:56:12.938 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:12 AM'.
2025-06-02 12:56:12.958 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 12:56:12.973 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 12:56:12.976 +04:00 [INF] Request GET /api/documents completed in 342ms with status 401 (Correlation ID: dfe707aa-c1cf-4ef3-9402-339d680ac185)
2025-06-02 12:56:12.984 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 372.2856ms
2025-06-02 12:56:12.988 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 12:56:13.048 +04:00 [INF] Request GET /api/documents started with correlation ID ff11cd6d-1e98-41c7-8937-64601477741e
2025-06-02 12:56:13.059 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:13.068 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:13 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 12:56:13.074 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:13 AM'.
2025-06-02 12:56:13.076 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:13 AM'.
2025-06-02 12:56:13.078 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 12:56:13.081 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 12:56:13.082 +04:00 [INF] Request GET /api/documents completed in 23ms with status 401 (Correlation ID: ff11cd6d-1e98-41c7-8937-64601477741e)
2025-06-02 12:56:13.085 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 97.4507ms
2025-06-02 12:56:41.809 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 12:56:41.815 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 12:56:41.817 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 2b60e2c4-d14c-4d3e-b3c7-bc6bd6c92c16
2025-06-02 12:56:41.822 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 1dc8d6bc-fbdc-4a58-87e1-71eb2173551e
2025-06-02 12:56:41.825 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:41.830 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:41.832 +04:00 [INF] Request OPTIONS /api/documents completed in 7ms with status 204 (Correlation ID: 2b60e2c4-d14c-4d3e-b3c7-bc6bd6c92c16)
2025-06-02 12:56:41.834 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: 1dc8d6bc-fbdc-4a58-87e1-71eb2173551e)
2025-06-02 12:56:41.944 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 12:56:41.944 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 128.9357ms
2025-06-02 12:56:41.886 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 76.7994ms
2025-06-02 12:56:41.965 +04:00 [INF] Request GET /api/documents started with correlation ID c799b0f4-cd68-4422-b5f6-e5db3c6b5916
2025-06-02 12:56:41.979 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:41.985 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:41 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 12:56:41.989 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:41 AM'.
2025-06-02 12:56:41.990 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:41 AM'.
2025-06-02 12:56:41.997 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 12:56:41.999 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 12:56:42.000 +04:00 [INF] Request GET /api/documents completed in 22ms with status 401 (Correlation ID: c799b0f4-cd68-4422-b5f6-e5db3c6b5916)
2025-06-02 12:56:42.003 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 59.7969ms
2025-06-02 12:56:42.005 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 12:56:42.016 +04:00 [INF] Request GET /api/documents started with correlation ID 502c8376-10c4-4df1-a012-0318484af3fc
2025-06-02 12:56:42.020 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:56:42.024 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:42 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 12:56:42.030 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:42 AM'.
2025-06-02 12:56:42.035 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 6:33:51 PM', Current time (UTC): '6/2/2025 8:56:42 AM'.
2025-06-02 12:56:42.040 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 12:56:42.048 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 12:56:42.051 +04:00 [INF] Request GET /api/documents completed in 31ms with status 401 (Correlation ID: 502c8376-10c4-4df1-a012-0318484af3fc)
2025-06-02 12:56:42.057 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 51.4066ms
2025-06-02 12:58:03.590 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 12:58:03.630 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 12:58:03.637 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID c90c47b4-f00c-425a-b59a-f30f5f0d09b1
2025-06-02 12:58:03.641 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID b7c1dbeb-4fd8-4032-a455-4ca03e5c2b09
2025-06-02 12:58:03.648 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:58:03.652 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:58:03.654 +04:00 [INF] Request OPTIONS /api/documents completed in 7ms with status 204 (Correlation ID: c90c47b4-f00c-425a-b59a-f30f5f0d09b1)
2025-06-02 12:58:03.656 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: b7c1dbeb-4fd8-4032-a455-4ca03e5c2b09)
2025-06-02 12:58:03.661 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 70.0335ms
2025-06-02 12:58:03.758 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 127.5499ms
2025-06-02 12:58:03.749 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 12:58:03.813 +04:00 [INF] Request GET /api/documents started with correlation ID 2530028d-0453-459a-87f7-a64c88c4df32
2025-06-02 12:58:03.816 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:58:03.840 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 12:58:03.848 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 12:58:03.921 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 12:58:07.170 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 12:58:07.173 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 12:58:07.175 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 12:58:07.180 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 12:58:07.183 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 12:58:07.186 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 12:58:09.826 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 12:58:10.116 +04:00 [INF] Executed DbCommand (30ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 12:58:10.478 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 12:58:10.567 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 12:58:10.571 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 6629.6251ms
2025-06-02 12:58:10.576 +04:00 [INF] Request GET /api/documents started with correlation ID 1fb3475d-dffe-4301-8b27-c2b8490f5672
2025-06-02 12:58:10.579 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 12:58:10.582 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:58:10.587 +04:00 [INF] Request GET /api/documents completed in 6770ms with status 200 (Correlation ID: 2530028d-0453-459a-87f7-a64c88c4df32)
2025-06-02 12:58:10.588 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 12:58:10.593 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 12:58:10.597 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 12:58:10.710 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 6961.361ms
2025-06-02 12:58:10.723 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 12:58:10.734 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 12:58:10.738 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 136.3308ms
2025-06-02 12:58:10.742 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 12:58:10.745 +04:00 [INF] Request GET /api/documents completed in 163ms with status 200 (Correlation ID: 1fb3475d-dffe-4301-8b27-c2b8490f5672)
2025-06-02 12:58:10.750 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 182.6575ms
2025-06-02 12:59:18.909 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 12:59:18.936 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 30ff416c-9eac-4bd1-aa68-f960eeeb12bf
2025-06-02 12:59:18.940 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:59:18.942 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 2ms with status 204 (Correlation ID: 30ff416c-9eac-4bd1-aa68-f960eeeb12bf)
2025-06-02 12:59:18.952 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 43.0062ms
2025-06-02 12:59:18.960 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundarypMJPU4tS793wSTd9 473849
2025-06-02 12:59:19.009 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 51aa47f8-bc88-48f8-82c5-c7e46a0ab7c0
2025-06-02 12:59:19.016 +04:00 [INF] CORS policy execution successful.
2025-06-02 12:59:19.020 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 12:59:19.027 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 12:59:19.042 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 12:59:19.316 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 12:59:19.351 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 12:59:19.394 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 12:59:19.436 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 12:59:34.512 +04:00 [WRN] Duplicate document detected: INTRODUCTION AU DROIT DU TRAVAIL.pdf (hash: e140140c91424630b068bc55d3b38e56b18921c2e66150d4ac6127f95702617c)
2025-06-02 12:59:43.159 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "9af3c1d0-f604-40b1-81f2-1694b11b22f8", Processing started: false
2025-06-02 12:59:43.164 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 12:59:43.178 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 24133.6965ms
2025-06-02 12:59:43.186 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 12:59:43.192 +04:00 [INF] Request POST /api/documents/upload completed in 24175ms with status 200 (Correlation ID: 51aa47f8-bc88-48f8-82c5-c7e46a0ab7c0)
2025-06-02 12:59:43.201 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 24240.7886ms
2025-06-02 13:00:04.582 +04:00 [INF] Generating processing statistics
2025-06-02 13:01:22.307 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 13:01:22.313 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 13:01:22.325 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID b1d451fe-f8e3-47d1-baca-be86b45653e8
2025-06-02 13:01:22.333 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID d917ded8-8cf8-4c67-9c99-4f3f485641cd
2025-06-02 13:01:22.337 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:01:22.341 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:01:22.343 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 6ms with status 204 (Correlation ID: b1d451fe-f8e3-47d1-baca-be86b45653e8)
2025-06-02 13:01:22.346 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 4ms with status 204 (Correlation ID: d917ded8-8cf8-4c67-9c99-4f3f485641cd)
2025-06-02 13:01:22.350 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 42.4687ms
2025-06-02 13:01:22.363 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 49.9266ms
2025-06-02 13:01:22.361 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 13:01:22.388 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID a4bf576d-a0cb-4d17-bc0f-1ae20f1837c6
2025-06-02 13:01:22.390 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:01:22.392 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:01:22.394 +04:00 [INF] Request GET /api/documents/undefined completed in 4ms with status 404 (Correlation ID: a4bf576d-a0cb-4d17-bc0f-1ae20f1837c6)
2025-06-02 13:01:22.397 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 35.3021ms
2025-06-02 13:01:22.399 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 13:01:22.402 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 13:01:22.407 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID 9354ded7-d3c0-4d08-ae0d-21594bea66b8
2025-06-02 13:01:22.411 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:01:22.413 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:01:22.413 +04:00 [INF] Request GET /api/documents/undefined completed in 2ms with status 404 (Correlation ID: 9354ded7-d3c0-4d08-ae0d-21594bea66b8)
2025-06-02 13:01:22.415 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 16.0903ms
2025-06-02 13:01:22.421 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 13:01:29.533 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 13:01:29.535 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 13:01:29.543 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID e30b8dc1-c8db-4b7a-9886-5b99b21f0f9f
2025-06-02 13:01:29.559 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 513ff0fe-00f7-4a20-a88e-2d3ada420322
2025-06-02 13:01:29.566 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:01:29.574 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:01:29.579 +04:00 [INF] Request OPTIONS /api/documents completed in 12ms with status 204 (Correlation ID: e30b8dc1-c8db-4b7a-9886-5b99b21f0f9f)
2025-06-02 13:01:29.583 +04:00 [INF] Request OPTIONS /api/documents completed in 8ms with status 204 (Correlation ID: 513ff0fe-00f7-4a20-a88e-2d3ada420322)
2025-06-02 13:01:29.593 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 59.2171ms
2025-06-02 13:01:29.597 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 13:01:29.603 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 67.3743ms
2025-06-02 13:01:29.626 +04:00 [INF] Request GET /api/documents started with correlation ID d8208793-b68e-4fcf-803a-06bf717245f8
2025-06-02 13:01:29.660 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:01:29.663 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:01:29.666 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:01:29.670 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:01:29.695 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 13:01:29.702 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 13:01:29.705 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 30.1714ms
2025-06-02 13:01:29.711 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 13:01:29.712 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:01:29.717 +04:00 [INF] Request GET /api/documents started with correlation ID 91aeeb3b-25d8-44c7-94c7-b08962c83a41
2025-06-02 13:01:29.718 +04:00 [INF] Request GET /api/documents completed in 58ms with status 200 (Correlation ID: d8208793-b68e-4fcf-803a-06bf717245f8)
2025-06-02 13:01:29.721 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:01:29.727 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 129.2529ms
2025-06-02 13:01:29.729 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:01:29.740 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:01:29.743 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:01:29.760 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 13:01:29.766 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 13:01:29.770 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 23.1059ms
2025-06-02 13:01:29.774 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:01:29.776 +04:00 [INF] Request GET /api/documents completed in 55ms with status 200 (Correlation ID: 91aeeb3b-25d8-44c7-94c7-b08962c83a41)
2025-06-02 13:01:29.782 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 70.6853ms
2025-06-02 13:02:02.042 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 13:02:02.062 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 053400ba-f06b-4f04-8e45-f22d105a9f55
2025-06-02 13:02:02.071 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:02:02.075 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 4ms with status 204 (Correlation ID: 053400ba-f06b-4f04-8e45-f22d105a9f55)
2025-06-02 13:02:02.094 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 52.183ms
2025-06-02 13:02:02.101 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryhTUomzbHdeGjlNc0 473855
2025-06-02 13:02:02.157 +04:00 [INF] Request POST /api/documents/upload started with correlation ID abf1296f-fe56-408f-b995-12add507f9ef
2025-06-02 13:02:02.167 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:02:02.171 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:02:02.174 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:02:02.178 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:02:02.203 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 13:02:02.214 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 13:02:02.248 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 13:03:13.712 +04:00 [WRN] Duplicate document detected: INTRODUCTION AU DROIT DU TRAVAIL.pdf (hash: e140140c91424630b068bc55d3b38e56b18921c2e66150d4ac6127f95702617c)
2025-06-02 13:03:30.558 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "9af3c1d0-f604-40b1-81f2-1694b11b22f8", Processing started: false
2025-06-02 13:03:30.567 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 13:03:30.571 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 88385.9271ms
2025-06-02 13:03:30.574 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:03:30.577 +04:00 [INF] Request POST /api/documents/upload completed in 88409ms with status 200 (Correlation ID: abf1296f-fe56-408f-b995-12add507f9ef)
2025-06-02 13:03:30.584 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 88483.0034ms
2025-06-02 13:06:39.719 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 13:06:39.737 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 13:06:39.762 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID b773c093-ca8e-4ad5-a301-4f84bef62a1a
2025-06-02 13:06:39.769 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID cb099b6e-c0af-4310-beec-61b2e2b551f2
2025-06-02 13:06:39.775 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:06:39.778 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:06:39.780 +04:00 [INF] Request OPTIONS /api/documents completed in 5ms with status 204 (Correlation ID: b773c093-ca8e-4ad5-a301-4f84bef62a1a)
2025-06-02 13:06:39.782 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: cb099b6e-c0af-4310-beec-61b2e2b551f2)
2025-06-02 13:06:39.785 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 66.4773ms
2025-06-02 13:06:39.811 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 73.5244ms
2025-06-02 13:06:39.804 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 13:06:39.859 +04:00 [INF] Request GET /api/documents started with correlation ID 5713f4aa-205f-44a6-b37a-4bfd4a9815c6
2025-06-02 13:06:39.865 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:06:39.868 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:06:39.870 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:06:39.873 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:06:39.916 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 13:06:39.923 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 13:06:39.927 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 48.9503ms
2025-06-02 13:06:39.928 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 13:06:39.929 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:06:39.933 +04:00 [INF] Request GET /api/documents started with correlation ID 128bea6c-84df-4875-b244-517c7f2bbe35
2025-06-02 13:06:39.934 +04:00 [INF] Request GET /api/documents completed in 69ms with status 200 (Correlation ID: 5713f4aa-205f-44a6-b37a-4bfd4a9815c6)
2025-06-02 13:06:39.938 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:06:39.945 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 140.8055ms
2025-06-02 13:06:39.948 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:06:39.961 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:06:39.967 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:06:39.997 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 13:06:40.011 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 13:06:40.021 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 42.8454ms
2025-06-02 13:06:40.029 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:06:40.035 +04:00 [INF] Request GET /api/documents completed in 96ms with status 200 (Correlation ID: 128bea6c-84df-4875-b244-517c7f2bbe35)
2025-06-02 13:06:40.043 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 114.2796ms
2025-06-02 13:12:44.195 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 13:12:44.217 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 5a9424f5-b339-4636-880a-63f4737b0602
2025-06-02 13:12:44.225 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:12:44.228 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 3ms with status 204 (Correlation ID: 5a9424f5-b339-4636-880a-63f4737b0602)
2025-06-02 13:12:44.235 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 40.2692ms
2025-06-02 13:12:44.243 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryjoxCrbBbPeN1G2XY 473855
2025-06-02 13:12:44.269 +04:00 [INF] Request POST /api/documents/upload started with correlation ID d5d3c7f8-144b-4836-962e-5e2709da8d77
2025-06-02 13:12:44.270 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:12:44.272 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:12:44.274 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:12:44.275 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:12:44.289 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 13:12:44.294 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 13:12:44.331 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 13:12:55.707 +04:00 [WRN] Duplicate document detected: INTRODUCTION AU DROIT DU TRAVAIL.pdf (hash: e140140c91424630b068bc55d3b38e56b18921c2e66150d4ac6127f95702617c)
2025-06-02 13:12:55.714 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "9af3c1d0-f604-40b1-81f2-1694b11b22f8", Processing started: false
2025-06-02 13:12:55.723 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 13:12:55.732 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 11451.6635ms
2025-06-02 13:12:55.738 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:12:55.743 +04:00 [INF] Request POST /api/documents/upload completed in 11472ms with status 200 (Correlation ID: d5d3c7f8-144b-4836-962e-5e2709da8d77)
2025-06-02 13:12:55.748 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 11505.6848ms
2025-06-02 13:18:00.819 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 13:18:00.819 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 13:18:00.829 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 3287ef8d-1afa-4a65-8187-aebd85a157ff
2025-06-02 13:18:00.835 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID bf820ce5-1ff8-4774-afb8-91146a453bb3
2025-06-02 13:18:00.840 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:18:00.844 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:18:00.850 +04:00 [INF] Request OPTIONS /api/documents completed in 10ms with status 204 (Correlation ID: 3287ef8d-1afa-4a65-8187-aebd85a157ff)
2025-06-02 13:18:00.855 +04:00 [INF] Request OPTIONS /api/documents completed in 10ms with status 204 (Correlation ID: bf820ce5-1ff8-4774-afb8-91146a453bb3)
2025-06-02 13:18:00.863 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 44.492ms
2025-06-02 13:18:00.927 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 13:18:00.928 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 109.8564ms
2025-06-02 13:18:00.938 +04:00 [INF] Request GET /api/documents started with correlation ID d0e79565-8f30-4978-b914-2cf92009d3ee
2025-06-02 13:18:00.948 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:18:00.950 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:18:00.953 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:18:00.955 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:18:00.990 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 13:18:00.998 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 13:18:01.001 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 42.5527ms
2025-06-02 13:18:01.002 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 13:18:01.003 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:18:01.043 +04:00 [INF] Request GET /api/documents started with correlation ID cd740bae-7e3f-4a8b-8822-f9e076b1e38f
2025-06-02 13:18:01.051 +04:00 [INF] Request GET /api/documents completed in 103ms with status 200 (Correlation ID: d0e79565-8f30-4978-b914-2cf92009d3ee)
2025-06-02 13:18:01.055 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:18:01.061 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 133.4145ms
2025-06-02 13:18:01.064 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:18:01.075 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:18:01.077 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:18:01.095 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 13:18:01.104 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 13:18:01.111 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 26.7398ms
2025-06-02 13:18:01.118 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:18:01.122 +04:00 [INF] Request GET /api/documents completed in 67ms with status 200 (Correlation ID: cd740bae-7e3f-4a8b-8822-f9e076b1e38f)
2025-06-02 13:18:01.128 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 125.1797ms
2025-06-02 13:18:36.824 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 13:18:36.858 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 86f6bd3c-b2a6-44f9-a3ec-0c994b15629f
2025-06-02 13:18:36.863 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:18:36.866 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 3ms with status 204 (Correlation ID: 86f6bd3c-b2a6-44f9-a3ec-0c994b15629f)
2025-06-02 13:18:36.870 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 45.9547ms
2025-06-02 13:18:36.876 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundarycuQAufu2R9fQliBu 473843
2025-06-02 13:18:36.909 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 90e59e99-4783-4a3c-b435-e39f1d4d81ce
2025-06-02 13:18:36.912 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:18:36.914 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:18:36.916 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:18:36.918 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:18:36.930 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 13:18:36.937 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 13:18:36.952 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 13:19:44.584 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> fdbf4a7c-d97f-4dcb-8a4b-e2bfc95919bb.pdf
2025-06-02 13:22:36.119 +04:00 [INF] Execution RecurringJobScheduler recovered from the Faulted state after 00:02:23.4458272 and is in the Running state now
2025-06-02 13:22:36.119 +04:00 [INF] Execution DelayedJobScheduler recovered from the Faulted state after 00:02:23.4458552 and is in the Running state now
2025-06-02 13:25:51.240 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-02 13:25:51.295 +04:00 [INF] Document added successfully: "6c6716af-0079-4400-b411-9254607486ea"
2025-06-02 13:25:51.296 +04:00 [INF] Document uploaded successfully: "6c6716af-0079-4400-b411-9254607486ea" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 13:25:56.933 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "6c6716af-0079-4400-b411-9254607486ea", Processing started: false
2025-06-02 13:25:56.938 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 13:25:56.942 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 440020.2604ms
2025-06-02 13:25:56.944 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:25:56.945 +04:00 [INF] Request POST /api/documents/upload completed in 440033ms with status 200 (Correlation ID: 90e59e99-4783-4a3c-b435-e39f1d4d81ce)
2025-06-02 13:25:56.948 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 440072.5007ms
