2025-06-02 13:52:24.972 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-02 13:52:25.102 +04:00 [INF] Hangfire SQL objects installed.
2025-06-02 13:52:25.117 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-02 13:52:25.567 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-02 13:52:25.591 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 13:52:25.857 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-02 13:52:25.859 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-02 13:52:25.912 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-02 13:52:25.914 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-02 13:52:25.917 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-02 13:52:25.929 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-02 13:52:25.930 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-02 13:52:25.931 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-02 13:52:25.962 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 13:52:25.963 +04:00 [INF] Hosting environment: Development
2025-06-02 13:52:25.965 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-02 13:52:25.997 +04:00 [INF] Server datapreprocessing-kevin11:51948:a6e464a6 successfully announced in 17.1894 ms
2025-06-02 13:52:26.091 +04:00 [INF] Server datapreprocessing-kevin11:51948:a6e464a6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-02 13:52:26.917 +04:00 [INF] Server datapreprocessing-kevin11:51948:a6e464a6 all the dispatchers started
2025-06-02 13:52:27.602 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-02 13:52:27.921 +04:00 [INF] Request GET / started with correlation ID ecbc9e2a-eab8-4d9c-847f-026c2eac7b91
2025-06-02 13:52:29.751 +04:00 [INF] Request GET / completed in 1825ms with status 404 (Correlation ID: ecbc9e2a-eab8-4d9c-847f-026c2eac7b91)
2025-06-02 13:52:29.766 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 2167.7713ms
2025-06-02 13:52:29.774 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-02 13:53:42.345 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 13:53:42.381 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 99bae830-3b39-4032-a8b1-e2450e8ab57a
2025-06-02 13:53:42.406 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:53:42.416 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 24ms with status 204 (Correlation ID: 99bae830-3b39-4032-a8b1-e2450e8ab57a)
2025-06-02 13:53:42.428 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 82.8381ms
2025-06-02 13:53:42.454 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryzwY4FBSIi5S5ZB9p 474190
2025-06-02 13:53:42.460 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 33d5722d-ab3c-4909-ace3-047f87f161e4
2025-06-02 13:53:42.464 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:53:42.672 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:53:42.680 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:53:42.715 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:53:44.098 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 13:53:44.101 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 13:53:44.103 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 13:53:44.106 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 13:53:44.107 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 13:53:44.108 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 13:53:45.170 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 13:53:45.243 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 13:53:45.515 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 13:53:45.698 +04:00 [INF] Executed DbCommand (16ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 13:54:01.156 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> 38e5fd42-edb9-4ab2-9de0-4edc581ef57d.pdf
2025-06-02 13:54:01.498 +04:00 [INF] Executed DbCommand (28ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-02 13:54:01.537 +04:00 [INF] Document added successfully: "56df4a49-57b0-46ba-9cb0-3396656a951d"
2025-06-02 13:54:01.539 +04:00 [INF] Document uploaded successfully: "56df4a49-57b0-46ba-9cb0-3396656a951d" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 13:56:23.464 +04:00 [INF] Background processing started for document "56df4a49-57b0-46ba-9cb0-3396656a951d"
2025-06-02 13:56:33.158 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "56df4a49-57b0-46ba-9cb0-3396656a951d", Processing started: true
2025-06-02 13:56:33.175 +04:00 [INF] Starting orchestrated processing for document "56df4a49-57b0-46ba-9cb0-3396656a951d": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 13:57:02.587 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 13:57:02.623 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@p24='?' (DbType = Guid), @p0='?' (DbType = Int32), @p1='?' (DbType = Double), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (DbType = DateTime), @p5='?', @p6='?', @p7='?' (DbType = Decimal), @p8='?', @p9='?', @p10='?', @p11='?' (DbType = Int64), @p12='?' (DbType = Boolean), @p13='?' (DbType = Boolean), @p14='?' (DbType = Object), @p15='?', @p16='?' (DbType = Object), @p17='?', @p18='?', @p19='?' (DbType = Int32), @p20='?' (DbType = DateTime), @p21='?', @p22='?', @p23='?', @p25='?' (DbType = Object), @p26='?' (DbType = Guid), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Guid), @p30='?', @p31='?' (DbType = Boolean), @p32='?' (DbType = Object), @p33='?' (DbType = DateTime), @p34='?'], CommandType='"Text"', CommandTimeout='30']
UPDATE "Documents" SET "ChunkCount" = @p0, "ClassificationConfidence" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "DetectedDomain" = @p6, "EstimatedCost" = @p7, "ExtractedText" = @p8, "FileHash" = @p9, "FileName" = @p10, "FileSize" = @p11, "IsDeleted" = @p12, "IsVectorized" = @p13, "Metadata" = @p14, "MimeType" = @p15, "ProcessingTime" = @p16, "Status" = @p17, "StoragePath" = @p18, "TotalTokens" = @p19, "UpdatedAt" = @p20, "UpdatedBy" = @p21, "VectorCollection" = @p22, "VectorDatabase" = @p23
WHERE "Id" = @p24 AND xmin = @p25
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
2025-06-02 13:57:02.642 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 199921.5207ms
2025-06-02 13:57:02.652 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:57:02.653 +04:00 [INF] Starting text extraction for document "56df4a49-57b0-46ba-9cb0-3396656a951d": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 13:57:02.660 +04:00 [INF] Request POST /api/documents/upload completed in 200196ms with status 200 (Correlation ID: 33d5722d-ab3c-4909-ace3-047f87f161e4)
2025-06-02 13:57:02.684 +04:00 [INF] Extracting text from INTRODUCTION AU DROIT DU TRAVAIL.pdf (MIME: application/pdf)
2025-06-02 13:57:02.699 +04:00 [INF] Text extraction successful for INTRODUCTION AU DROIT DU TRAVAIL.pdf. Length: 179
2025-06-02 13:57:02.704 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 200249.9577ms
2025-06-02 13:57:02.715 +04:00 [INF] Text extraction completed for document "56df4a49-57b0-46ba-9cb0-3396656a951d". Length: 179, Confidence: 0.70, Time: 54ms
2025-06-02 13:57:02.769 +04:00 [ERR] Error updating document "56df4a49-57b0-46ba-9cb0-3396656a951d"
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'DataPreprocessingDbContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies.get_StateManager()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.EntryWithoutDetectChanges(TEntity entity)
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.Update(TEntity entity)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 172
2025-06-02 13:57:02.820 +04:00 [ERR] Error in extraction step for document "56df4a49-57b0-46ba-9cb0-3396656a951d"
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'DataPreprocessingDbContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies.get_StateManager()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.EntryWithoutDetectChanges(TEntity entity)
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.Update(TEntity entity)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 172
   at LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.ExecuteExtractionStep(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Agents\OrchestrationAgent.cs:line 412
2025-06-02 13:58:09.837 +04:00 [ERR] Error updating document "56df4a49-57b0-46ba-9cb0-3396656a951d"
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'DataPreprocessingDbContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies.get_StateManager()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.EntryWithoutDetectChanges(TEntity entity)
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.Update(TEntity entity)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 172
2025-06-02 13:58:09.871 +04:00 [ERR] Error in classification step for document "56df4a49-57b0-46ba-9cb0-3396656a951d"
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'DataPreprocessingDbContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies.get_StateManager()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.EntryWithoutDetectChanges(TEntity entity)
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.Update(TEntity entity)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 172
   at LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.ExecuteClassificationStep(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Agents\OrchestrationAgent.cs:line 438
2025-06-02 13:58:33.371 +04:00 [ERR] Error updating document "56df4a49-57b0-46ba-9cb0-3396656a951d"
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'DataPreprocessingDbContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies.get_StateManager()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.EntryWithoutDetectChanges(TEntity entity)
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.Update(TEntity entity)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 172
2025-06-02 13:58:33.421 +04:00 [ERR] Error updating document "56df4a49-57b0-46ba-9cb0-3396656a951d" with error
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'DataPreprocessingDbContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies.get_StateManager()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.EntryWithoutDetectChanges(TEntity entity)
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.Update(TEntity entity)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 172
   at LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.UpdateDocumentWithError(Document document, String step, String errorMessage) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Agents\OrchestrationAgent.cs:line 707
2025-06-02 13:58:40.166 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 13:58:40.168 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 13:58:40.244 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID 49c8a695-432f-4759-ba37-355213f690ab
2025-06-02 13:58:40.247 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID b83e25b8-d1db-48a7-a762-7b4a1e2a3e37
2025-06-02 13:58:40.250 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:58:40.254 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:58:40.259 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 8ms with status 204 (Correlation ID: 49c8a695-432f-4759-ba37-355213f690ab)
2025-06-02 13:58:40.261 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 6ms with status 204 (Correlation ID: b83e25b8-d1db-48a7-a762-7b4a1e2a3e37)
2025-06-02 13:58:40.265 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 99.0142ms
2025-06-02 13:58:40.272 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 104.0318ms
2025-06-02 13:58:40.270 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 13:58:40.325 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID be447cc5-f9af-4b23-aaf4-5411969daa06
2025-06-02 13:58:40.327 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:58:40.333 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:58:40.335 +04:00 [INF] Request GET /api/documents/undefined completed in 7ms with status 404 (Correlation ID: be447cc5-f9af-4b23-aaf4-5411969daa06)
2025-06-02 13:58:40.338 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 68.5072ms
2025-06-02 13:58:40.343 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 13:58:40.345 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 13:58:40.354 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID e46b7ce8-0b2b-4362-9c49-4af08d94bdfb
2025-06-02 13:58:40.363 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:58:40.367 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:58:40.369 +04:00 [INF] Request GET /api/documents/undefined completed in 6ms with status 404 (Correlation ID: e46b7ce8-0b2b-4362-9c49-4af08d94bdfb)
2025-06-02 13:58:40.374 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 31.2115ms
2025-06-02 13:58:40.378 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 13:58:43.395 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 13:58:43.398 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 13:58:43.400 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID ab78b669-876d-4e22-9dda-8f633e1f53cf
2025-06-02 13:58:43.405 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID f7586529-af65-4d0f-a213-00d1201c2487
2025-06-02 13:58:43.407 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:58:43.410 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:58:43.412 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: ab78b669-876d-4e22-9dda-8f633e1f53cf)
2025-06-02 13:58:43.413 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: f7586529-af65-4d0f-a213-00d1201c2487)
2025-06-02 13:58:43.417 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 22.5079ms
2025-06-02 13:58:43.428 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 13:58:43.452 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 53.3909ms
2025-06-02 13:58:43.472 +04:00 [INF] Request GET /api/documents started with correlation ID e088dec2-7a40-4664-91c2-dc84fb1251d7
2025-06-02 13:58:43.485 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:58:43.487 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:58:43.491 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:58:43.502 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:58:43.584 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 13:58:43.607 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 13:58:43.661 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 13:58:43.698 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 189.8965ms
2025-06-02 13:58:43.700 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 13:58:43.702 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:58:43.706 +04:00 [INF] Request GET /api/documents started with correlation ID 6547aca3-1def-49d9-89dc-8a14c8bea01f
2025-06-02 13:58:43.707 +04:00 [INF] Request GET /api/documents completed in 223ms with status 200 (Correlation ID: e088dec2-7a40-4664-91c2-dc84fb1251d7)
2025-06-02 13:58:43.710 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:58:43.713 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 285.6087ms
2025-06-02 13:58:43.714 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:58:43.723 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:58:43.727 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:58:43.836 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 13:58:43.844 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 13:58:43.845 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 113.5185ms
2025-06-02 13:58:43.847 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 13:58:43.848 +04:00 [INF] Request GET /api/documents completed in 138ms with status 200 (Correlation ID: 6547aca3-1def-49d9-89dc-8a14c8bea01f)
2025-06-02 13:58:43.851 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 150.7175ms
2025-06-02 13:59:13.422 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d - null null
2025-06-02 13:59:13.422 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d - null null
2025-06-02 13:59:13.432 +04:00 [INF] Request OPTIONS /api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d started with correlation ID 42900462-c917-4959-9e94-a4bc7b34bf94
2025-06-02 13:59:13.439 +04:00 [INF] Request OPTIONS /api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d started with correlation ID b68de11e-064d-4ae5-b87d-4f27fd8782fe
2025-06-02 13:59:13.443 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:59:13.445 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:59:13.447 +04:00 [INF] Request OPTIONS /api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d completed in 4ms with status 204 (Correlation ID: 42900462-c917-4959-9e94-a4bc7b34bf94)
2025-06-02 13:59:13.451 +04:00 [INF] Request OPTIONS /api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d completed in 6ms with status 204 (Correlation ID: b68de11e-064d-4ae5-b87d-4f27fd8782fe)
2025-06-02 13:59:13.459 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d - 204 null null 36.3156ms
2025-06-02 13:59:13.461 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d - application/json null
2025-06-02 13:59:13.464 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d - 204 null null 41.6764ms
2025-06-02 13:59:13.541 +04:00 [INF] Request GET /api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d started with correlation ID 0b496be2-5823-4cd0-8b4c-18854d0c5435
2025-06-02 13:59:13.551 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:59:13.553 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:59:13.555 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:59:13.560 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:59:13.576 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 13:59:13.593 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 13:59:13.618 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 13:59:13.621 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 57.88ms
2025-06-02 13:59:13.623 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d - application/json null
2025-06-02 13:59:13.624 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:59:13.629 +04:00 [INF] Request GET /api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d started with correlation ID f7dc7442-56f5-4684-ad83-1d82aca994f3
2025-06-02 13:59:13.629 +04:00 [INF] Request GET /api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d completed in 78ms with status 200 (Correlation ID: 0b496be2-5823-4cd0-8b4c-18854d0c5435)
2025-06-02 13:59:13.631 +04:00 [INF] CORS policy execution successful.
2025-06-02 13:59:13.636 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d - 200 null application/json; charset=utf-8 174.8424ms
2025-06-02 13:59:13.638 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 13:59:13.647 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:59:13.652 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 13:59:13.665 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 13:59:13.675 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 13:59:13.678 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 20.4384ms
2025-06-02 13:59:13.679 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 13:59:13.680 +04:00 [INF] Request GET /api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d completed in 48ms with status 200 (Correlation ID: f7dc7442-56f5-4684-ad83-1d82aca994f3)
2025-06-02 13:59:13.683 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/56df4a49-57b0-46ba-9cb0-3396656a951d - 200 null application/json; charset=utf-8 59.4576ms
2025-06-02 14:00:03.705 +04:00 [INF] Generating processing statistics
2025-06-02 14:03:58.571 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:03:58.572 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:03:58.588 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 60221511-48f8-4d98-bc03-58f853420c7d
2025-06-02 14:03:58.592 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 8e601df0-20bf-468d-bb20-8d48f408ad3f
2025-06-02 14:03:58.594 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:03:58.596 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:03:58.597 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 60221511-48f8-4d98-bc03-58f853420c7d)
2025-06-02 14:03:58.598 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: 8e601df0-20bf-468d-bb20-8d48f408ad3f)
2025-06-02 14:03:58.601 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 30.4134ms
2025-06-02 14:03:58.607 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:03:58.607 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 35.3469ms
2025-06-02 14:03:58.627 +04:00 [INF] Request GET /api/documents started with correlation ID 6d9230be-273c-4d81-9ce2-ed13535cf975
2025-06-02 14:03:58.635 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:03:58.645 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:03:58 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 14:03:58.686 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:03:58 AM'.
2025-06-02 14:03:58.689 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:03:58 AM'.
2025-06-02 14:03:58.695 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 14:03:58.713 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 14:03:58.716 +04:00 [INF] Request GET /api/documents completed in 81ms with status 401 (Correlation ID: 6d9230be-273c-4d81-9ce2-ed13535cf975)
2025-06-02 14:03:58.719 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 111.6365ms
2025-06-02 14:03:58.723 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:03:58.729 +04:00 [INF] Request GET /api/documents started with correlation ID 58ead722-861f-4805-bd4f-f92a771e570f
2025-06-02 14:03:58.733 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:03:58.736 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:03:58 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 14:03:58.740 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:03:58 AM'.
2025-06-02 14:03:58.741 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:03:58 AM'.
2025-06-02 14:03:58.744 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 14:03:58.746 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 14:03:58.748 +04:00 [INF] Request GET /api/documents completed in 15ms with status 401 (Correlation ID: 58ead722-861f-4805-bd4f-f92a771e570f)
2025-06-02 14:03:58.752 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 28.8088ms
2025-06-02 14:04:05.052 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:04:05.056 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:04:05.057 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 0e32e6cc-5000-4588-85b7-018ef021d99a
2025-06-02 14:04:05.060 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 465dbf8b-80f8-4365-a4f0-42cf632fe1d4
2025-06-02 14:04:05.062 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:05.064 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:05.066 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: 465dbf8b-80f8-4365-a4f0-42cf632fe1d4)
2025-06-02 14:04:05.065 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 0e32e6cc-5000-4588-85b7-018ef021d99a)
2025-06-02 14:04:05.069 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 13.1426ms
2025-06-02 14:04:05.071 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:04:05.072 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 19.35ms
2025-06-02 14:04:05.082 +04:00 [INF] Request GET /api/documents started with correlation ID bb2ec5be-02cd-479d-bd1c-becfddb44994
2025-06-02 14:04:05.089 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:05.091 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:05 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 14:04:05.096 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:05 AM'.
2025-06-02 14:04:05.098 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:05 AM'.
2025-06-02 14:04:05.101 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 14:04:05.105 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 14:04:05.108 +04:00 [INF] Request GET /api/documents completed in 19ms with status 401 (Correlation ID: bb2ec5be-02cd-479d-bd1c-becfddb44994)
2025-06-02 14:04:05.111 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 40.043ms
2025-06-02 14:04:05.118 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:04:05.128 +04:00 [INF] Request GET /api/documents started with correlation ID 9bc4c478-21ac-40cc-93eb-5f6f598fc082
2025-06-02 14:04:05.139 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:05.143 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:05 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 14:04:05.148 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:05 AM'.
2025-06-02 14:04:05.150 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:05 AM'.
2025-06-02 14:04:05.152 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 14:04:05.154 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 14:04:05.157 +04:00 [INF] Request GET /api/documents completed in 17ms with status 401 (Correlation ID: 9bc4c478-21ac-40cc-93eb-5f6f598fc082)
2025-06-02 14:04:05.162 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 44.3116ms
2025-06-02 14:04:26.144 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:04:26.146 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:04:26.149 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 3f827372-f6de-40ff-a678-55694a1f0a25
2025-06-02 14:04:26.154 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 5ebe0a81-e578-452c-8f2e-bd570da3c4ff
2025-06-02 14:04:26.156 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:26.158 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:26.160 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: 3f827372-f6de-40ff-a678-55694a1f0a25)
2025-06-02 14:04:26.161 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 5ebe0a81-e578-452c-8f2e-bd570da3c4ff)
2025-06-02 14:04:26.165 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 20.1594ms
2025-06-02 14:04:26.167 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:04:26.168 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 22.0102ms
2025-06-02 14:04:26.184 +04:00 [INF] Request GET /api/documents started with correlation ID 989a4ee8-5c85-4b83-9f12-1e54f3b28b60
2025-06-02 14:04:26.190 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:26.193 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:26 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 14:04:26.198 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:26 AM'.
2025-06-02 14:04:26.200 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:26 AM'.
2025-06-02 14:04:26.202 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 14:04:26.204 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 14:04:26.206 +04:00 [INF] Request GET /api/documents completed in 15ms with status 401 (Correlation ID: 989a4ee8-5c85-4b83-9f12-1e54f3b28b60)
2025-06-02 14:04:26.209 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 41.5629ms
2025-06-02 14:04:26.214 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:04:26.220 +04:00 [INF] Request GET /api/documents started with correlation ID f63ac0c8-3923-4030-917d-0f7b6c9fa98f
2025-06-02 14:04:26.222 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:04:26.225 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:26 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 14:04:26.229 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:26 AM'.
2025-06-02 14:04:26.231 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:57:57 AM', Current time (UTC): '6/2/2025 10:04:26 AM'.
2025-06-02 14:04:26.234 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 14:04:26.237 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 14:04:26.239 +04:00 [INF] Request GET /api/documents completed in 16ms with status 401 (Correlation ID: f63ac0c8-3923-4030-917d-0f7b6c9fa98f)
2025-06-02 14:04:26.242 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 27.9825ms
2025-06-02 14:05:28.756 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:05:28.813 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:05:28.819 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID e7735c0d-6eec-44a3-bb86-7180718602b7
2025-06-02 14:05:28.830 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 290eea4e-af08-4d46-8e39-738b214135a3
2025-06-02 14:05:28.833 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:05:28.836 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:05:28.838 +04:00 [INF] Request OPTIONS /api/documents completed in 5ms with status 204 (Correlation ID: e7735c0d-6eec-44a3-bb86-7180718602b7)
2025-06-02 14:05:28.839 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 290eea4e-af08-4d46-8e39-738b214135a3)
2025-06-02 14:05:28.845 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 88.9398ms
2025-06-02 14:05:28.889 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 76.2438ms
2025-06-02 14:05:28.888 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:05:28.950 +04:00 [INF] Request GET /api/documents started with correlation ID 3429568b-0b03-47e5-a654-037d26ade1ed
2025-06-02 14:05:28.957 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:05:28.961 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 14:05:28.963 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:05:28.981 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 14:05:29.035 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 14:05:29.039 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 14:05:29.053 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 65.0488ms
2025-06-02 14:05:29.054 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:05:29.058 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:05:29.063 +04:00 [INF] Request GET /api/documents started with correlation ID 8d50d951-d416-4a10-9ca2-9c419726af4c
2025-06-02 14:05:29.065 +04:00 [INF] Request GET /api/documents completed in 108ms with status 200 (Correlation ID: 3429568b-0b03-47e5-a654-037d26ade1ed)
2025-06-02 14:05:29.069 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:05:29.077 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 188.935ms
2025-06-02 14:05:29.084 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 14:05:29.110 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:05:29.118 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 14:05:29.137 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 14:05:29.145 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 14:05:29.150 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 21.8015ms
2025-06-02 14:05:29.154 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:05:29.158 +04:00 [INF] Request GET /api/documents completed in 88ms with status 200 (Correlation ID: 8d50d951-d416-4a10-9ca2-9c419726af4c)
2025-06-02 14:05:29.166 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 111.3015ms
2025-06-02 14:05:56.436 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 14:05:56.454 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 24f1dad7-acfe-4fc4-ba1d-f168fc9c2f55
2025-06-02 14:05:56.462 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:05:56.467 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 4ms with status 204 (Correlation ID: 24f1dad7-acfe-4fc4-ba1d-f168fc9c2f55)
2025-06-02 14:05:56.477 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 40.5712ms
2025-06-02 14:05:56.514 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryri2xnYKsaXDwa5dj 474174
2025-06-02 14:05:56.593 +04:00 [INF] Request POST /api/documents/upload started with correlation ID f24a9a5c-2215-4194-bb0e-0887ba2bedf7
2025-06-02 14:05:56.595 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:05:56.597 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 14:05:56.599 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 14:05:56.605 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 14:05:56.623 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 14:05:56.633 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 14:05:56.636 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 14:05:56.651 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 14:06:03.048 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> 3e419ab4-7e56-4eef-871a-80a6851ba6e5.pdf
2025-06-02 14:06:03.068 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-02 14:06:03.077 +04:00 [INF] Document added successfully: "a4affcfc-0e03-4827-8dcd-4e78400796eb"
2025-06-02 14:06:03.078 +04:00 [INF] Document uploaded successfully: "a4affcfc-0e03-4827-8dcd-4e78400796eb" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 14:06:27.842 +04:00 [INF] Starting orchestrated processing for document "a4affcfc-0e03-4827-8dcd-4e78400796eb": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 14:06:30.637 +04:00 [INF] Background processing started for document "a4affcfc-0e03-4827-8dcd-4e78400796eb"
2025-06-02 14:06:49.316 +04:00 [INF] Executed DbCommand (1,652ms) [Parameters=[@p24='?' (DbType = Guid), @p0='?' (DbType = Int32), @p1='?' (DbType = Double), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (DbType = DateTime), @p5='?', @p6='?', @p7='?' (DbType = Decimal), @p8='?', @p9='?', @p10='?', @p11='?' (DbType = Int64), @p12='?' (DbType = Boolean), @p13='?' (DbType = Boolean), @p14='?' (DbType = Object), @p15='?', @p16='?' (DbType = Object), @p17='?', @p18='?', @p19='?' (DbType = Int32), @p20='?' (DbType = DateTime), @p21='?', @p22='?', @p23='?', @p25='?' (DbType = Object), @p26='?' (DbType = Guid), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Guid), @p30='?', @p31='?' (DbType = Boolean), @p32='?' (DbType = Object), @p33='?' (DbType = DateTime), @p34='?'], CommandType='"Text"', CommandTimeout='30']
UPDATE "Documents" SET "ChunkCount" = @p0, "ClassificationConfidence" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "DetectedDomain" = @p6, "EstimatedCost" = @p7, "ExtractedText" = @p8, "FileHash" = @p9, "FileName" = @p10, "FileSize" = @p11, "IsDeleted" = @p12, "IsVectorized" = @p13, "Metadata" = @p14, "MimeType" = @p15, "ProcessingTime" = @p16, "Status" = @p17, "StoragePath" = @p18, "TotalTokens" = @p19, "UpdatedAt" = @p20, "UpdatedBy" = @p21, "VectorCollection" = @p22, "VectorDatabase" = @p23
WHERE "Id" = @p24 AND xmin = @p25
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
2025-06-02 14:06:52.044 +04:00 [INF] Starting text extraction for document "a4affcfc-0e03-4827-8dcd-4e78400796eb": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 14:06:52.059 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "a4affcfc-0e03-4827-8dcd-4e78400796eb", Processing started: true
2025-06-02 14:06:52.060 +04:00 [INF] Extracting text from INTRODUCTION AU DROIT DU TRAVAIL.pdf (MIME: application/pdf)
2025-06-02 14:06:59.323 +04:00 [INF] Text extraction successful for INTRODUCTION AU DROIT DU TRAVAIL.pdf. Length: 179
2025-06-02 14:07:00.495 +04:00 [INF] Text extraction completed for document "a4affcfc-0e03-4827-8dcd-4e78400796eb". Length: 179, Confidence: 0.70, Time: 8437ms
2025-06-02 14:07:00.510 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 14:07:03.240 +04:00 [INF] Executed DbCommand (2,723ms) [Parameters=[@p24='?' (DbType = Guid), @p0='?' (DbType = Int32), @p1='?' (DbType = Double), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (DbType = DateTime), @p5='?', @p6='?', @p7='?' (DbType = Decimal), @p8='?', @p9='?', @p10='?', @p11='?' (DbType = Int64), @p12='?' (DbType = Boolean), @p13='?' (DbType = Boolean), @p14='?' (DbType = Object), @p15='?', @p16='?' (DbType = Object), @p17='?', @p18='?', @p19='?' (DbType = Int32), @p20='?' (DbType = DateTime), @p21='?', @p22='?', @p23='?', @p25='?' (DbType = Object), @p26='?' (DbType = Guid), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Guid), @p30='?', @p31='?' (DbType = Boolean), @p32='?' (DbType = Object), @p33='?' (DbType = DateTime), @p34='?'], CommandType='"Text"', CommandTimeout='30']
UPDATE "Documents" SET "ChunkCount" = @p0, "ClassificationConfidence" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "DetectedDomain" = @p6, "EstimatedCost" = @p7, "ExtractedText" = @p8, "FileHash" = @p9, "FileName" = @p10, "FileSize" = @p11, "IsDeleted" = @p12, "IsVectorized" = @p13, "Metadata" = @p14, "MimeType" = @p15, "ProcessingTime" = @p16, "Status" = @p17, "StoragePath" = @p18, "TotalTokens" = @p19, "UpdatedAt" = @p20, "UpdatedBy" = @p21, "VectorCollection" = @p22, "VectorDatabase" = @p23
WHERE "Id" = @p24 AND xmin = @p25
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
2025-06-02 14:07:03.243 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 66633.6026ms
2025-06-02 14:07:03.287 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 14:07:03.295 +04:00 [INF] Request POST /api/documents/upload completed in 66699ms with status 200 (Correlation ID: f24a9a5c-2215-4194-bb0e-0887ba2bedf7)
2025-06-02 14:07:18.624 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 82109.7556ms
2025-06-02 14:07:22.908 +04:00 [ERR] Error updating document "a4affcfc-0e03-4827-8dcd-4e78400796eb"
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'DataPreprocessingDbContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies.get_StateManager()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.EntryWithoutDetectChanges(TEntity entity)
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.Update(TEntity entity)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 172
2025-06-02 14:07:23.011 +04:00 [ERR] Error in classification step for document "a4affcfc-0e03-4827-8dcd-4e78400796eb"
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'DataPreprocessingDbContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies.get_StateManager()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.EntryWithoutDetectChanges(TEntity entity)
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.Update(TEntity entity)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 172
   at LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.ExecuteClassificationStep(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Agents\OrchestrationAgent.cs:line 438
2025-06-02 14:07:44.739 +04:00 [ERR] Error updating document "a4affcfc-0e03-4827-8dcd-4e78400796eb"
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'DataPreprocessingDbContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies.get_StateManager()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.EntryWithoutDetectChanges(TEntity entity)
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.Update(TEntity entity)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 172
2025-06-02 14:07:44.786 +04:00 [ERR] Error updating document "a4affcfc-0e03-4827-8dcd-4e78400796eb" with error
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'DataPreprocessingDbContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies.get_StateManager()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.EntryWithoutDetectChanges(TEntity entity)
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.Update(TEntity entity)
   at LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsync(Document document, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Repositories\DocumentRepository.cs:line 172
   at LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.UpdateDocumentWithError(Document document, String step, String errorMessage) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Agents\OrchestrationAgent.cs:line 707
2025-06-02 14:08:01.881 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 14:08:01.881 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 14:08:01.892 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID 22d5ff66-cdd3-4a6e-9513-8d225c37edee
2025-06-02 14:08:01.899 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID 31edf753-23f5-422a-b159-cb7d23bdc39e
2025-06-02 14:08:01.901 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:01.908 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:01.912 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 11ms with status 204 (Correlation ID: 22d5ff66-cdd3-4a6e-9513-8d225c37edee)
2025-06-02 14:08:01.914 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 6ms with status 204 (Correlation ID: 31edf753-23f5-422a-b159-cb7d23bdc39e)
2025-06-02 14:08:01.920 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 39.4425ms
2025-06-02 14:08:01.925 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 14:08:01.929 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 47.7113ms
2025-06-02 14:08:01.954 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID 9c85d555-c6c8-4a92-be1f-f6e7140d3b4c
2025-06-02 14:08:02.011 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:02.013 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 14:08:02.015 +04:00 [INF] Request GET /api/documents/undefined completed in 4ms with status 404 (Correlation ID: 9c85d555-c6c8-4a92-be1f-f6e7140d3b4c)
2025-06-02 14:08:02.021 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 94.992ms
2025-06-02 14:08:02.027 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 14:08:02.032 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 14:08:02.038 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID 0f19a995-dbd2-4b8c-a9d2-646f25791d8a
2025-06-02 14:08:02.043 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:02.046 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 14:08:02.048 +04:00 [INF] Request GET /api/documents/undefined completed in 4ms with status 404 (Correlation ID: 0f19a995-dbd2-4b8c-a9d2-646f25791d8a)
2025-06-02 14:08:02.052 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 25.9361ms
2025-06-02 14:08:02.059 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 14:08:05.976 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:08:05.986 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:08:06.006 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID aded5093-d536-4a02-bfea-f8bd524651e3
2025-06-02 14:08:06.014 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 509d8f81-c7fd-4786-a953-cbbed610ffe0
2025-06-02 14:08:06.017 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:06.025 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:06.029 +04:00 [INF] Request OPTIONS /api/documents completed in 12ms with status 204 (Correlation ID: aded5093-d536-4a02-bfea-f8bd524651e3)
2025-06-02 14:08:06.033 +04:00 [INF] Request OPTIONS /api/documents completed in 7ms with status 204 (Correlation ID: 509d8f81-c7fd-4786-a953-cbbed610ffe0)
2025-06-02 14:08:06.043 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 66.4ms
2025-06-02 14:08:06.046 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:08:06.053 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 66.1016ms
2025-06-02 14:08:06.064 +04:00 [INF] Request GET /api/documents started with correlation ID 8733fedc-2fbe-4066-9c15-cfa1cd0e6389
2025-06-02 14:08:06.073 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:06.076 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 14:08:06.079 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:06.084 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 14:08:06.109 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 14:08:06.118 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 14:08:06.128 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 38.4495ms
2025-06-02 14:08:06.130 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:06.131 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:08:06.139 +04:00 [INF] Request GET /api/documents completed in 65ms with status 200 (Correlation ID: 8733fedc-2fbe-4066-9c15-cfa1cd0e6389)
2025-06-02 14:08:06.144 +04:00 [INF] Request GET /api/documents started with correlation ID d9623524-057a-4e1e-9db4-3cb93f3316d2
2025-06-02 14:08:06.147 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 100.8976ms
2025-06-02 14:08:06.152 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:06.174 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 14:08:06.179 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:06.184 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 14:08:06.199 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 14:08:06.210 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 14:08:06.212 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 20.1743ms
2025-06-02 14:08:06.215 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:06.217 +04:00 [INF] Request GET /api/documents completed in 64ms with status 200 (Correlation ID: d9623524-057a-4e1e-9db4-3cb93f3316d2)
2025-06-02 14:08:06.222 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 91.0958ms
2025-06-02 14:08:10.879 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb - null null
2025-06-02 14:08:10.879 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb - null null
2025-06-02 14:08:10.894 +04:00 [INF] Request OPTIONS /api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb started with correlation ID c43a49d3-492f-4198-b2d3-8d6f15bd047c
2025-06-02 14:08:10.909 +04:00 [INF] Request OPTIONS /api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb started with correlation ID f7c3f44c-87d9-486b-8544-b910ae879405
2025-06-02 14:08:10.914 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:10.943 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:10.955 +04:00 [INF] Request OPTIONS /api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb completed in 41ms with status 204 (Correlation ID: c43a49d3-492f-4198-b2d3-8d6f15bd047c)
2025-06-02 14:08:10.960 +04:00 [INF] Request OPTIONS /api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb completed in 17ms with status 204 (Correlation ID: f7c3f44c-87d9-486b-8544-b910ae879405)
2025-06-02 14:08:10.967 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb - 204 null null 88.0665ms
2025-06-02 14:08:10.970 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb - application/json null
2025-06-02 14:08:10.974 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb - 204 null null 95.0046ms
2025-06-02 14:08:10.997 +04:00 [INF] Request GET /api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb started with correlation ID fa34d882-342d-4d91-9c79-21dc8d5043bf
2025-06-02 14:08:11.009 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:11.012 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 14:08:11.017 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:11.029 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 14:08:11.040 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 14:08:11.046 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 14:08:11.050 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 17.7805ms
2025-06-02 14:08:11.054 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb - application/json null
2025-06-02 14:08:11.055 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:11.060 +04:00 [INF] Request GET /api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb started with correlation ID b1fba17f-d3c8-4569-bcee-4b3c071d34ef
2025-06-02 14:08:11.066 +04:00 [INF] Request GET /api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb completed in 57ms with status 200 (Correlation ID: fa34d882-342d-4d91-9c79-21dc8d5043bf)
2025-06-02 14:08:11.069 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:11.074 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb - 200 null application/json; charset=utf-8 103.3264ms
2025-06-02 14:08:11.076 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 14:08:11.083 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:11.087 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 14:08:11.097 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 14:08:11.104 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 14:08:11.107 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 15.986ms
2025-06-02 14:08:11.109 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:11.112 +04:00 [INF] Request GET /api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb completed in 42ms with status 200 (Correlation ID: b1fba17f-d3c8-4569-bcee-4b3c071d34ef)
2025-06-02 14:08:11.117 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/a4affcfc-0e03-4827-8dcd-4e78400796eb - 200 null application/json; charset=utf-8 63.3927ms
2025-06-02 14:08:19.946 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:08:19.947 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 14:08:19.954 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 1f6d5b6f-31c3-4c96-a4d4-244195becc34
2025-06-02 14:08:19.961 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID be4c6f73-1b37-469a-a882-c88b66aac137
2025-06-02 14:08:19.964 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:19.968 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:19.970 +04:00 [INF] Request OPTIONS /api/documents completed in 5ms with status 204 (Correlation ID: 1f6d5b6f-31c3-4c96-a4d4-244195becc34)
2025-06-02 14:08:19.971 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: be4c6f73-1b37-469a-a882-c88b66aac137)
2025-06-02 14:08:19.978 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 31.6826ms
2025-06-02 14:08:19.981 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:08:19.982 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 34.9361ms
2025-06-02 14:08:19.995 +04:00 [INF] Request GET /api/documents started with correlation ID 9a42403c-a2d7-4f2d-962e-a80618b72e86
2025-06-02 14:08:20.004 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:20.006 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 14:08:20.008 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:20.010 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 14:08:20.027 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 14:08:20.070 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 14:08:20.075 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 59.8132ms
2025-06-02 14:08:20.079 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 14:08:20.080 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:20.092 +04:00 [INF] Request GET /api/documents started with correlation ID 4778e866-d758-4552-937d-713789ad4cb0
2025-06-02 14:08:20.094 +04:00 [INF] Request GET /api/documents completed in 90ms with status 200 (Correlation ID: 9a42403c-a2d7-4f2d-962e-a80618b72e86)
2025-06-02 14:08:20.097 +04:00 [INF] CORS policy execution successful.
2025-06-02 14:08:20.101 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 119.4126ms
2025-06-02 14:08:20.103 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 14:08:20.110 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:20.113 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 14:08:20.132 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 14:08:20.144 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 14:08:20.147 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 30.4283ms
2025-06-02 14:08:20.150 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 14:08:20.153 +04:00 [INF] Request GET /api/documents completed in 56ms with status 200 (Correlation ID: 4778e866-d758-4552-937d-713789ad4cb0)
2025-06-02 14:08:20.158 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 79.0044ms
