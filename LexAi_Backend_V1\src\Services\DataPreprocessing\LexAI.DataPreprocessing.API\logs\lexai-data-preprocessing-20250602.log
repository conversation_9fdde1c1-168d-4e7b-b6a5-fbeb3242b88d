2025-06-02 17:26:51.311 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-02 17:26:51.518 +04:00 [INF] Hangfire SQL objects installed.
2025-06-02 17:26:51.542 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-02 17:26:52.208 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-02 17:26:52.231 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 17:26:52.619 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-02 17:26:52.622 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-02 17:26:52.717 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-02 17:26:52.719 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-02 17:26:52.722 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-02 17:26:52.723 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-02 17:26:52.735 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-02 17:26:52.737 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-02 17:26:52.779 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 17:26:52.782 +04:00 [INF] Hosting environment: Development
2025-06-02 17:26:52.785 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-02 17:26:52.855 +04:00 [INF] Server datapreprocessing-kevin11:10456:d16de16b successfully announced in 41.6733 ms
2025-06-02 17:26:52.907 +04:00 [INF] Server datapreprocessing-kevin11:10456:d16de16b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-02 17:26:53.022 +04:00 [INF] Server datapreprocessing-kevin11:10456:d16de16b all the dispatchers started
2025-06-02 17:26:54.601 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-02 17:26:54.769 +04:00 [INF] Generating processing statistics
2025-06-02 17:26:54.886 +04:00 [INF] Request GET / started with correlation ID 1ff2629f-5131-454e-ae9e-6ed97dc51477
2025-06-02 17:26:56.606 +04:00 [INF] Request GET / completed in 1713ms with status 404 (Correlation ID: 1ff2629f-5131-454e-ae9e-6ed97dc51477)
2025-06-02 17:26:56.618 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 2018.8031ms
2025-06-02 17:26:56.626 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-02 17:27:25.541 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 17:27:25.541 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 17:27:25.624 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 90cf1c82-1116-45b7-90bd-7d68dc6d02de
2025-06-02 17:27:25.624 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID e043d12e-580a-46d7-a470-b05fcf6a42bf
2025-06-02 17:27:25.645 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:27:25.645 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:27:25.651 +04:00 [INF] Request OPTIONS /api/documents completed in 8ms with status 204 (Correlation ID: e043d12e-580a-46d7-a470-b05fcf6a42bf)
2025-06-02 17:27:25.651 +04:00 [INF] Request OPTIONS /api/documents completed in 11ms with status 204 (Correlation ID: 90cf1c82-1116-45b7-90bd-7d68dc6d02de)
2025-06-02 17:27:25.655 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 113.8508ms
2025-06-02 17:27:25.659 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 117.9148ms
2025-06-02 17:27:25.659 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 17:27:25.685 +04:00 [INF] Request GET /api/documents started with correlation ID 8eeabb4a-4673-489d-9ebc-df1580d38e17
2025-06-02 17:27:25.687 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:27:25.915 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 12:09:07 PM', Current time (UTC): '6/2/2025 1:27:25 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 17:27:25.919 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 12:09:07 PM', Current time (UTC): '6/2/2025 1:27:25 PM'.
2025-06-02 17:27:25.922 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 12:09:07 PM', Current time (UTC): '6/2/2025 1:27:25 PM'.
2025-06-02 17:27:25.929 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 17:27:25.934 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 17:27:25.935 +04:00 [INF] Request GET /api/documents completed in 248ms with status 401 (Correlation ID: 8eeabb4a-4673-489d-9ebc-df1580d38e17)
2025-06-02 17:27:25.939 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 279.8912ms
2025-06-02 17:27:25.943 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 17:27:25.950 +04:00 [INF] Request GET /api/documents started with correlation ID dbdca700-becf-4206-a08d-18c8e0a65891
2025-06-02 17:27:25.952 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:27:25.955 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 12:09:07 PM', Current time (UTC): '6/2/2025 1:27:25 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 17:27:25.959 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 12:09:07 PM', Current time (UTC): '6/2/2025 1:27:25 PM'.
2025-06-02 17:27:25.960 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 12:09:07 PM', Current time (UTC): '6/2/2025 1:27:25 PM'.
2025-06-02 17:27:25.962 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 17:27:25.963 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 17:27:25.965 +04:00 [INF] Request GET /api/documents completed in 13ms with status 401 (Correlation ID: dbdca700-becf-4206-a08d-18c8e0a65891)
2025-06-02 17:27:25.968 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 24.4128ms
2025-06-02 17:27:27.787 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 17:27:27.795 +04:00 [INF] Request GET /api/documents started with correlation ID d8f60e70-f933-4322-be7c-a6b3f5946a2e
2025-06-02 17:27:27.800 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:27:27.815 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:27:27.820 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:27:27.854 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 17:27:29.511 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 17:27:29.516 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 17:27:29.518 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 17:27:29.522 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 17:27:29.525 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 17:27:29.528 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 17:27:31.031 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 17:27:31.151 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 17:27:31.171 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:27:31.217 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 17:27:31.218 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 3357.7959ms
2025-06-02 17:27:31.222 +04:00 [INF] Request GET /api/documents started with correlation ID 324c4af5-4a86-46f7-86dc-16358122c169
2025-06-02 17:27:31.223 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:27:31.224 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:27:31.227 +04:00 [INF] Request GET /api/documents completed in 3427ms with status 200 (Correlation ID: d8f60e70-f933-4322-be7c-a6b3f5946a2e)
2025-06-02 17:27:31.227 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:27:31.231 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:27:31.233 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 17:27:31.239 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 3451.2657ms
2025-06-02 17:27:31.320 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 17:27:31.324 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:27:31.329 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 91.0495ms
2025-06-02 17:27:31.335 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:27:31.338 +04:00 [INF] Request GET /api/documents completed in 113ms with status 200 (Correlation ID: 324c4af5-4a86-46f7-86dc-16358122c169)
2025-06-02 17:27:31.341 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 124.6401ms
2025-06-02 17:28:06.349 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 17:28:06.369 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 88bd7da0-7066-48c3-a866-196297f292a8
2025-06-02 17:28:06.373 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:28:06.376 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 3ms with status 204 (Correlation ID: 88bd7da0-7066-48c3-a866-196297f292a8)
2025-06-02 17:28:06.388 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 38.8839ms
2025-06-02 17:28:06.394 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundarysgYq1avE8YiXZrBV 474176
2025-06-02 17:28:06.415 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 4ab07104-1e11-45a5-8f5c-eaf9a8e28ea3
2025-06-02 17:28:06.420 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:28:06.421 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:28:06.422 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 17:28:06.428 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 17:28:06.513 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 17:28:06.525 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 17:28:06.533 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 17:28:06.544 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 17:28:06.559 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 17:28:10.348 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> ac906a2e-f82a-4856-9f56-498f5d5acacf.pdf
2025-06-02 17:28:10.719 +04:00 [INF] Executed DbCommand (15ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-02 17:28:10.774 +04:00 [INF] Document added successfully: "2843cb99-80dd-497f-af73-ca2803480940"
2025-06-02 17:28:10.781 +04:00 [INF] Document uploaded successfully: "2843cb99-80dd-497f-af73-ca2803480940" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 17:28:13.631 +04:00 [INF] Background processing started for document "2843cb99-80dd-497f-af73-ca2803480940"
2025-06-02 17:28:13.635 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "2843cb99-80dd-497f-af73-ca2803480940", Processing started: true
2025-06-02 17:28:29.261 +04:00 [INF] Starting orchestrated processing for document "2843cb99-80dd-497f-af73-ca2803480940": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 17:28:29.277 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 17:28:33.061 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 26629.6475ms
2025-06-02 17:28:33.073 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 17:28:33.112 +04:00 [INF] Request POST /api/documents/upload completed in 26692ms with status 200 (Correlation ID: 4ab07104-1e11-45a5-8f5c-eaf9a8e28ea3)
2025-06-02 17:28:33.128 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 26733.9478ms
2025-06-02 17:28:33.301 +04:00 [INF] Starting text extraction for document "2843cb99-80dd-497f-af73-ca2803480940": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 17:28:33.321 +04:00 [INF] Extracting text from INTRODUCTION AU DROIT DU TRAVAIL.pdf (MIME: application/pdf)
2025-06-02 17:28:33.327 +04:00 [INF] Text extraction successful for INTRODUCTION AU DROIT DU TRAVAIL.pdf. Length: 179
2025-06-02 17:28:33.332 +04:00 [INF] Text extraction completed for document "2843cb99-80dd-497f-af73-ca2803480940". Length: 179, Confidence: 0.70, Time: 29ms
2025-06-02 17:28:33.405 +04:00 [INF] Starting classification for document "2843cb99-80dd-497f-af73-ca2803480940": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 17:28:33.409 +04:00 [INF] Classifying text into legal domains. Text length: 179
2025-06-02 17:28:33.424 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-02 17:28:33.428 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-02 17:28:35.562 +04:00 [INF] Received HTTP response headers after 2128.3062ms - 429
2025-06-02 17:28:35.566 +04:00 [INF] End processing HTTP request after 2142.7135ms - 429
2025-06-02 17:28:35.571 +04:00 [WRN] AI classification failed, using rule-based result: "Labor" (0.33)
2025-06-02 17:28:35.603 +04:00 [INF] Classification completed for document "2843cb99-80dd-497f-af73-ca2803480940". Domain: "Labor", Confidence: 0.33, Keywords: 10, Entities: 0, Time: 2195ms
2025-06-02 17:28:35.637 +04:00 [INF] Starting chunking for document "2843cb99-80dd-497f-af73-ca2803480940" with strategy "Semantic"
2025-06-02 17:28:35.641 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 179
2025-06-02 17:28:35.647 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-02 17:28:35.655 +04:00 [INF] Chunking completed for document "2843cb99-80dd-497f-af73-ca2803480940". Chunks: 1, Average size: 179, Time: 16ms
2025-06-02 17:28:35.693 +04:00 [INF] Starting vectorization with model "HuggingFace" for 1 chunks
2025-06-02 17:28:35.700 +04:00 [INF] Generating embeddings for 1 texts using local model
2025-06-02 17:28:35.884 +04:00 [ERR] Error generating embeddings with local service
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Http.HttpClient'.
   at System.Net.Http.HttpClient.CheckRequestBeforeSend(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.CheckServiceHealthAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Services\LocalEmbeddingService.cs:line 164
   at LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.GenerateEmbeddingsAsync(IEnumerable`1 textList, EmbeddingModelType modelType, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\Services\LocalEmbeddingService.cs:line 61
2025-06-02 17:28:35.928 +04:00 [ERR] All embeddings failed for vectorization
2025-06-02 17:29:00.642 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 17:29:00.647 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 17:29:00.651 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID b9a5a0bd-4e39-4cc9-8d3b-a74db1215650
2025-06-02 17:29:00.660 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID 0d872229-7e84-461c-a0e7-04da8db1e87d
2025-06-02 17:29:00.665 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:29:00.667 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:29:00.668 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 3ms with status 204 (Correlation ID: b9a5a0bd-4e39-4cc9-8d3b-a74db1215650)
2025-06-02 17:29:00.669 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 2ms with status 204 (Correlation ID: 0d872229-7e84-461c-a0e7-04da8db1e87d)
2025-06-02 17:29:00.672 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 30.0479ms
2025-06-02 17:29:00.712 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 65.7436ms
2025-06-02 17:29:00.708 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 17:29:00.738 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID 01d67482-0f99-4def-94e4-e84a666483ce
2025-06-02 17:29:00.742 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:29:00.744 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:29:00.746 +04:00 [INF] Request GET /api/documents/undefined completed in 4ms with status 404 (Correlation ID: 01d67482-0f99-4def-94e4-e84a666483ce)
2025-06-02 17:29:00.750 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 41.6671ms
2025-06-02 17:29:00.753 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 17:29:00.759 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 17:29:00.766 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID 09845343-c806-4459-b2e3-37df700b0f48
2025-06-02 17:29:00.770 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:29:00.771 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:29:00.774 +04:00 [INF] Request GET /api/documents/undefined completed in 3ms with status 404 (Correlation ID: 09845343-c806-4459-b2e3-37df700b0f48)
2025-06-02 17:29:00.779 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 25.9011ms
2025-06-02 17:29:00.784 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 17:30:02.987 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - null null
2025-06-02 17:30:02.987 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - null null
2025-06-02 17:30:03.003 +04:00 [INF] Request OPTIONS /api/documents/2843cb99-80dd-497f-af73-ca2803480940 started with correlation ID 241d3973-2679-4b93-9ab6-f473ccf74aa0
2025-06-02 17:30:03.010 +04:00 [INF] Request OPTIONS /api/documents/2843cb99-80dd-497f-af73-ca2803480940 started with correlation ID 988cf2af-3bdc-4e12-83a4-5308197eb473
2025-06-02 17:30:03.013 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:30:03.016 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:30:03.018 +04:00 [INF] Request OPTIONS /api/documents/2843cb99-80dd-497f-af73-ca2803480940 completed in 5ms with status 204 (Correlation ID: 241d3973-2679-4b93-9ab6-f473ccf74aa0)
2025-06-02 17:30:03.020 +04:00 [INF] Request OPTIONS /api/documents/2843cb99-80dd-497f-af73-ca2803480940 completed in 4ms with status 204 (Correlation ID: 988cf2af-3bdc-4e12-83a4-5308197eb473)
2025-06-02 17:30:03.026 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - 204 null null 39.1423ms
2025-06-02 17:30:03.034 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - 204 null null 46.4185ms
2025-06-02 17:30:03.031 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - application/json null
2025-06-02 17:30:03.075 +04:00 [INF] Request GET /api/documents/2843cb99-80dd-497f-af73-ca2803480940 started with correlation ID 80b91d04-af1f-4ce3-a045-b1682c80abb5
2025-06-02 17:30:03.079 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:30:03.082 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:30:03.085 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 17:30:03.098 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 17:30:03.126 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 17:30:03.154 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 17:30:03.222 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 17:30:03.240 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 135.3756ms
2025-06-02 17:30:03.243 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - application/json null
2025-06-02 17:30:03.245 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 17:30:03.252 +04:00 [INF] Request GET /api/documents/2843cb99-80dd-497f-af73-ca2803480940 started with correlation ID adc3ce27-5c44-45dd-af95-c5aa2155ccd2
2025-06-02 17:30:03.255 +04:00 [INF] Request GET /api/documents/2843cb99-80dd-497f-af73-ca2803480940 completed in 176ms with status 200 (Correlation ID: 80b91d04-af1f-4ce3-a045-b1682c80abb5)
2025-06-02 17:30:03.260 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:30:03.266 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - 200 null application/json; charset=utf-8 235.1604ms
2025-06-02 17:30:03.270 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:30:03.289 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 17:30:03.295 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 17:30:03.308 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 17:30:03.319 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 17:30:03.323 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 22.8512ms
2025-06-02 17:30:03.327 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 17:30:03.330 +04:00 [INF] Request GET /api/documents/2843cb99-80dd-497f-af73-ca2803480940 completed in 70ms with status 200 (Correlation ID: adc3ce27-5c44-45dd-af95-c5aa2155ccd2)
2025-06-02 17:30:03.335 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - 200 null application/json; charset=utf-8 91.9861ms
2025-06-02 17:31:10.730 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - null null
2025-06-02 17:31:10.731 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - null null
2025-06-02 17:31:10.763 +04:00 [INF] Request OPTIONS /api/documents/2843cb99-80dd-497f-af73-ca2803480940 started with correlation ID 8739ebf7-a004-4ca6-8d51-931312b86a94
2025-06-02 17:31:10.775 +04:00 [INF] Request OPTIONS /api/documents/2843cb99-80dd-497f-af73-ca2803480940 started with correlation ID b8010bd7-26d4-4e91-b4c4-a5aaa0374633
2025-06-02 17:31:10.782 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:31:10.786 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:31:10.788 +04:00 [INF] Request OPTIONS /api/documents/2843cb99-80dd-497f-af73-ca2803480940 completed in 6ms with status 204 (Correlation ID: 8739ebf7-a004-4ca6-8d51-931312b86a94)
2025-06-02 17:31:10.791 +04:00 [INF] Request OPTIONS /api/documents/2843cb99-80dd-497f-af73-ca2803480940 completed in 5ms with status 204 (Correlation ID: b8010bd7-26d4-4e91-b4c4-a5aaa0374633)
2025-06-02 17:31:10.799 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - 204 null null 68.6839ms
2025-06-02 17:31:10.806 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - 204 null null 74.2657ms
2025-06-02 17:31:10.803 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - application/json null
2025-06-02 17:31:10.837 +04:00 [INF] Request GET /api/documents/2843cb99-80dd-497f-af73-ca2803480940 started with correlation ID 24caeceb-0b82-4c50-93cd-fdfda3abdc61
2025-06-02 17:31:10.840 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:31:10.842 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:31:10.844 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 17:31:10.847 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 17:31:10.856 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 17:31:10.870 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 17:31:10.873 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 21.5884ms
2025-06-02 17:31:10.876 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - application/json null
2025-06-02 17:31:10.876 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 17:31:10.880 +04:00 [INF] Request GET /api/documents/2843cb99-80dd-497f-af73-ca2803480940 started with correlation ID cbad1849-b798-40ed-aab1-07c6fc3e92ff
2025-06-02 17:31:10.881 +04:00 [INF] Request GET /api/documents/2843cb99-80dd-497f-af73-ca2803480940 completed in 41ms with status 200 (Correlation ID: 24caeceb-0b82-4c50-93cd-fdfda3abdc61)
2025-06-02 17:31:10.885 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:31:10.888 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - 200 null application/json; charset=utf-8 85.2714ms
2025-06-02 17:31:10.891 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:31:10.898 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 17:31:10.900 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 17:31:10.908 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 17:31:10.914 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 17:31:10.917 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 14.246ms
2025-06-02 17:31:10.923 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 17:31:10.926 +04:00 [INF] Request GET /api/documents/2843cb99-80dd-497f-af73-ca2803480940 completed in 40ms with status 200 (Correlation ID: cbad1849-b798-40ed-aab1-07c6fc3e92ff)
2025-06-02 17:31:10.930 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/2843cb99-80dd-497f-af73-ca2803480940 - 200 null application/json; charset=utf-8 54.516ms
2025-06-02 17:45:05.837 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 17:45:05.896 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 17:45:05.914 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 58425f96-d56f-4d0e-bb9e-ea654f8dfaae
2025-06-02 17:45:05.935 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 8341fe94-bdad-41b9-9c2e-dd600e4a0e05
2025-06-02 17:45:06.023 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:45:06.028 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:45:06.030 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: 58425f96-d56f-4d0e-bb9e-ea654f8dfaae)
2025-06-02 17:45:06.031 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: 8341fe94-bdad-41b9-9c2e-dd600e4a0e05)
2025-06-02 17:45:06.034 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 197.9083ms
2025-06-02 17:45:06.037 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 173.2663ms
2025-06-02 17:45:06.045 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 17:45:06.058 +04:00 [INF] Request GET /api/documents started with correlation ID efdd460d-0bd2-4551-8a0d-b288b19400e1
2025-06-02 17:45:06.062 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:45:06.064 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:45:06.067 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:45:06.069 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 17:45:06.109 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 17:45:06.121 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:45:06.128 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 54.1631ms
2025-06-02 17:45:06.130 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 17:45:06.132 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:45:06.142 +04:00 [INF] Request GET /api/documents started with correlation ID c837a115-5821-4a63-a838-a06836a156fb
2025-06-02 17:45:06.147 +04:00 [INF] Request GET /api/documents completed in 84ms with status 200 (Correlation ID: efdd460d-0bd2-4551-8a0d-b288b19400e1)
2025-06-02 17:45:06.154 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:45:06.161 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 116.0387ms
2025-06-02 17:45:06.162 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:45:06.174 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:45:06.179 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 17:45:06.196 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 17:45:06.205 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:45:06.209 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 22.4074ms
2025-06-02 17:45:06.210 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:45:06.212 +04:00 [INF] Request GET /api/documents completed in 58ms with status 200 (Correlation ID: c837a115-5821-4a63-a838-a06836a156fb)
2025-06-02 17:45:06.215 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 84.6524ms
2025-06-02 17:51:39.545 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 17:51:39.551 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 17:51:39.601 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID f40f27b8-775c-4824-842f-4bb3855650f1
2025-06-02 17:51:39.611 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 24d74ac0-f250-417d-a428-804c2d570dc0
2025-06-02 17:51:39.617 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:51:39.620 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:51:39.621 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: f40f27b8-775c-4824-842f-4bb3855650f1)
2025-06-02 17:51:39.622 +04:00 [INF] Request OPTIONS /api/documents completed in 1ms with status 204 (Correlation ID: 24d74ac0-f250-417d-a428-804c2d570dc0)
2025-06-02 17:51:39.626 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 81.1548ms
2025-06-02 17:51:39.658 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 17:51:39.678 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 126.7899ms
2025-06-02 17:51:39.692 +04:00 [INF] Request GET /api/documents started with correlation ID 32d13de2-329c-4b6f-8e61-3b757961f2ac
2025-06-02 17:51:39.698 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:51:39.699 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:51:39.700 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:51:39.702 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 17:51:39.729 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 17:51:39.732 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:51:39.734 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 30.4275ms
2025-06-02 17:51:39.735 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:51:39.735 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 17:51:39.736 +04:00 [INF] Request GET /api/documents completed in 38ms with status 200 (Correlation ID: 32d13de2-329c-4b6f-8e61-3b757961f2ac)
2025-06-02 17:51:39.739 +04:00 [INF] Request GET /api/documents started with correlation ID b7ce88c2-fb69-4c97-a07c-44fbe6c3c567
2025-06-02 17:51:39.744 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 85.996ms
2025-06-02 17:51:39.745 +04:00 [INF] CORS policy execution successful.
2025-06-02 17:51:39.752 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 17:51:39.753 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:51:39.755 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 17:51:39.766 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 17:51:39.769 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:51:39.771 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 10.8207ms
2025-06-02 17:51:39.775 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 17:51:39.777 +04:00 [INF] Request GET /api/documents completed in 31ms with status 200 (Correlation ID: b7ce88c2-fb69-4c97-a07c-44fbe6c3c567)
2025-06-02 17:51:39.779 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 43.4284ms
