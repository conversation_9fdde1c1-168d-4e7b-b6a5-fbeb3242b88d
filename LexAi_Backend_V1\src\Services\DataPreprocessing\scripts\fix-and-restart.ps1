# Script pour corriger les problèmes et redémarrer le service
Write-Host "🔧 Correction des problèmes et redémarrage du service" -ForegroundColor Green

# Naviguer vers le répertoire du service
$serviceDir = "src/Services/DataPreprocessing"
if (!(Test-Path $serviceDir)) {
    Write-Error "Répertoire du service non trouvé : $serviceDir"
    exit 1
}

Set-Location $serviceDir

try {
    Write-Host ""
    Write-Host "📋 Étape 1: Compilation du projet" -ForegroundColor Cyan
    
    $buildResult = dotnet build LexAI.DataPreprocessing.API 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Compilation réussie" -ForegroundColor Green
    } else {
        Write-Host "❌ Erreurs de compilation:" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
        
        Write-Host ""
        Write-Host "🔧 Tentative de correction automatique..." -ForegroundColor Yellow
        
        # Ajouter les using manquants si nécessaire
        $programFile = "LexAI.DataPreprocessing.API/Program.cs"
        $content = Get-Content $programFile -Raw
        
        if ($content -notmatch "using Microsoft.EntityFrameworkCore;") {
            Write-Host "  📝 Ajout des using manquants..." -ForegroundColor Yellow
            $newContent = $content -replace "using Microsoft.AspNetCore.RateLimiting;", @"
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.EntityFrameworkCore;
"@
            Set-Content $programFile -Value $newContent
        }
        
        # Re-compiler
        $buildResult2 = dotnet build LexAI.DataPreprocessing.API 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Compilation réussie après correction" -ForegroundColor Green
        } else {
            Write-Error "❌ Compilation toujours en échec : $buildResult2"
            exit 1
        }
    }

    Write-Host ""
    Write-Host "📋 Étape 2: Mise à jour de la base de données" -ForegroundColor Cyan
    
    # Appliquer les migrations
    $migrationResult = dotnet ef database update --project LexAI.DataPreprocessing.Infrastructure --startup-project LexAI.DataPreprocessing.API --context DataPreprocessingDbContext 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Base de données mise à jour" -ForegroundColor Green
    } else {
        Write-Warning "⚠️ Problème avec les migrations : $migrationResult"
    }

    Write-Host ""
    Write-Host "📋 Étape 3: Test de démarrage du service" -ForegroundColor Cyan
    
    # Démarrer le service en arrière-plan pour tester
    Write-Host "🚀 Démarrage du service..." -ForegroundColor Yellow
    
    $serviceProcess = Start-Process -FilePath "dotnet" -ArgumentList "run --project LexAI.DataPreprocessing.API --urls http://localhost:5001" -PassThru -WindowStyle Hidden
    
    # Attendre que le service démarre
    $maxWait = 30
    $waited = 0
    $serviceReady = $false
    
    while ($waited -lt $maxWait -and -not $serviceReady) {
        Start-Sleep -Seconds 2
        $waited += 2
        
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:5001/health" -UseBasicParsing -TimeoutSec 5 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                $serviceReady = $true
                Write-Host "✅ Service démarré avec succès !" -ForegroundColor Green
            }
        } catch {
            Write-Host "⏳ Attente du service... ($waited/$maxWait secondes)" -ForegroundColor Yellow
        }
    }
    
    # Arrêter le service de test
    if ($serviceProcess -and -not $serviceProcess.HasExited) {
        $serviceProcess.Kill()
        Start-Sleep -Seconds 2
    }
    
    if (-not $serviceReady) {
        Write-Error "❌ Le service n'a pas démarré correctement"
        exit 1
    }

    Write-Host ""
    Write-Host "📋 Étape 4: Vérification des endpoints" -ForegroundColor Cyan
    
    # Redémarrer le service pour les tests
    $serviceProcess = Start-Process -FilePath "dotnet" -ArgumentList "run --project LexAI.DataPreprocessing.API --urls http://localhost:5001" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 10
    
    # Test des endpoints principaux
    $endpoints = @(
        @{ Name = "Health Check"; Url = "http://localhost:5001/health" },
        @{ Name = "Swagger"; Url = "http://localhost:5001/swagger" },
        @{ Name = "Hangfire"; Url = "http://localhost:5001/hangfire" }
    )
    
    foreach ($endpoint in $endpoints) {
        try {
            $response = Invoke-WebRequest -Uri $endpoint.Url -UseBasicParsing -TimeoutSec 10 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200 -or $response.StatusCode -eq 401) {
                Write-Host "  ✅ $($endpoint.Name): $($response.StatusCode)" -ForegroundColor Green
            } else {
                Write-Host "  ⚠️ $($endpoint.Name): $($response.StatusCode)" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  ❌ $($endpoint.Name): Erreur" -ForegroundColor Red
        }
    }
    
    # Arrêter le service de test
    if ($serviceProcess -and -not $serviceProcess.HasExited) {
        $serviceProcess.Kill()
    }

    Write-Host ""
    Write-Host "🎉 Correction terminée avec succès !" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Prochaines étapes :" -ForegroundColor Cyan
    Write-Host "  1. Démarrez le service : dotnet run --project LexAI.DataPreprocessing.API" -ForegroundColor White
    Write-Host "  2. Testez avec le frontend ou utilisez le script de test :" -ForegroundColor White
    Write-Host "     .\scripts\test-api-endpoints.ps1 -Token 'votre_jwt_token'" -ForegroundColor Gray
    Write-Host "  3. Surveillez Hangfire : http://localhost:5001/hangfire" -ForegroundColor White
    Write-Host ""
    Write-Host "📖 Documentation complète : .\docs\frontend-testing-guide.md" -ForegroundColor Cyan

}
catch {
    Write-Error "❌ Erreur lors de la correction : $_"
    exit 1
}
finally {
    # Nettoyer les processus
    if ($serviceProcess -and -not $serviceProcess.HasExited) {
        $serviceProcess.Kill()
    }
    
    # Retourner au répertoire racine
    Set-Location ../../..
}
