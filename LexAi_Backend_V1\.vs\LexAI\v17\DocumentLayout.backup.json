{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{376EC7D1-C6D4-2176-3D6A-BBB995A7F2DC}|tests\\LexAI.DataPreprocessing.UnitTests\\LexAI.DataPreprocessing.UnitTests.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\tests\\lexai.datapreprocessing.unittests\\domain\\entities\\documenttests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{376EC7D1-C6D4-2176-3D6A-BBB995A7F2DC}|tests\\LexAI.DataPreprocessing.UnitTests\\LexAI.DataPreprocessing.UnitTests.csproj|solutionrelative:tests\\lexai.datapreprocessing.unittests\\domain\\entities\\documenttests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7FB6747D-3AA4-1F0A-E89E-EA1BC5786CF2}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\LexAI.DataPreprocessing.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.application\\commands\\documentcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7FB6747D-3AA4-1F0A-E89E-EA1BC5786CF2}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\LexAI.DataPreprocessing.Application.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.application\\commands\\documentcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\controllers\\documentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\controllers\\documentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "DocumentTests.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\tests\\LexAI.DataPreprocessing.UnitTests\\Domain\\Entities\\DocumentTests.cs", "RelativeDocumentMoniker": "tests\\LexAI.DataPreprocessing.UnitTests\\Domain\\Entities\\DocumentTests.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\tests\\LexAI.DataPreprocessing.UnitTests\\Domain\\Entities\\DocumentTests.cs", "RelativeToolTip": "tests\\LexAI.DataPreprocessing.UnitTests\\Domain\\Entities\\DocumentTests.cs", "ViewState": "AgIAAFAAAAAAAAAAAAAgwFkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T10:38:38.484Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DocumentCommands.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Commands\\DocumentCommands.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Commands\\DocumentCommands.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Commands\\DocumentCommands.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Commands\\DocumentCommands.cs", "ViewState": "AgIAAH0AAAAAAAAAAAAIwIYAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T09:52:19.937Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DocumentsController.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "ViewState": "AgIAAKsAAAAAAAAAAAAIwLkAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T08:46:49.939Z", "EditorCaption": ""}]}]}]}