{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.application\\interfaces\\iaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.application\\interfaces\\iaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\openaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\openaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\logs\\lexai-ai-assistant-20250603.log||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\logs\\lexai-ai-assistant-20250603.log||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\LexAI.LegalResearch.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.application\\interfaces\\irepositories.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\LexAI.LegalResearch.Application.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.application\\interfaces\\irepositories.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\LexAI.LegalResearch.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.application\\commands\\searchcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\LexAI.LegalResearch.Application.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.application\\commands\\searchcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\LexAI.LegalResearch.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.application\\interfaces\\ilegalsearchservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\LexAI.LegalResearch.Application.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.application\\interfaces\\ilegalsearchservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AAA4EAF2-8662-495C-AC3C-FDBA17D8053A}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\LexAI.LegalResearch.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.domain\\valueobjects\\additionalvalueobjects.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AAA4EAF2-8662-495C-AC3C-FDBA17D8053A}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\LexAI.LegalResearch.Domain.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.domain\\valueobjects\\additionalvalueobjects.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\logs\\lexai-ai-assistant-20250530.log||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\logs\\lexai-ai-assistant-20250530.log||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\controllers\\chatcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\controllers\\chatcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\extractionagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\extractionagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\services\\textextractionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\services\\textextractionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\classificationagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\classificationagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\controllers\\documentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\controllers\\documentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 12, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "OpenAIAssistantService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T17:56:02.131Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "IAIAssistantService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Interfaces\\IAIAssistantService.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Interfaces\\IAIAssistantService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Interfaces\\IAIAssistantService.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Interfaces\\IAIAssistantService.cs", "ViewState": "AgIAAFgAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T17:54:04.459Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "SearchCommands.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Commands\\SearchCommands.cs", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Commands\\SearchCommands.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Commands\\SearchCommands.cs", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Commands\\SearchCommands.cs", "ViewState": "AgIAAMwAAAAAAAAAAAAAwOUAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T17:36:29.888Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ILegalSearchService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Interfaces\\ILegalSearchService.cs", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Interfaces\\ILegalSearchService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Interfaces\\ILegalSearchService.cs", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Interfaces\\ILegalSearchService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T17:26:07.133Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "IRepositories.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Interfaces\\IRepositories.cs", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Interfaces\\IRepositories.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Interfaces\\IRepositories.cs", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Interfaces\\IRepositories.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAAABEAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T17:24:28.56Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "AdditionalValueObjects.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\ValueObjects\\AdditionalValueObjects.cs", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\ValueObjects\\AdditionalValueObjects.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\ValueObjects\\AdditionalValueObjects.cs", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\ValueObjects\\AdditionalValueObjects.cs", "ViewState": "AgIAAJIAAAAAAAAAAAAAAKIAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T17:15:43.52Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "lexai-ai-assistant-20250603.log", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250603.log", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250603.log", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250603.log", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250603.log", "ViewState": "AgIAAA8AAAAAAAAAAAAAACMAAAD3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-03T17:13:34.989Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "lexai-ai-assistant-20250530.log", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250530.log", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250530.log", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250530.log", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\logs\\lexai-ai-assistant-20250530.log", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-03T17:13:29.579Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ChatController.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Controllers\\ChatController.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Controllers\\ChatController.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Controllers\\ChatController.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Controllers\\ChatController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T17:12:18.957Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "ViewState": "AgIAAKMAAAAAAAAAAAAIwLIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T17:10:22.138Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "TextExtractionService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\TextExtractionService.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\TextExtractionService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\TextExtractionService.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\TextExtractionService.cs", "ViewState": "AgIAAGUAAAAAAAAAAADwv2sAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T16:48:11.17Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ExtractionAgent.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ExtractionAgent.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ExtractionAgent.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ExtractionAgent.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ExtractionAgent.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAkwFIAAABQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T16:46:34.743Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "ClassificationAgent.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ClassificationAgent.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ClassificationAgent.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ClassificationAgent.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ClassificationAgent.cs", "ViewState": "AgIAAMgAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T16:45:24.565Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "DocumentsController.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "ViewState": "AgIAAHIAAAAAAAAAAAAAAHgAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T19:36:14.714Z", "EditorCaption": ""}]}]}]}