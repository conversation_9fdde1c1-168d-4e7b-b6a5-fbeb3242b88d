{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\routingagent.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\routingagent.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\orchestrationagent.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\orchestrationagent.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\services\\vectorstorageservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\services\\vectorstorageservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\controllers\\documentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\controllers\\documentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\vectorizationagent.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\vectorizationagent.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "RoutingAgent.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\RoutingAgent.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\RoutingAgent.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\RoutingAgent.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\RoutingAgent.cs", "ViewState": "AgIAAIAAAAAAAAAAAAAIwIsAAABYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T08:08:14.186Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DocumentsController.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "ViewState": "AgIAAIAAAAAAAAAAAAAAAJEAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T07:59:39.858Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T07:59:35.674Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "OrchestrationAgent.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "ViewState": "AgIAAFwCAAAAAAAAAAAAAGECAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T07:39:50.403Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "VectorizationAgent.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\VectorizationAgent.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\VectorizationAgent.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\VectorizationAgent.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\VectorizationAgent.cs", "ViewState": "AgIAAM4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T07:39:50.493Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "VectorStorageService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\VectorStorageService.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\VectorStorageService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\VectorStorageService.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\VectorStorageService.cs", "ViewState": "AgIAADwAAAAAAAAAAAAuwGcAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T07:39:50.534Z", "EditorCaption": ""}]}]}]}