import React, { useState } from 'react'
import { Search, Clock, RotateCcw, Copy, ExternalLink } from 'lucide-react'
import { useThemeStore } from '../../store/themeStore'

interface Source {
  title: string
  url: string
  type: 'United Nations' | 'Universal Declaration' | 'Amnesty International' | 'Other'
}

interface SearchResult {
  id: string
  question: string
  answer: string
  sources: Source[]
  timestamp: Date
}

const LegalResearch: React.FC = () => {
  const { theme } = useThemeStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [currentResult, setCurrentResult] = useState<SearchResult | null>(null)
  const [searchHistory, setSearchHistory] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([])

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    setIsLoading(true)
    
    // Simulation d'une recherche
    setTimeout(() => {
      const mockResult: SearchResult = {
        id: Date.now().toString(),
        question: searchQuery,
        answer: `Include freedom of speech, religion, and assembly; equality before the law; protection from unjust detention; fair trials; privacy; asylum from persecution; nationality; marriage; property ownership; and access to education. These universal, inherent protections ensure dignity, justice, and freedom for all. *(Exactly 50 words)*`,
        sources: [
          {
            title: 'United Nations',
            url: 'https://www.un.org',
            type: 'United Nations'
          },
          {
            title: 'Universal Declaration of Human Rights',
            url: 'https://www.un.org/en/about-us/universal-declaration-of-human-rights',
            type: 'Universal Declaration'
          },
          {
            title: 'Amnesty International',
            url: 'https://www.amnesty.org',
            type: 'Amnesty International'
          }
        ],
        timestamp: new Date()
      }

      setCurrentResult(mockResult)
      setSearchHistory(prev => [mockResult, ...prev])
      setFollowUpQuestions([
        'How do these rights vary between different countries?',
        'What legal recourse do individuals have if their rights are violated?'
      ])
      setIsLoading(false)
    }, 2000)

    setSearchQuery('')
  }

  const handleFollowUpQuestion = (question: string) => {
    setSearchQuery(question)
    handleSearch()
  }

  const regenerateAnswer = () => {
    if (currentResult) {
      setIsLoading(true)
      // Simulation de régénération
      setTimeout(() => {
        setIsLoading(false)
      }, 1500)
    }
  }

  const copyAnswer = () => {
    if (currentResult) {
      navigator.clipboard.writeText(currentResult.answer)
    }
  }

  return (
    <div className={`flex h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Sidebar */}
      <div className={`w-80 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} border-r ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} flex flex-col`}>
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center space-x-2 mb-4">
            <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">ai</span>
            </div>
            <span className="font-semibold">lawyer</span>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            {/* Search History */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-400 mb-3">Recent Searches</h4>
              {searchHistory.length === 0 ? (
                <p className="text-sm text-gray-500">No searches yet</p>
              ) : (
                <div className="space-y-2">
                  {searchHistory.slice(0, 10).map(result => (
                    <div 
                      key={result.id}
                      onClick={() => setCurrentResult(result)}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        currentResult?.id === result.id 
                          ? 'bg-gray-700' 
                          : 'hover:bg-gray-700'
                      }`}
                    >
                      <p className="text-sm truncate">{result.question}</p>
                      <p className="text-xs text-gray-400 mt-1">
                        {result.timestamp.toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="p-4 border-t border-gray-700 text-center">
          <p className="text-xs text-gray-400 mb-2">
            Earn with us as an elite distributor! Click{' '}
            <span className="text-blue-400 cursor-pointer">here</span> for the survey 🔥
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {!currentResult ? (
          /* Search Interface */
          <div className="flex-1 flex items-center justify-center">
            <div className="max-w-2xl mx-auto text-center p-8">
              <h1 className="text-3xl font-bold mb-8">Legal Research</h1>
              <div className={`relative mb-6 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg`}>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  placeholder="Ask a legal question..."
                  className={`w-full p-4 pr-12 rounded-lg border ${
                    theme === 'dark' 
                      ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400' 
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  } focus:outline-none focus:ring-2 focus:ring-purple-500`}
                />
                <button 
                  onClick={handleSearch}
                  disabled={isLoading}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-purple-500 transition-colors"
                >
                  <Search className="w-5 h-5" />
                </button>
              </div>
              
              {isLoading && (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                  <span className="ml-2 text-gray-500">Searching...</span>
                </div>
              )}
            </div>
          </div>
        ) : (
          /* Results Interface */
          <>
            {/* Header */}
            <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} flex items-center justify-between`}>
              <h2 className="text-lg font-semibold truncate flex-1 mr-4">{currentResult.question}</h2>
              <div className="flex items-center space-x-2">
                <button 
                  onClick={() => setCurrentResult(null)}
                  className={`px-4 py-2 rounded-lg border ${
                    theme === 'dark' 
                      ? 'border-gray-600 hover:bg-gray-700' 
                      : 'border-gray-300 hover:bg-gray-50'
                  } transition-colors flex items-center space-x-2`}
                >
                  <Search className="w-4 h-4" />
                  <span>New request</span>
                </button>
                <Clock className="w-5 h-5 text-gray-400" />
              </div>
            </div>

            {/* Answer Section */}
            <div className="flex-1 overflow-y-auto p-6">
              <div className={`rounded-lg p-6 mb-6 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
                <div className="flex items-start justify-between mb-4">
                  <h3 className="text-lg font-semibold">Answer</h3>
                  <div className="flex items-center space-x-2">
                    <button 
                      onClick={regenerateAnswer}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      title="Regenerate"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </button>
                    <button 
                      onClick={copyAnswer}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      title="Copy"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-500 mr-2"></div>
                    <span className="text-gray-500">Generating answer...</span>
                  </div>
                ) : (
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {currentResult.answer}
                  </p>
                )}
              </div>

              {/* Sources */}
              <div className={`rounded-lg p-6 mb-6 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <span className="mr-2">📚</span>
                  Sources
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {currentResult.sources.map((source, index) => (
                    <div 
                      key={index}
                      className={`p-4 rounded-lg border ${
                        theme === 'dark' 
                          ? 'border-gray-600 bg-gray-700' 
                          : 'border-gray-200 bg-gray-50'
                      } hover:shadow-md transition-shadow`}
                    >
                      <h4 className="font-medium text-sm mb-2">{source.title}</h4>
                      <a 
                        href={source.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:text-blue-600 text-xs flex items-center"
                      >
                        <span className="truncate">{source.url}</span>
                        <ExternalLink className="w-3 h-3 ml-1 flex-shrink-0" />
                      </a>
                    </div>
                  ))}
                </div>
              </div>

              {/* Follow-up Questions */}
              {followUpQuestions.length > 0 && (
                <div className={`rounded-lg p-6 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
                  <h3 className="text-lg font-semibold mb-4">Follow-up Questions</h3>
                  <div className="space-y-3">
                    {followUpQuestions.map((question, index) => (
                      <button
                        key={index}
                        onClick={() => handleFollowUpQuestion(question)}
                        className={`w-full text-left p-3 rounded-lg border ${
                          theme === 'dark' 
                            ? 'border-gray-600 hover:bg-gray-700' 
                            : 'border-gray-200 hover:bg-gray-50'
                        } transition-colors flex items-center justify-between group`}
                      >
                        <span className="text-sm">{question}</span>
                        <Search className="w-4 h-4 text-gray-400 group-hover:text-purple-500 transition-colors" />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Search Input at Bottom */}
            <div className={`p-4 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
              <div className={`flex items-center space-x-2 p-3 rounded-lg ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  placeholder="Ask to follow-up..."
                  className="flex-1 bg-transparent outline-none placeholder-gray-400"
                />
                <button 
                  onClick={handleSearch}
                  disabled={isLoading}
                  className="p-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
                >
                  <Search className="w-4 h-4" />
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default LegalResearch
