# Guide de Test Frontend - Service Data Preprocessing

## 📋 Vue d'ensemble

Ce guide détaille comment tester le service Data Preprocessing depuis le frontend et via Hangfire.

## 🚀 Prérequis

### 1. Services en cours d'exécution
```bash
# Démarrer les services
cd src/Services/DataPreprocessing
.\scripts\start-dev.ps1 -WithAdmin

# Vérifier que les services sont prêts
curl http://localhost:5001/health
```

### 2. Authentification
- Le service nécessite un token JWT valide
- L'utilisateur doit être authentifié via le service d'authentification

## 🔧 Endpoints API Disponibles

### 1. Upload de Document
```http
POST /api/documents/upload
Content-Type: multipart/form-data
Authorization: Bearer {jwt_token}

Form Data:
- file: [fichier PDF/DOCX/DOC/TXT/HTML/RTF]
- metadata: {"key": "value"} (optionnel)
```

**Réponse:**
```json
{
  "documentId": "guid",
  "fileName": "document.pdf",
  "fileSize": 1024000,
  "status": "Uploaded",
  "processingStarted": false,
  "estimatedProcessingTime": "00:05:00",
  "message": "Document uploaded successfully"
}
```

### 2. Récupération d'un Document
```http
GET /api/documents/{documentId}
Authorization: Bearer {jwt_token}
```

**Réponse:**
```json
{
  "id": "guid",
  "fileName": "document.pdf",
  "fileSize": 1024000,
  "mimeType": "application/pdf",
  "status": "Completed",
  "detectedDomain": "Commercial",
  "classificationConfidence": 0.95,
  "chunkCount": 25,
  "totalTokens": 5000,
  "estimatedCost": 0.05,
  "isVectorized": true,
  "vectorDatabase": "MongoDB",
  "processingTime": "00:03:45",
  "createdAt": "2025-06-01T20:00:00Z",
  "updatedAt": "2025-06-01T20:03:45Z"
}
```

### 3. Liste des Documents Utilisateur
```http
GET /api/documents?limit=20&offset=0
Authorization: Bearer {jwt_token}
```

### 4. Démarrage du Traitement
```http
POST /api/documents/{documentId}/process
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "chunkingStrategy": "Semantic",
  "chunkSize": 1000,
  "overlapSize": 100,
  "embeddingModel": "OpenAISmall",
  "vectorDatabase": "MongoDB",
  "enableQualityAssurance": true,
  "customSettings": {}
}
```

### 5. Statut du Traitement
```http
GET /api/documents/{documentId}/status
Authorization: Bearer {jwt_token}
```

**Réponse:**
```json
{
  "documentId": "guid",
  "status": "Processing",
  "currentStep": "Vectorization",
  "progress": 75.5,
  "estimatedTimeRemaining": "00:01:30",
  "steps": [
    {
      "name": "Extraction",
      "status": "Completed",
      "startedAt": "2025-06-01T20:00:00Z",
      "completedAt": "2025-06-01T20:00:30Z",
      "duration": "00:00:30"
    }
  ],
  "errors": [],
  "metrics": {
    "totalTokens": 5000,
    "estimatedCost": 0.05,
    "qualityScore": 0.92
  }
}
```

## 🧪 Scénarios de Test

### Scénario 1: Upload et Traitement Complet

1. **Upload du document**
   ```javascript
   const formData = new FormData();
   formData.append('file', fileInput.files[0]);
   formData.append('metadata', JSON.stringify({
     source: 'frontend',
     category: 'legal'
   }));

   const response = await fetch('/api/documents/upload', {
     method: 'POST',
     headers: {
       'Authorization': `Bearer ${token}`
     },
     body: formData
   });

   const result = await response.json();
   const documentId = result.documentId;
   ```

2. **Démarrer le traitement**
   ```javascript
   const processingConfig = {
     chunkingStrategy: 'Semantic',
     chunkSize: 1000,
     overlapSize: 100,
     embeddingModel: 'OpenAISmall',
     vectorDatabase: 'MongoDB',
     enableQualityAssurance: true
   };

   const processResponse = await fetch(`/api/documents/${documentId}/process`, {
     method: 'POST',
     headers: {
       'Authorization': `Bearer ${token}`,
       'Content-Type': 'application/json'
     },
     body: JSON.stringify(processingConfig)
   });
   ```

3. **Surveiller le statut**
   ```javascript
   const pollStatus = async () => {
     const statusResponse = await fetch(`/api/documents/${documentId}/status`, {
       headers: { 'Authorization': `Bearer ${token}` }
     });
     
     const status = await statusResponse.json();
     
     if (status.status === 'Completed') {
       console.log('Traitement terminé!');
       return;
     }
     
     if (status.status === 'Failed') {
       console.error('Traitement échoué:', status.errors);
       return;
     }
     
     console.log(`Progression: ${status.progress}%`);
     setTimeout(pollStatus, 2000); // Poll toutes les 2 secondes
   };

   pollStatus();
   ```

### Scénario 2: Gestion d'Erreurs

1. **Document invalide**
   ```javascript
   // Tester avec un fichier non supporté
   const invalidFile = new File(['test'], 'test.xyz', { type: 'application/unknown' });
   // Devrait retourner 400 Bad Request
   ```

2. **Document trop volumineux**
   ```javascript
   // Tester avec un fichier > 50MB
   // Devrait retourner 413 Payload Too Large
   ```

3. **Document inexistant**
   ```javascript
   const fakeId = '00000000-0000-0000-0000-000000000000';
   const response = await fetch(`/api/documents/${fakeId}`, {
     headers: { 'Authorization': `Bearer ${token}` }
   });
   // Devrait retourner 404 Not Found
   ```

## 📊 Monitoring via Hangfire

### 1. Accès au Dashboard
- URL: `http://localhost:5001/hangfire`
- Credentials: `admin` / `admin123`

### 2. Types de Jobs à Surveiller

#### Jobs de Traitement
- **Nom**: `ProcessDocumentJob`
- **Paramètres**: `documentId`, `configuration`
- **Durée typique**: 2-10 minutes selon la taille

#### Jobs de Nettoyage
- **Nom**: `CleanupExpiredDocumentsJob`
- **Fréquence**: Quotidienne
- **Fonction**: Supprime les documents expirés

#### Jobs de Maintenance
- **Nom**: `DatabaseMaintenanceJob`
- **Fréquence**: Hebdomadaire
- **Fonction**: Optimise les performances

### 3. États des Jobs

| État | Description | Action |
|------|-------------|---------|
| **Enqueued** | En attente | Normal |
| **Processing** | En cours | Surveiller la progression |
| **Succeeded** | Réussi | Vérifier les résultats |
| **Failed** | Échoué | Analyser les erreurs |
| **Scheduled** | Programmé | Attendre l'exécution |

### 4. Surveillance des Erreurs

```csharp
// Dans Hangfire Dashboard, vérifier:
// 1. Failed Jobs - pour les erreurs de traitement
// 2. Recurring Jobs - pour les tâches périodiques
// 3. Servers - pour l'état des workers
```

## 🔍 Débogage

### Logs à Surveiller

1. **Logs Application**
   ```bash
   # Logs en temps réel
   docker-compose logs -f datapreprocessing-api
   
   # Logs spécifiques
   grep "ERROR\|WARN" logs/lexai-datapreprocessing-*.log
   ```

2. **Logs Base de Données**
   ```bash
   # PostgreSQL
   docker-compose logs postgres
   
   # MongoDB
   docker-compose logs mongodb
   ```

### Problèmes Courants

1. **"undefined" dans les URLs**
   - **Cause**: Frontend envoie des IDs non définis
   - **Solution**: Vérifier que `documentId` est bien défini avant l'appel API

2. **Erreurs de sérialisation JSON**
   - **Cause**: Problèmes avec `DocumentMetadata`
   - **Solution**: Vérifiée avec les corrections apportées

3. **Timeouts de traitement**
   - **Cause**: Documents trop volumineux ou API OpenAI lente
   - **Solution**: Augmenter les timeouts ou diviser le document

## ✅ Checklist de Test

### Tests Fonctionnels
- [ ] Upload de PDF réussi
- [ ] Upload de DOCX réussi
- [ ] Traitement complet d'un document
- [ ] Récupération de la liste des documents
- [ ] Récupération d'un document spécifique
- [ ] Surveillance du statut en temps réel

### Tests d'Erreur
- [ ] Fichier non supporté rejeté
- [ ] Fichier trop volumineux rejeté
- [ ] Document inexistant retourne 404
- [ ] Requête sans authentification rejetée

### Tests de Performance
- [ ] Upload de gros fichier (< 50MB)
- [ ] Traitement de document complexe
- [ ] Requêtes simultanées

### Tests Hangfire
- [ ] Dashboard accessible
- [ ] Jobs de traitement visibles
- [ ] Erreurs de jobs tracées
- [ ] Métriques de performance disponibles

## 🎯 Métriques de Succès

- **Temps d'upload**: < 30 secondes pour 10MB
- **Temps de traitement**: < 5 minutes pour 100 pages
- **Taux de succès**: > 95%
- **Disponibilité**: > 99%
