using LexAI.LegalResearch.Application.DTOs;
using LexAI.LegalResearch.Domain.ValueObjects;

namespace LexAI.LegalResearch.Application.Interfaces;

/// <summary>
/// Interface for legal search service
/// </summary>
public interface ILegalSearchService
{
    /// <summary>
    /// Performs a semantic search for legal documents
    /// </summary>
    /// <param name="request">Search request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search response with results</returns>
    Task<SearchResponseDto> SearchAsync(SearchRequestDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs a hybrid search combining keyword and semantic search
    /// </summary>
    /// <param name="request">Search request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search response with results</returns>
    Task<SearchResponseDto> HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Finds similar documents to a given document
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="limit">Maximum number of similar documents</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Similar documents</returns>
    Task<IEnumerable<SearchResultDto>> FindSimilarDocumentsAsync(Guid documentId, int limit = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets search suggestions based on partial query
    /// </summary>
    /// <param name="partialQuery">Partial query text</param>
    /// <param name="limit">Maximum number of suggestions</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search suggestions</returns>
    Task<IEnumerable<string>> GetSearchSuggestionsAsync(string partialQuery, int limit = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyzes query intent and extracts entities
    /// </summary>
    /// <param name="query">Query text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Query analysis result</returns>
    Task<QueryAnalysisDto> AnalyzeQueryAsync(string query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets search analytics for a user or session
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="sessionId">Session ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search analytics</returns>
    Task<SearchAnalyticsDto> GetSearchAnalyticsAsync(Guid? userId = null, string? sessionId = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for document embedding service
/// </summary>
public interface IEmbeddingService
{
    /// <summary>
    /// Generates embedding vector for text
    /// </summary>
    /// <param name="text">Text to embed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Embedding vector</returns>
    Task<float[]> GenerateEmbeddingAsync(string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates embeddings for multiple texts
    /// </summary>
    /// <param name="texts">Texts to embed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Embedding vectors</returns>
    Task<float[][]> GenerateEmbeddingsAsync(IEnumerable<string> texts, CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculates cosine similarity between two embedding vectors
    /// </summary>
    /// <param name="vector1">First vector</param>
    /// <param name="vector2">Second vector</param>
    /// <returns>Cosine similarity score</returns>
    double CalculateSimilarity(float[] vector1, float[] vector2);

    /// <summary>
    /// Gets the embedding model dimension
    /// </summary>
    /// <returns>Embedding dimension</returns>
    int GetEmbeddingDimension();
}

/// <summary>
/// Interface for document chunking service
/// </summary>
public interface IDocumentChunkingService
{
    /// <summary>
    /// Splits document content into chunks for vector search
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="content">Document content</param>
    /// <param name="chunkSize">Maximum chunk size in characters</param>
    /// <param name="overlap">Overlap between chunks in characters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document chunks</returns>
    Task<IEnumerable<DocumentChunkDto>> ChunkDocumentAsync(
        Guid documentId, 
        string content, 
        int chunkSize = 1000, 
        int overlap = 200, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Extracts keywords from text
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="maxKeywords">Maximum number of keywords</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extracted keywords</returns>
    Task<IEnumerable<string>> ExtractKeywordsAsync(string text, int maxKeywords = 20, CancellationToken cancellationToken = default);

    /// <summary>
    /// Identifies the type of text chunk
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="context">Surrounding context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Chunk type</returns>
    Task<ChunkType> IdentifyChunkTypeAsync(string text, string? context = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for vector database operations
/// </summary>
public interface IVectorDatabaseService
{
    /// <summary>
    /// Stores document chunks with embeddings in vector database
    /// </summary>
    /// <param name="chunks">Document chunks with embeddings</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task StoreChunksAsync(IEnumerable<DocumentChunkDto> chunks, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs vector similarity search
    /// </summary>
    /// <param name="queryVector">Query embedding vector</param>
    /// <param name="limit">Maximum number of results</param>
    /// <param name="threshold">Minimum similarity threshold</param>
    /// <param name="filters">Optional filters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Similar chunks with scores</returns>
    Task<IEnumerable<VectorSearchResultDto>> SearchSimilarAsync(
        float[] queryVector, 
        int limit = 10, 
        double threshold = 0.7,
        Dictionary<string, object>? filters = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates embeddings for a document
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="chunks">Updated chunks</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task UpdateDocumentEmbeddingsAsync(Guid documentId, IEnumerable<DocumentChunkDto> chunks, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes document embeddings from vector database
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task DeleteDocumentEmbeddingsAsync(Guid documentId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vector database statistics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Database statistics</returns>
    Task<VectorDatabaseStatsDto> GetStatsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for query processing service
/// </summary>
public interface IQueryProcessingService
{
    /// <summary>
    /// Processes and normalizes search query
    /// </summary>
    /// <param name="query">Raw query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processed query</returns>
    Task<string> ProcessQueryAsync(string query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Expands query with synonyms and related terms
    /// </summary>
    /// <param name="query">Original query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Expanded query terms</returns>
    Task<IEnumerable<string>> ExpandQueryAsync(string query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Detects query intent
    /// </summary>
    /// <param name="query">Query text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Query intent</returns>
    Task<QueryIntent> DetectIntentAsync(string query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Extracts legal entities from query
    /// </summary>
    /// <param name="query">Query text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extracted entities</returns>
    Task<IEnumerable<LegalEntityDto>> ExtractEntitiesAsync(string query, CancellationToken cancellationToken = default);
}
