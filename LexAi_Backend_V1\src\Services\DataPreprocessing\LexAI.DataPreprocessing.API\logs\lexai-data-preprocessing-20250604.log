2025-06-04 11:46:14.159 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-04 11:46:14.269 +04:00 [INF] Hangfire SQL objects installed.
2025-06-04 11:46:14.278 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-04 11:46:14.576 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-04 11:46:14.595 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 11:46:14.731 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-04 11:46:14.733 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-04 11:46:14.773 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-04 11:46:14.782 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-04 11:46:14.784 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-04 11:46:14.786 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-04 11:46:14.787 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-04 11:46:14.788 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-04 11:46:14.810 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 11:46:14.812 +04:00 [INF] Hosting environment: Development
2025-06-04 11:46:14.813 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-04 11:46:14.893 +04:00 [INF] Server datapreprocessing-kevin11:47980:f078efbb successfully announced in 35.9311 ms
2025-06-04 11:46:14.902 +04:00 [INF] Server datapreprocessing-kevin11:47980:f078efbb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-04 11:46:15.076 +04:00 [INF] Server datapreprocessing-kevin11:47980:f078efbb all the dispatchers started
2025-06-04 11:46:16.157 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-04 11:46:16.348 +04:00 [INF] Request GET / started with correlation ID e4572516-3494-4ec4-886e-a28787a0de2f
2025-06-04 11:46:16.437 +04:00 [INF] Request GET / completed in 85ms with status 404 (Correlation ID: e4572516-3494-4ec4-886e-a28787a0de2f)
2025-06-04 11:46:16.448 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 297.3638ms
2025-06-04 11:46:16.460 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-04 11:47:13.469 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 11:47:13.469 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 11:47:13.483 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID cd2c328a-8f26-4771-a958-f295d714893d
2025-06-04 11:47:13.483 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID fff4e38b-6290-427f-bc41-b0bfc895daa9
2025-06-04 11:47:13.507 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:47:13.507 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:47:13.513 +04:00 [INF] Request OPTIONS /api/documents completed in 13ms with status 204 (Correlation ID: cd2c328a-8f26-4771-a958-f295d714893d)
2025-06-04 11:47:13.513 +04:00 [INF] Request OPTIONS /api/documents completed in 9ms with status 204 (Correlation ID: fff4e38b-6290-427f-bc41-b0bfc895daa9)
2025-06-04 11:47:13.524 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 55.2901ms
2025-06-04 11:47:13.531 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 11:47:13.531 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 61.8656ms
2025-06-04 11:47:13.559 +04:00 [INF] Request GET /api/documents started with correlation ID 39b91f56-9f5f-4916-93a4-70e27193222f
2025-06-04 11:47:13.562 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:47:13.720 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:16:51 PM', Current time (UTC): '6/4/2025 7:47:13 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-04 11:47:13.730 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:16:51 PM', Current time (UTC): '6/4/2025 7:47:13 AM'.
2025-06-04 11:47:13.739 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:16:51 PM', Current time (UTC): '6/4/2025 7:47:13 AM'.
2025-06-04 11:47:13.756 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-04 11:47:13.769 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-04 11:47:13.772 +04:00 [INF] Request GET /api/documents completed in 210ms with status 401 (Correlation ID: 39b91f56-9f5f-4916-93a4-70e27193222f)
2025-06-04 11:47:13.778 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 246.5534ms
2025-06-04 11:47:13.781 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 11:47:13.789 +04:00 [INF] Request GET /api/documents started with correlation ID 987fec05-25ef-4ae0-9173-abcfde06b48a
2025-06-04 11:47:13.792 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:47:13.797 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:16:51 PM', Current time (UTC): '6/4/2025 7:47:13 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-04 11:47:13.804 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:16:51 PM', Current time (UTC): '6/4/2025 7:47:13 AM'.
2025-06-04 11:47:13.805 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 9:16:51 PM', Current time (UTC): '6/4/2025 7:47:13 AM'.
2025-06-04 11:47:13.807 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-04 11:47:13.809 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-04 11:47:13.811 +04:00 [INF] Request GET /api/documents completed in 19ms with status 401 (Correlation ID: 987fec05-25ef-4ae0-9173-abcfde06b48a)
2025-06-04 11:47:13.816 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 35.1002ms
2025-06-04 11:47:16.552 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 11:47:16.564 +04:00 [INF] Request GET /api/documents started with correlation ID e2e67254-b9e8-42b6-bf8a-52795f035c3f
2025-06-04 11:47:16.568 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:47:16.589 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 11:47:16.599 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 11:47:16.655 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 11:47:18.291 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-04 11:47:18.296 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-04 11:47:18.302 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-04 11:47:18.313 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-04 11:47:18.317 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-04 11:47:18.321 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-04 11:47:20.540 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-04 11:47:20.872 +04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 11:47:20.911 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 11:47:20.994 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 11:47:20.997 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 4331.9218ms
2025-06-04 11:47:21.001 +04:00 [INF] Request GET /api/documents started with correlation ID 651ea69f-52f8-4e07-918e-311761f620b7
2025-06-04 11:47:21.003 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 11:47:21.004 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:47:21.007 +04:00 [INF] Request GET /api/documents completed in 4438ms with status 200 (Correlation ID: e2e67254-b9e8-42b6-bf8a-52795f035c3f)
2025-06-04 11:47:21.007 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 11:47:21.012 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 11:47:21.017 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 11:47:21.038 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 4486.5852ms
2025-06-04 11:47:21.050 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 11:47:21.055 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 11:47:21.058 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 36.1574ms
2025-06-04 11:47:21.060 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 11:47:21.062 +04:00 [INF] Request GET /api/documents completed in 57ms with status 200 (Correlation ID: 651ea69f-52f8-4e07-918e-311761f620b7)
2025-06-04 11:47:21.066 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 71.1169ms
2025-06-04 11:48:54.005 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-04 11:48:54.018 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 66bfb7bf-65b3-4f9c-b22c-f43d67e05b3a
2025-06-04 11:48:54.020 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:48:54.022 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 1ms with status 204 (Correlation ID: 66bfb7bf-65b3-4f9c-b22c-f43d67e05b3a)
2025-06-04 11:48:54.025 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 20.482ms
2025-06-04 11:48:54.037 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryc2q8785OhHVBXKPr 474167
2025-06-04 11:48:54.047 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 702fbad3-dd20-4fde-862e-bc8c5862e21a
2025-06-04 11:48:54.054 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:48:54.058 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 11:48:54.065 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 11:48:54.121 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 11:48:54.422 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 11:48:54.490 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 11:48:54.507 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 11:48:54.541 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-04 11:48:54.575 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-04 11:48:54.598 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> e1f9337b-843d-47d7-8089-76fb7ba23a8f.pdf
2025-06-04 11:48:55.495 +04:00 [INF] Executed DbCommand (16ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-04 11:48:55.557 +04:00 [INF] Document added successfully: "3934aba1-3c3e-44ec-9b99-283069707e2d"
2025-06-04 11:48:55.562 +04:00 [INF] Document uploaded successfully: "3934aba1-3c3e-44ec-9b99-283069707e2d" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 11:48:55.566 +04:00 [INF] Background processing started for document "3934aba1-3c3e-44ec-9b99-283069707e2d"
2025-06-04 11:48:55.570 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "3934aba1-3c3e-44ec-9b99-283069707e2d", Processing started: true
2025-06-04 11:48:55.572 +04:00 [INF] Starting orchestrated processing for document "3934aba1-3c3e-44ec-9b99-283069707e2d": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 11:48:55.577 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-04 11:48:55.594 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 1464.1559ms
2025-06-04 11:48:55.601 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 11:48:55.606 +04:00 [INF] Request POST /api/documents/upload completed in 1552ms with status 200 (Correlation ID: 702fbad3-dd20-4fde-862e-bc8c5862e21a)
2025-06-04 11:48:55.621 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 1584.1545ms
2025-06-04 11:48:56.128 +04:00 [INF] Starting text extraction for document "3934aba1-3c3e-44ec-9b99-283069707e2d": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 11:48:56.150 +04:00 [INF] Extracting text from INTRODUCTION AU DROIT DU TRAVAIL.pdf (MIME: application/pdf)
2025-06-04 11:48:56.200 +04:00 [INF] Text extraction successful for INTRODUCTION AU DROIT DU TRAVAIL.pdf. Length: 179
2025-06-04 11:48:56.219 +04:00 [INF] Text extraction completed for document "3934aba1-3c3e-44ec-9b99-283069707e2d". Length: 179, Confidence: 0.70, Time: 86ms
2025-06-04 11:48:56.449 +04:00 [INF] Starting classification for document "3934aba1-3c3e-44ec-9b99-283069707e2d": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 11:48:56.455 +04:00 [INF] Classifying text into legal domains. Text length: 179
2025-06-04 11:48:56.489 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 11:48:56.497 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 11:48:59.662 +04:00 [INF] Received HTTP response headers after 3159.5034ms - 429
2025-06-04 11:48:59.666 +04:00 [INF] End processing HTTP request after 3178.4841ms - 429
2025-06-04 11:48:59.672 +04:00 [WRN] AI classification failed, using rule-based result: "Labor" (0.33)
2025-06-04 11:48:59.698 +04:00 [INF] Classification completed for document "3934aba1-3c3e-44ec-9b99-283069707e2d". Domain: "Labor", Confidence: 0.33, Keywords: 10, Entities: 0, Time: 3244ms
2025-06-04 11:48:59.765 +04:00 [INF] Starting chunking for document "3934aba1-3c3e-44ec-9b99-283069707e2d" with strategy "Semantic"
2025-06-04 11:48:59.774 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 179
2025-06-04 11:48:59.783 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-04 11:48:59.808 +04:00 [INF] Chunking completed for document "3934aba1-3c3e-44ec-9b99-283069707e2d". Chunks: 1, Average size: 179, Time: 39ms
2025-06-04 11:48:59.917 +04:00 [INF] Starting vectorization with model "HuggingFace" for 1 chunks
2025-06-04 11:48:59.936 +04:00 [INF] Generating embeddings for 1 texts using local model
2025-06-04 11:48:59.941 +04:00 [INF] Start processing HTTP request GET http://localhost:8000/health
2025-06-04 11:48:59.943 +04:00 [INF] Sending HTTP request GET http://localhost:8000/health
2025-06-04 11:49:03.544 +04:00 [INF] Received HTTP response headers after 3596.857ms - 200
2025-06-04 11:49:03.547 +04:00 [INF] End processing HTTP request after 3606.0944ms - 200
2025-06-04 11:49:03.553 +04:00 [INF] Start processing HTTP request POST http://localhost:8000/embeddings
2025-06-04 11:49:03.556 +04:00 [INF] Sending HTTP request POST http://localhost:8000/embeddings
2025-06-04 11:49:05.074 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d - null null
2025-06-04 11:49:05.077 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d - null null
2025-06-04 11:49:05.088 +04:00 [INF] Request OPTIONS /api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d started with correlation ID 203b5ea3-f2b3-470e-a850-7e71c0e20e94
2025-06-04 11:49:05.096 +04:00 [INF] Request OPTIONS /api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d started with correlation ID 44b4b9bb-f27a-4c09-9f4e-338ca56a77b3
2025-06-04 11:49:05.100 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:49:05.102 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:49:05.105 +04:00 [INF] Request OPTIONS /api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d completed in 4ms with status 204 (Correlation ID: 203b5ea3-f2b3-470e-a850-7e71c0e20e94)
2025-06-04 11:49:05.107 +04:00 [INF] Request OPTIONS /api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d completed in 5ms with status 204 (Correlation ID: 44b4b9bb-f27a-4c09-9f4e-338ca56a77b3)
2025-06-04 11:49:05.117 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d - 204 null null 41.8215ms
2025-06-04 11:49:05.119 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d - application/json null
2025-06-04 11:49:05.123 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d - 204 null null 45.2297ms
2025-06-04 11:49:05.146 +04:00 [INF] Request GET /api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d started with correlation ID e906579b-b03d-4aa5-b1ea-b85d94808ef5
2025-06-04 11:49:05.157 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:49:05.163 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 11:49:05.170 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-04 11:49:05.182 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 11:49:05.209 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-04 11:49:05.246 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-04 11:49:05.290 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-04 11:49:05.325 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 139.1013ms
2025-06-04 11:49:05.329 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d - application/json null
2025-06-04 11:49:05.336 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-04 11:49:05.348 +04:00 [INF] Request GET /api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d started with correlation ID 6c7ae408-c0fb-4d24-ac54-331f700ad547
2025-06-04 11:49:05.352 +04:00 [INF] Request GET /api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d completed in 194ms with status 200 (Correlation ID: e906579b-b03d-4aa5-b1ea-b85d94808ef5)
2025-06-04 11:49:05.356 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:49:05.366 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d - 200 null application/json; charset=utf-8 247.1084ms
2025-06-04 11:49:05.370 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 11:49:05.385 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-04 11:49:05.387 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 11:49:05.397 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-04 11:49:05.403 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-04 11:49:05.409 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 18.8126ms
2025-06-04 11:49:05.418 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-04 11:49:05.420 +04:00 [INF] Request GET /api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d completed in 63ms with status 200 (Correlation ID: 6c7ae408-c0fb-4d24-ac54-331f700ad547)
2025-06-04 11:49:05.424 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/3934aba1-3c3e-44ec-9b99-283069707e2d - 200 null application/json; charset=utf-8 95.5427ms
2025-06-04 11:49:08.931 +04:00 [INF] Received HTTP response headers after 5373.0576ms - 200
2025-06-04 11:49:08.936 +04:00 [INF] End processing HTTP request after 5383.0224ms - 200
2025-06-04 11:49:08.984 +04:00 [INF] Generated 1/1 embeddings successfully
2025-06-04 11:49:08.995 +04:00 [INF] Vectorization completed. Successful: 1/1, Tokens: 44, Cost: $0.0000, Time: 9069ms
2025-06-04 11:49:09.031 +04:00 [INF] Starting routing for 1 chunks
2025-06-04 11:49:09.043 +04:00 [INF] Routing completed for 1 chunks in 8ms
2025-06-04 11:49:09.717 +04:00 [INF] Storing 1 vectors in "MongoDB"/legal_labor_chunks
2025-06-04 11:49:10.221 +04:00 [INF] Vector storage completed. Stored: 1/1, Time: 492ms
2025-06-04 11:49:10.235 +04:00 [INF] Starting quality assessment for document "3934aba1-3c3e-44ec-9b99-283069707e2d"
2025-06-04 11:49:10.260 +04:00 [INF] Quality assessment completed for document "3934aba1-3c3e-44ec-9b99-283069707e2d". Score: 0.67, Passed: false, Issues: 2, Time: 21ms
2025-06-04 11:49:10.321 +04:00 [INF] Document processing completed successfully for "3934aba1-3c3e-44ec-9b99-283069707e2d". Total time: 14687ms, Chunks: 1, Cost: $0.0000
2025-06-04 11:49:20.913 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 11:49:20.915 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 11:49:20.924 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 4bfc025c-6c28-4c85-ae2f-6c0e0396ce99
2025-06-04 11:49:20.933 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID f2e35ae9-d1fa-4ac2-8a40-70cf336ca823
2025-06-04 11:49:20.937 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:49:20.940 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:49:20.942 +04:00 [INF] Request OPTIONS /api/documents completed in 5ms with status 204 (Correlation ID: 4bfc025c-6c28-4c85-ae2f-6c0e0396ce99)
2025-06-04 11:49:20.945 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: f2e35ae9-d1fa-4ac2-8a40-70cf336ca823)
2025-06-04 11:49:20.952 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 38.7392ms
2025-06-04 11:49:20.955 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 11:49:20.958 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 43.7006ms
2025-06-04 11:49:20.969 +04:00 [INF] Request GET /api/documents started with correlation ID 5276d8e8-0f58-4123-b16f-ddbdc6a6d0f5
2025-06-04 11:49:20.975 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:49:20.976 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 11:49:20.978 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 11:49:20.979 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 11:49:20.986 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 11:49:20.997 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 11:49:20.999 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 17.4479ms
2025-06-04 11:49:21.001 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 11:49:21.003 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 11:49:21.008 +04:00 [INF] Request GET /api/documents started with correlation ID 59b438dc-fd91-4909-a482-0d1614427150
2025-06-04 11:49:21.009 +04:00 [INF] Request GET /api/documents completed in 33ms with status 200 (Correlation ID: 5276d8e8-0f58-4123-b16f-ddbdc6a6d0f5)
2025-06-04 11:49:21.010 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:49:21.014 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 58.964ms
2025-06-04 11:49:21.015 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 11:49:21.023 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 11:49:21.024 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 11:49:21.033 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 11:49:21.046 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 11:49:21.053 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 26.8204ms
2025-06-04 11:49:21.061 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 11:49:21.063 +04:00 [INF] Request GET /api/documents completed in 53ms with status 200 (Correlation ID: 59b438dc-fd91-4909-a482-0d1614427150)
2025-06-04 11:49:21.067 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 65.5778ms
2025-06-04 11:51:15.321 +04:00 [INF] 1 servers were removed due to timeout
2025-06-04 11:58:46.436 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-04 11:58:46.451 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID ab2440b9-1f3e-4c36-9e82-4c2439d0abae
2025-06-04 11:58:46.455 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:58:46.457 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 1ms with status 204 (Correlation ID: ab2440b9-1f3e-4c36-9e82-4c2439d0abae)
2025-06-04 11:58:46.460 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 61.9958ms
2025-06-04 11:58:46.463 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryjipBSiUMpRrcwDqA 869509
2025-06-04 11:58:46.524 +04:00 [INF] Request POST /api/documents/upload started with correlation ID efcb0cfe-6591-4121-bffe-f7846066fc74
2025-06-04 11:58:46.529 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:58:46.532 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 11:58:46.534 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 11:58:46.537 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 11:58:46.565 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": Instructions.pdf
2025-06-04 11:58:46.575 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 11:58:46.577 +04:00 [INF] Processing document upload: Instructions.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 11:58:46.620 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-04 11:58:46.628 +04:00 [INF] File stored successfully: Instructions.pdf -> 8edcdc1d-72b4-4dee-a27b-0d71d9ed65fb.pdf
2025-06-04 11:58:46.635 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-04 11:58:46.644 +04:00 [INF] Document added successfully: "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7"
2025-06-04 11:58:46.646 +04:00 [INF] Document uploaded successfully: "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7" - Instructions.pdf
2025-06-04 11:58:46.647 +04:00 [INF] Background processing started for document "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7"
2025-06-04 11:58:46.648 +04:00 [INF] Starting orchestrated processing for document "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7": Instructions.pdf
2025-06-04 11:58:46.649 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7", Processing started: true
2025-06-04 11:58:46.654 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-04 11:58:46.659 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 116.1459ms
2025-06-04 11:58:46.663 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 11:58:46.665 +04:00 [INF] Starting text extraction for document "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7": Instructions.pdf
2025-06-04 11:58:46.667 +04:00 [INF] Request POST /api/documents/upload completed in 137ms with status 200 (Correlation ID: efcb0cfe-6591-4121-bffe-f7846066fc74)
2025-06-04 11:58:46.672 +04:00 [INF] Extracting text from Instructions.pdf (MIME: application/pdf)
2025-06-04 11:58:46.679 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 215.3746ms
2025-06-04 11:58:46.682 +04:00 [INF] Text extraction successful for Instructions.pdf. Length: 159
2025-06-04 11:58:46.695 +04:00 [INF] Text extraction completed for document "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7". Length: 159, Confidence: 0.70, Time: 26ms
2025-06-04 11:58:46.723 +04:00 [INF] Starting classification for document "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7": Instructions.pdf
2025-06-04 11:58:46.731 +04:00 [INF] Classifying text into legal domains. Text length: 159
2025-06-04 11:58:46.741 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 11:58:46.747 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 11:58:48.947 +04:00 [INF] Received HTTP response headers after 2192.2597ms - 429
2025-06-04 11:58:48.950 +04:00 [INF] End processing HTTP request after 2208.4853ms - 429
2025-06-04 11:58:48.953 +04:00 [WRN] AI classification failed, using rule-based result: "Tax" (0.25)
2025-06-04 11:58:48.962 +04:00 [INF] Classification completed for document "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7". Domain: "Tax", Confidence: 0.25, Keywords: 10, Entities: 0, Time: 2231ms
2025-06-04 11:58:48.993 +04:00 [INF] Starting chunking for document "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7" with strategy "Semantic"
2025-06-04 11:58:48.997 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 159
2025-06-04 11:58:49.001 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-04 11:58:49.008 +04:00 [INF] Chunking completed for document "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7". Chunks: 1, Average size: 159, Time: 11ms
2025-06-04 11:58:49.049 +04:00 [INF] Starting vectorization with model "HuggingFace" for 1 chunks
2025-06-04 11:58:49.051 +04:00 [INF] Generating embeddings for 1 texts using local model
2025-06-04 11:58:49.053 +04:00 [INF] Start processing HTTP request GET http://localhost:8000/health
2025-06-04 11:58:49.055 +04:00 [INF] Sending HTTP request GET http://localhost:8000/health
2025-06-04 11:58:51.115 +04:00 [INF] Received HTTP response headers after 2056.5673ms - 200
2025-06-04 11:58:51.122 +04:00 [INF] End processing HTTP request after 2069.0496ms - 200
2025-06-04 11:58:51.130 +04:00 [INF] Start processing HTTP request POST http://localhost:8000/embeddings
2025-06-04 11:58:51.138 +04:00 [INF] Sending HTTP request POST http://localhost:8000/embeddings
2025-06-04 11:58:53.313 +04:00 [INF] Received HTTP response headers after 2169.7398ms - 200
2025-06-04 11:58:53.318 +04:00 [INF] End processing HTTP request after 2187.7373ms - 200
2025-06-04 11:58:53.322 +04:00 [INF] Generated 1/1 embeddings successfully
2025-06-04 11:58:53.324 +04:00 [INF] Vectorization completed. Successful: 1/1, Tokens: 39, Cost: $0.0000, Time: 4273ms
2025-06-04 11:59:18.342 +04:00 [INF] Starting routing for 1 chunks
2025-06-04 11:59:18.371 +04:00 [INF] Routing completed for 1 chunks in 0ms
2025-06-04 11:59:20.853 +04:00 [INF] Storing 1 vectors in "MongoDB"/legal_other_chunks
2025-06-04 11:59:20.873 +04:00 [INF] Vector storage completed. Stored: 1/1, Time: 16ms
2025-06-04 11:59:20.876 +04:00 [INF] Starting quality assessment for document "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7"
2025-06-04 11:59:20.877 +04:00 [INF] Quality assessment completed for document "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7". Score: 0.67, Passed: false, Issues: 2, Time: 0ms
2025-06-04 11:59:20.891 +04:00 [INF] Document processing completed successfully for "29d82cbb-ddf0-4ec1-a5d0-38c75c8c4aa7". Total time: 34228ms, Chunks: 1, Cost: $0.0000
2025-06-04 12:00:03.620 +04:00 [INF] Generating processing statistics
2025-06-04 12:02:24.974 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 12:02:24.986 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 12:02:24.991 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 63f7950b-39e6-44e1-957c-8f0095694a23
2025-06-04 12:02:24.997 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 0d950e39-002e-4e91-b159-8f8d53fb5516
2025-06-04 12:02:24.998 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:02:25.030 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:02:25.038 +04:00 [INF] Request OPTIONS /api/documents completed in 39ms with status 204 (Correlation ID: 63f7950b-39e6-44e1-957c-8f0095694a23)
2025-06-04 12:02:25.041 +04:00 [INF] Request OPTIONS /api/documents completed in 11ms with status 204 (Correlation ID: 0d950e39-002e-4e91-b159-8f8d53fb5516)
2025-06-04 12:02:25.047 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 72.8904ms
2025-06-04 12:02:25.066 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 80.697ms
2025-06-04 12:02:25.063 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 12:02:25.089 +04:00 [INF] Request GET /api/documents started with correlation ID 92381e05-3b5d-401e-a3f6-ec8cf684c69f
2025-06-04 12:02:25.091 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:02:25.092 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:02:25.093 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:02:25.098 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:02:25.108 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 12:02:25.112 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 12:02:25.120 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 16.5676ms
2025-06-04 12:02:25.121 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 12:02:25.122 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:02:25.125 +04:00 [INF] Request GET /api/documents started with correlation ID 95d75ff1-35ed-4c54-ae71-7e163f3d5bb2
2025-06-04 12:02:25.127 +04:00 [INF] Request GET /api/documents completed in 36ms with status 200 (Correlation ID: 92381e05-3b5d-401e-a3f6-ec8cf684c69f)
2025-06-04 12:02:25.130 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:02:25.135 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 72.3112ms
2025-06-04 12:02:25.137 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:02:25.149 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:02:25.153 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:02:25.164 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 12:02:25.168 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 12:02:25.171 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 14.7173ms
2025-06-04 12:02:25.173 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:02:25.174 +04:00 [INF] Request GET /api/documents completed in 43ms with status 200 (Correlation ID: 95d75ff1-35ed-4c54-ae71-7e163f3d5bb2)
2025-06-04 12:02:25.178 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 56.9715ms
2025-06-04 12:02:49.467 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-04 12:02:49.481 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID c058b9d7-f8a6-4553-a491-032d354fc5d1
2025-06-04 12:02:49.491 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:02:49.498 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 6ms with status 204 (Correlation ID: c058b9d7-f8a6-4553-a491-032d354fc5d1)
2025-06-04 12:02:49.510 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 43.0262ms
2025-06-04 12:02:49.519 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryaKAahuEzWvBCTsWh 869509
2025-06-04 12:02:49.563 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 00a46629-d86f-4b74-9de5-b8f86e5598d3
2025-06-04 12:02:49.569 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:02:49.572 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:02:49.575 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 12:02:49.588 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:02:49.626 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": Instructions.pdf
2025-06-04 12:02:49.636 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 12:02:49.638 +04:00 [INF] Processing document upload: Instructions.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 12:02:49.665 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-04 12:02:49.677 +04:00 [INF] File stored successfully: Instructions.pdf -> d3b02f3c-bbdb-43c0-b177-2617b6955ae4.pdf
2025-06-04 12:02:49.696 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-04 12:02:49.706 +04:00 [INF] Document added successfully: "b3982438-15e5-4d74-84a7-8140ee45de5f"
2025-06-04 12:02:49.708 +04:00 [INF] Document uploaded successfully: "b3982438-15e5-4d74-84a7-8140ee45de5f" - Instructions.pdf
2025-06-04 12:02:49.711 +04:00 [INF] Background processing started for document "b3982438-15e5-4d74-84a7-8140ee45de5f"
2025-06-04 12:02:49.711 +04:00 [INF] Starting orchestrated processing for document "b3982438-15e5-4d74-84a7-8140ee45de5f": Instructions.pdf
2025-06-04 12:02:49.713 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "b3982438-15e5-4d74-84a7-8140ee45de5f", Processing started: true
2025-06-04 12:02:49.721 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-04 12:02:49.727 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 131.8012ms
2025-06-04 12:02:49.729 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 12:02:49.735 +04:00 [INF] Request POST /api/documents/upload completed in 166ms with status 200 (Correlation ID: 00a46629-d86f-4b74-9de5-b8f86e5598d3)
2025-06-04 12:02:49.738 +04:00 [INF] Starting text extraction for document "b3982438-15e5-4d74-84a7-8140ee45de5f": Instructions.pdf
2025-06-04 12:02:49.743 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 224.1163ms
2025-06-04 12:02:49.746 +04:00 [INF] Extracting text from Instructions.pdf (MIME: application/pdf)
2025-06-04 12:02:49.756 +04:00 [INF] Text extraction successful for Instructions.pdf. Length: 159
2025-06-04 12:02:49.757 +04:00 [INF] Text extraction completed for document "b3982438-15e5-4d74-84a7-8140ee45de5f". Length: 159, Confidence: 0.70, Time: 13ms
2025-06-04 12:02:49.799 +04:00 [INF] Starting classification for document "b3982438-15e5-4d74-84a7-8140ee45de5f": Instructions.pdf
2025-06-04 12:02:49.804 +04:00 [INF] Classifying text into legal domains. Text length: 159
2025-06-04 12:02:49.816 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 12:02:49.821 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 12:02:52.233 +04:00 [INF] Received HTTP response headers after 2409.0159ms - 429
2025-06-04 12:02:52.236 +04:00 [INF] End processing HTTP request after 2420.3735ms - 429
2025-06-04 12:02:52.240 +04:00 [WRN] AI classification failed, using rule-based result: "Tax" (0.25)
2025-06-04 12:02:52.244 +04:00 [INF] Classification completed for document "b3982438-15e5-4d74-84a7-8140ee45de5f". Domain: "Tax", Confidence: 0.25, Keywords: 10, Entities: 0, Time: 2439ms
2025-06-04 12:02:52.284 +04:00 [INF] Starting chunking for document "b3982438-15e5-4d74-84a7-8140ee45de5f" with strategy "Semantic"
2025-06-04 12:02:52.288 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 159
2025-06-04 12:02:52.291 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-04 12:02:52.293 +04:00 [INF] Chunking completed for document "b3982438-15e5-4d74-84a7-8140ee45de5f". Chunks: 1, Average size: 159, Time: 5ms
2025-06-04 12:02:52.314 +04:00 [INF] Starting vectorization with model "HuggingFace" for 1 chunks
2025-06-04 12:02:52.319 +04:00 [INF] Generating embeddings for 1 texts using local model
2025-06-04 12:02:52.323 +04:00 [INF] Start processing HTTP request GET http://localhost:8000/health
2025-06-04 12:02:52.326 +04:00 [INF] Sending HTTP request GET http://localhost:8000/health
2025-06-04 12:02:54.382 +04:00 [INF] Received HTTP response headers after 2053.9614ms - 200
2025-06-04 12:02:54.390 +04:00 [INF] End processing HTTP request after 2067.4293ms - 200
2025-06-04 12:02:54.399 +04:00 [INF] Start processing HTTP request POST http://localhost:8000/embeddings
2025-06-04 12:02:54.408 +04:00 [INF] Sending HTTP request POST http://localhost:8000/embeddings
2025-06-04 12:02:56.540 +04:00 [INF] Received HTTP response headers after 2124.8418ms - 200
2025-06-04 12:02:56.545 +04:00 [INF] End processing HTTP request after 2145.9398ms - 200
2025-06-04 12:02:56.553 +04:00 [INF] Generated 1/1 embeddings successfully
2025-06-04 12:02:56.557 +04:00 [INF] Vectorization completed. Successful: 1/1, Tokens: 39, Cost: $0.0000, Time: 4238ms
2025-06-04 12:03:04.461 +04:00 [INF] Starting routing for 1 chunks
2025-06-04 12:03:04.468 +04:00 [INF] Routing completed for 1 chunks in 0ms
2025-06-04 12:03:14.571 +04:00 [INF] Storing 1 vectors in "MongoDB"/legal_other_chunks
2025-06-04 12:03:14.656 +04:00 [INF] Vector storage completed. Stored: 1/1, Time: 6ms
2025-06-04 12:03:14.664 +04:00 [INF] Starting quality assessment for document "b3982438-15e5-4d74-84a7-8140ee45de5f"
2025-06-04 12:03:14.665 +04:00 [INF] Quality assessment completed for document "b3982438-15e5-4d74-84a7-8140ee45de5f". Score: 0.67, Passed: false, Issues: 2, Time: 0ms
2025-06-04 12:03:14.676 +04:00 [INF] Document processing completed successfully for "b3982438-15e5-4d74-84a7-8140ee45de5f". Total time: 24951ms, Chunks: 1, Cost: $0.0000
2025-06-04 12:05:56.356 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 12:05:56.399 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 12:05:56.436 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 4e8de6ac-8e39-435a-a44e-2da140183897
2025-06-04 12:05:56.440 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID f4d6e86d-0756-47c1-a10a-c724cb3a32b1
2025-06-04 12:05:56.443 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:05:56.445 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:05:56.446 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 4e8de6ac-8e39-435a-a44e-2da140183897)
2025-06-04 12:05:56.447 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: f4d6e86d-0756-47c1-a10a-c724cb3a32b1)
2025-06-04 12:05:56.450 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 94.7277ms
2025-06-04 12:05:56.464 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 65.2205ms
2025-06-04 12:05:56.462 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 12:05:56.481 +04:00 [INF] Request GET /api/documents started with correlation ID da8138e7-85b5-4c7f-b333-bc808a1eea81
2025-06-04 12:05:56.483 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:05:56.485 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:05:56.486 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:05:56.488 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:05:56.514 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 12:05:56.518 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 12:05:56.522 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 30.0856ms
2025-06-04 12:05:56.524 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 12:05:56.525 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:05:56.528 +04:00 [INF] Request GET /api/documents started with correlation ID 3bc0b5f4-00b9-418e-8b43-e61be42f8097
2025-06-04 12:05:56.529 +04:00 [INF] Request GET /api/documents completed in 46ms with status 200 (Correlation ID: da8138e7-85b5-4c7f-b333-bc808a1eea81)
2025-06-04 12:05:56.531 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:05:56.535 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 72.6803ms
2025-06-04 12:05:56.537 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:05:56.544 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:05:56.545 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:05:56.551 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 12:05:56.555 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 12:05:56.557 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 10.6398ms
2025-06-04 12:05:56.559 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:05:56.562 +04:00 [INF] Request GET /api/documents completed in 30ms with status 200 (Correlation ID: 3bc0b5f4-00b9-418e-8b43-e61be42f8097)
2025-06-04 12:05:56.565 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 41.0024ms
2025-06-04 12:06:09.948 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-04 12:06:09.954 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 5f6696bb-e95f-4017-b737-c197e14ac8c2
2025-06-04 12:06:09.974 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:06:09.977 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 3ms with status 204 (Correlation ID: 5f6696bb-e95f-4017-b737-c197e14ac8c2)
2025-06-04 12:06:09.985 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 36.7733ms
2025-06-04 12:06:09.990 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryebzQllpOXvkvwjZl 474196
2025-06-04 12:06:10.002 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 07297feb-299f-40c9-b788-979f00e5be50
2025-06-04 12:06:10.004 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:06:10.006 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:06:10.010 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 12:06:10.014 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:06:10.026 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:06:10.031 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 12:06:10.032 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 12:06:10.047 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-04 12:06:10.056 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> 2282c75f-99c8-4fad-8fae-dcd92a89157e.pdf
2025-06-04 12:06:10.067 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-04 12:06:10.082 +04:00 [INF] Document added successfully: "1ffb8d1f-3375-46f1-a7df-8d4076dff08c"
2025-06-04 12:06:10.085 +04:00 [INF] Document uploaded successfully: "1ffb8d1f-3375-46f1-a7df-8d4076dff08c" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:06:10.088 +04:00 [INF] Background processing started for document "1ffb8d1f-3375-46f1-a7df-8d4076dff08c"
2025-06-04 12:06:10.088 +04:00 [INF] Starting orchestrated processing for document "1ffb8d1f-3375-46f1-a7df-8d4076dff08c": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:06:10.091 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "1ffb8d1f-3375-46f1-a7df-8d4076dff08c", Processing started: true
2025-06-04 12:06:10.100 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-04 12:06:10.103 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 84.0767ms
2025-06-04 12:06:10.108 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 12:06:10.113 +04:00 [INF] Starting text extraction for document "1ffb8d1f-3375-46f1-a7df-8d4076dff08c": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:06:10.113 +04:00 [INF] Request POST /api/documents/upload completed in 108ms with status 200 (Correlation ID: 07297feb-299f-40c9-b788-979f00e5be50)
2025-06-04 12:06:10.117 +04:00 [INF] Extracting text from INTRODUCTION AU DROIT DU TRAVAIL.pdf (MIME: application/pdf)
2025-06-04 12:06:10.123 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 132.3888ms
2025-06-04 12:06:10.126 +04:00 [INF] Text extraction successful for INTRODUCTION AU DROIT DU TRAVAIL.pdf. Length: 179
2025-06-04 12:06:10.136 +04:00 [INF] Text extraction completed for document "1ffb8d1f-3375-46f1-a7df-8d4076dff08c". Length: 179, Confidence: 0.70, Time: 19ms
2025-06-04 12:06:10.175 +04:00 [INF] Starting classification for document "1ffb8d1f-3375-46f1-a7df-8d4076dff08c": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:06:10.182 +04:00 [INF] Classifying text into legal domains. Text length: 179
2025-06-04 12:06:10.186 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 12:06:10.188 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 12:06:12.332 +04:00 [INF] Received HTTP response headers after 2141.3015ms - 429
2025-06-04 12:06:12.337 +04:00 [INF] End processing HTTP request after 2150.5434ms - 429
2025-06-04 12:06:12.343 +04:00 [WRN] AI classification failed, using rule-based result: "Labor" (0.33)
2025-06-04 12:06:12.350 +04:00 [INF] Classification completed for document "1ffb8d1f-3375-46f1-a7df-8d4076dff08c". Domain: "Labor", Confidence: 0.33, Keywords: 10, Entities: 0, Time: 2168ms
2025-06-04 12:06:12.378 +04:00 [INF] Starting chunking for document "1ffb8d1f-3375-46f1-a7df-8d4076dff08c" with strategy "Semantic"
2025-06-04 12:06:12.380 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 179
2025-06-04 12:06:12.382 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-04 12:06:12.385 +04:00 [INF] Chunking completed for document "1ffb8d1f-3375-46f1-a7df-8d4076dff08c". Chunks: 1, Average size: 179, Time: 4ms
2025-06-04 12:06:12.410 +04:00 [INF] Starting vectorization with model "HuggingFace" for 1 chunks
2025-06-04 12:06:12.413 +04:00 [INF] Generating embeddings for 1 texts using local model
2025-06-04 12:06:12.415 +04:00 [INF] Start processing HTTP request GET http://localhost:8000/health
2025-06-04 12:06:12.418 +04:00 [INF] Sending HTTP request GET http://localhost:8000/health
2025-06-04 12:06:14.461 +04:00 [INF] Received HTTP response headers after 2040.9192ms - 200
2025-06-04 12:06:14.465 +04:00 [INF] End processing HTTP request after 2050.0648ms - 200
2025-06-04 12:06:14.468 +04:00 [INF] Start processing HTTP request POST http://localhost:8000/embeddings
2025-06-04 12:06:14.471 +04:00 [INF] Sending HTTP request POST http://localhost:8000/embeddings
2025-06-04 12:06:16.576 +04:00 [INF] Received HTTP response headers after 2101.3615ms - 200
2025-06-04 12:06:16.581 +04:00 [INF] End processing HTTP request after 2112.4318ms - 200
2025-06-04 12:06:16.584 +04:00 [INF] Generated 1/1 embeddings successfully
2025-06-04 12:06:16.590 +04:00 [INF] Vectorization completed. Successful: 1/1, Tokens: 44, Cost: $0.0000, Time: 4177ms
2025-06-04 12:06:28.139 +04:00 [INF] Starting routing for 1 chunks
2025-06-04 12:06:28.170 +04:00 [INF] Routing completed for 1 chunks in 0ms
2025-06-04 12:11:16.792 +04:00 [INF] Storing 1 vectors in "MongoDB"/legal_labor_chunks
2025-06-04 12:11:16.997 +04:00 [INF] Vector storage completed. Stored: 1/1, Time: 42ms
2025-06-04 12:11:17.017 +04:00 [INF] Starting quality assessment for document "1ffb8d1f-3375-46f1-a7df-8d4076dff08c"
2025-06-04 12:11:17.024 +04:00 [INF] Quality assessment completed for document "1ffb8d1f-3375-46f1-a7df-8d4076dff08c". Score: 0.67, Passed: false, Issues: 2, Time: 0ms
2025-06-04 12:11:17.058 +04:00 [INF] Document processing completed successfully for "1ffb8d1f-3375-46f1-a7df-8d4076dff08c". Total time: 306942ms, Chunks: 1, Cost: $0.0000
2025-06-04 12:12:53.804 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 12:12:53.821 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 12:12:53.830 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 10f6a562-952f-41b2-b143-6339e960681d
2025-06-04 12:12:53.839 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID d13837a3-4382-43cc-9c5b-5e04e7f47bab
2025-06-04 12:12:53.840 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:12:53.843 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:12:53.844 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: 10f6a562-952f-41b2-b143-6339e960681d)
2025-06-04 12:12:53.845 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: d13837a3-4382-43cc-9c5b-5e04e7f47bab)
2025-06-04 12:12:53.849 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 45.1462ms
2025-06-04 12:12:53.864 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 42.8313ms
2025-06-04 12:12:53.862 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 12:12:53.895 +04:00 [INF] Request GET /api/documents started with correlation ID 70b0551e-fcaa-4e94-890d-6829c214368c
2025-06-04 12:12:53.898 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:12:53.899 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:12:53.901 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:12:53.911 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:12:53.923 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 12:12:53.931 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 12:12:53.942 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 27.3649ms
2025-06-04 12:12:53.944 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 12:12:53.945 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:12:53.952 +04:00 [INF] Request GET /api/documents started with correlation ID 7791d501-b001-43e7-94d6-99d6a5d05340
2025-06-04 12:12:53.953 +04:00 [INF] Request GET /api/documents completed in 55ms with status 200 (Correlation ID: 70b0551e-fcaa-4e94-890d-6829c214368c)
2025-06-04 12:12:53.956 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:12:53.963 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 101.4907ms
2025-06-04 12:12:53.966 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:12:53.973 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:12:53.975 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:12:53.984 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 12:12:53.989 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 12:12:53.991 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 13.9175ms
2025-06-04 12:12:53.997 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:12:53.998 +04:00 [INF] Request GET /api/documents completed in 42ms with status 200 (Correlation ID: 7791d501-b001-43e7-94d6-99d6a5d05340)
2025-06-04 12:12:54.001 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 57.4751ms
2025-06-04 12:13:09.002 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-04 12:13:09.011 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 4c3cb44d-2023-417c-9755-145692db5ed0
2025-06-04 12:13:09.017 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:13:09.019 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 2ms with status 204 (Correlation ID: 4c3cb44d-2023-417c-9755-145692db5ed0)
2025-06-04 12:13:09.024 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 21.6506ms
2025-06-04 12:13:09.026 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundary6MBg3F5yJY2TCjYW 869509
2025-06-04 12:13:09.046 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 2e5826b9-a6cd-4d92-bbc2-09f0bbb386df
2025-06-04 12:13:09.048 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:13:09.049 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:13:09.050 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 12:13:09.058 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:13:09.074 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": Instructions.pdf
2025-06-04 12:13:09.081 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 12:13:09.083 +04:00 [INF] Processing document upload: Instructions.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 12:13:09.101 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-04 12:13:09.112 +04:00 [INF] File stored successfully: Instructions.pdf -> dbe98785-5503-43e6-bfe2-3e35c037a2bd.pdf
2025-06-04 12:13:09.129 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-04 12:13:09.138 +04:00 [INF] Document added successfully: "d6a4f392-c23a-47d2-82aa-684b3aadf5ab"
2025-06-04 12:13:09.140 +04:00 [INF] Document uploaded successfully: "d6a4f392-c23a-47d2-82aa-684b3aadf5ab" - Instructions.pdf
2025-06-04 12:13:09.142 +04:00 [INF] Background processing started for document "d6a4f392-c23a-47d2-82aa-684b3aadf5ab"
2025-06-04 12:13:09.142 +04:00 [INF] Starting orchestrated processing for document "d6a4f392-c23a-47d2-82aa-684b3aadf5ab": Instructions.pdf
2025-06-04 12:13:09.145 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "d6a4f392-c23a-47d2-82aa-684b3aadf5ab", Processing started: true
2025-06-04 12:13:09.149 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-04 12:13:09.154 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 91.8567ms
2025-06-04 12:13:09.160 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 12:13:09.163 +04:00 [INF] Request POST /api/documents/upload completed in 115ms with status 200 (Correlation ID: 2e5826b9-a6cd-4d92-bbc2-09f0bbb386df)
2025-06-04 12:13:09.164 +04:00 [INF] Starting text extraction for document "d6a4f392-c23a-47d2-82aa-684b3aadf5ab": Instructions.pdf
2025-06-04 12:13:09.167 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 141.2596ms
2025-06-04 12:13:09.172 +04:00 [INF] Extracting text from Instructions.pdf (MIME: application/pdf)
2025-06-04 12:13:09.181 +04:00 [INF] Text extraction successful for Instructions.pdf. Length: 159
2025-06-04 12:13:09.184 +04:00 [INF] Text extraction completed for document "d6a4f392-c23a-47d2-82aa-684b3aadf5ab". Length: 159, Confidence: 0.70, Time: 14ms
2025-06-04 12:13:09.218 +04:00 [INF] Starting classification for document "d6a4f392-c23a-47d2-82aa-684b3aadf5ab": Instructions.pdf
2025-06-04 12:13:09.224 +04:00 [INF] Classifying text into legal domains. Text length: 159
2025-06-04 12:13:09.235 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 12:13:09.239 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 12:13:11.545 +04:00 [INF] Received HTTP response headers after 2301.7065ms - 429
2025-06-04 12:13:11.549 +04:00 [INF] End processing HTTP request after 2313.2977ms - 429
2025-06-04 12:13:11.552 +04:00 [WRN] AI classification failed, using rule-based result: "Tax" (0.25)
2025-06-04 12:13:11.555 +04:00 [INF] Classification completed for document "d6a4f392-c23a-47d2-82aa-684b3aadf5ab". Domain: "Tax", Confidence: 0.25, Keywords: 10, Entities: 0, Time: 2331ms
2025-06-04 12:13:11.578 +04:00 [INF] Starting chunking for document "d6a4f392-c23a-47d2-82aa-684b3aadf5ab" with strategy "Semantic"
2025-06-04 12:13:11.580 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 159
2025-06-04 12:13:11.582 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-04 12:13:11.584 +04:00 [INF] Chunking completed for document "d6a4f392-c23a-47d2-82aa-684b3aadf5ab". Chunks: 1, Average size: 159, Time: 4ms
2025-06-04 12:13:11.612 +04:00 [INF] Starting vectorization with model "HuggingFace" for 1 chunks
2025-06-04 12:13:11.616 +04:00 [INF] Generating embeddings for 1 texts using local model
2025-06-04 12:13:11.618 +04:00 [INF] Start processing HTTP request GET http://localhost:8000/health
2025-06-04 12:13:11.622 +04:00 [INF] Sending HTTP request GET http://localhost:8000/health
2025-06-04 12:13:13.665 +04:00 [INF] Received HTTP response headers after 2038.9742ms - 200
2025-06-04 12:13:13.672 +04:00 [INF] End processing HTTP request after 2053.8659ms - 200
2025-06-04 12:13:13.679 +04:00 [INF] Start processing HTTP request POST http://localhost:8000/embeddings
2025-06-04 12:13:13.683 +04:00 [INF] Sending HTTP request POST http://localhost:8000/embeddings
2025-06-04 12:13:15.802 +04:00 [INF] Received HTTP response headers after 2116.2304ms - 200
2025-06-04 12:13:15.806 +04:00 [INF] End processing HTTP request after 2126.4967ms - 200
2025-06-04 12:13:15.813 +04:00 [INF] Generated 1/1 embeddings successfully
2025-06-04 12:13:15.816 +04:00 [INF] Vectorization completed. Successful: 1/1, Tokens: 39, Cost: $0.0000, Time: 4199ms
2025-06-04 12:13:15.827 +04:00 [INF] Starting routing for 1 chunks
2025-06-04 12:13:15.829 +04:00 [INF] Routing completed for 1 chunks in 0ms
2025-06-04 12:13:16.754 +04:00 [INF] Created Qdrant collection: legal-other with dimension 384
2025-06-04 12:13:33.377 +04:00 [INF] Storing 1 vectors in "Qdrant"/legal-other
2025-06-04 12:14:18.699 +04:00 [INF] Successfully stored 1 vectors in Qdrant collection legal-other
2025-06-04 12:14:18.702 +04:00 [INF] Vector storage completed. Stored: 1/1, Time: 44645ms
2025-06-04 12:14:18.705 +04:00 [INF] Starting quality assessment for document "d6a4f392-c23a-47d2-82aa-684b3aadf5ab"
2025-06-04 12:14:18.706 +04:00 [INF] Quality assessment completed for document "d6a4f392-c23a-47d2-82aa-684b3aadf5ab". Score: 0.67, Passed: false, Issues: 2, Time: 0ms
2025-06-04 12:14:18.718 +04:00 [INF] Document processing completed successfully for "d6a4f392-c23a-47d2-82aa-684b3aadf5ab". Total time: 69562ms, Chunks: 1, Cost: $0.0000
2025-06-04 12:20:15.326 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 12:20:15.326 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 12:20:15.412 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 55cca5ba-1223-49fb-a5d4-57b6d8dce037
2025-06-04 12:20:15.434 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 2944a214-04fc-4639-b2cb-f586604c0bf3
2025-06-04 12:20:15.435 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:20:15.438 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:20:15.440 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: 55cca5ba-1223-49fb-a5d4-57b6d8dce037)
2025-06-04 12:20:15.441 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: 2944a214-04fc-4639-b2cb-f586604c0bf3)
2025-06-04 12:20:15.444 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 117.2571ms
2025-06-04 12:20:15.447 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 120.2288ms
2025-06-04 12:20:15.446 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 12:20:15.463 +04:00 [INF] Request GET /api/documents started with correlation ID 0ef79e22-d222-4217-ab01-296f909e034e
2025-06-04 12:20:15.465 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:20:15.466 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:20:15.467 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:20:15.469 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:20:15.498 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 12:20:15.503 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 12:20:15.508 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 35.6822ms
2025-06-04 12:20:15.510 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:20:15.510 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 12:20:15.511 +04:00 [INF] Request GET /api/documents completed in 46ms with status 200 (Correlation ID: 0ef79e22-d222-4217-ab01-296f909e034e)
2025-06-04 12:20:15.515 +04:00 [INF] Request GET /api/documents started with correlation ID c2483617-8ced-4fed-a976-9c5a34b52ec1
2025-06-04 12:20:15.517 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 70.4463ms
2025-06-04 12:20:15.519 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:20:15.525 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:20:15.526 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:20:15.527 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:20:15.535 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 12:20:15.540 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 12:20:15.542 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 11.9154ms
2025-06-04 12:20:15.545 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:20:15.547 +04:00 [INF] Request GET /api/documents completed in 28ms with status 200 (Correlation ID: c2483617-8ced-4fed-a976-9c5a34b52ec1)
2025-06-04 12:20:15.549 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 39.2119ms
2025-06-04 12:20:26.866 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-04 12:20:26.874 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 14c9166e-6a2d-4563-90d9-e6abbf48c79b
2025-06-04 12:20:26.878 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:20:26.881 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 2ms with status 204 (Correlation ID: 14c9166e-6a2d-4563-90d9-e6abbf48c79b)
2025-06-04 12:20:26.885 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 19.4484ms
2025-06-04 12:20:26.894 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryb0kR5LUdYQgLMBpn 474196
2025-06-04 12:20:26.907 +04:00 [INF] Request POST /api/documents/upload started with correlation ID e4721aef-5930-4630-93c3-8f0851628990
2025-06-04 12:20:26.910 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:20:26.912 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:20:26.914 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 12:20:26.916 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:20:26.925 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:20:26.930 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 12:20:26.932 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 12:20:26.943 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-04 12:20:26.951 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> b357a605-eedb-41cc-910b-6ce30b966a3e.pdf
2025-06-04 12:20:26.957 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-04 12:20:26.964 +04:00 [INF] Document added successfully: "d11a38a5-f248-4a87-b4e6-833838f92e65"
2025-06-04 12:20:26.965 +04:00 [INF] Document uploaded successfully: "d11a38a5-f248-4a87-b4e6-833838f92e65" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:20:26.967 +04:00 [INF] Background processing started for document "d11a38a5-f248-4a87-b4e6-833838f92e65"
2025-06-04 12:20:26.968 +04:00 [INF] Starting orchestrated processing for document "d11a38a5-f248-4a87-b4e6-833838f92e65": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:20:26.969 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "d11a38a5-f248-4a87-b4e6-833838f92e65", Processing started: true
2025-06-04 12:20:26.975 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-04 12:20:26.978 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 59.6784ms
2025-06-04 12:20:26.984 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 12:20:26.987 +04:00 [INF] Request POST /api/documents/upload completed in 77ms with status 200 (Correlation ID: e4721aef-5930-4630-93c3-8f0851628990)
2025-06-04 12:20:26.989 +04:00 [INF] Starting text extraction for document "d11a38a5-f248-4a87-b4e6-833838f92e65": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:20:26.996 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 102.0681ms
2025-06-04 12:20:27.000 +04:00 [INF] Extracting text from INTRODUCTION AU DROIT DU TRAVAIL.pdf (MIME: application/pdf)
2025-06-04 12:20:27.011 +04:00 [INF] Text extraction successful for INTRODUCTION AU DROIT DU TRAVAIL.pdf. Length: 179
2025-06-04 12:20:27.015 +04:00 [INF] Text extraction completed for document "d11a38a5-f248-4a87-b4e6-833838f92e65". Length: 179, Confidence: 0.70, Time: 15ms
2025-06-04 12:20:27.057 +04:00 [INF] Starting classification for document "d11a38a5-f248-4a87-b4e6-833838f92e65": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:20:27.060 +04:00 [INF] Classifying text into legal domains. Text length: 179
2025-06-04 12:20:27.063 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 12:20:27.065 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 12:20:30.874 +04:00 [INF] Received HTTP response headers after 3807.8775ms - 429
2025-06-04 12:20:30.879 +04:00 [INF] End processing HTTP request after 3815.3184ms - 429
2025-06-04 12:20:30.882 +04:00 [WRN] AI classification failed, using rule-based result: "Labor" (0.33)
2025-06-04 12:20:30.885 +04:00 [INF] Classification completed for document "d11a38a5-f248-4a87-b4e6-833838f92e65". Domain: "Labor", Confidence: 0.33, Keywords: 10, Entities: 0, Time: 3825ms
2025-06-04 12:20:30.911 +04:00 [INF] Starting chunking for document "d11a38a5-f248-4a87-b4e6-833838f92e65" with strategy "Semantic"
2025-06-04 12:20:30.914 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 179
2025-06-04 12:20:30.916 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-04 12:20:30.918 +04:00 [INF] Chunking completed for document "d11a38a5-f248-4a87-b4e6-833838f92e65". Chunks: 1, Average size: 179, Time: 4ms
2025-06-04 12:20:30.944 +04:00 [INF] Starting vectorization with model "HuggingFace" for 1 chunks
2025-06-04 12:20:30.948 +04:00 [INF] Generating embeddings for 1 texts using local model
2025-06-04 12:20:30.950 +04:00 [INF] Start processing HTTP request GET http://localhost:8000/health
2025-06-04 12:20:30.953 +04:00 [INF] Sending HTTP request GET http://localhost:8000/health
2025-06-04 12:20:33.008 +04:00 [INF] Received HTTP response headers after 2052.6223ms - 200
2025-06-04 12:20:33.012 +04:00 [INF] End processing HTTP request after 2061.4175ms - 200
2025-06-04 12:20:33.016 +04:00 [INF] Start processing HTTP request POST http://localhost:8000/embeddings
2025-06-04 12:20:33.019 +04:00 [INF] Sending HTTP request POST http://localhost:8000/embeddings
2025-06-04 12:20:35.173 +04:00 [INF] Received HTTP response headers after 2150.4774ms - 200
2025-06-04 12:20:35.190 +04:00 [INF] End processing HTTP request after 2174.3631ms - 200
2025-06-04 12:20:35.194 +04:00 [INF] Generated 1/1 embeddings successfully
2025-06-04 12:20:35.197 +04:00 [INF] Vectorization completed. Successful: 1/1, Tokens: 44, Cost: $0.0000, Time: 4248ms
2025-06-04 12:20:35.213 +04:00 [INF] Starting routing for 1 chunks
2025-06-04 12:20:35.216 +04:00 [INF] Routing completed for 1 chunks in 0ms
2025-06-04 12:20:43.155 +04:00 [INF] Storing 1 vectors in "MongoDB"/legal_labor_chunks
2025-06-04 12:20:43.169 +04:00 [INF] Vector storage completed. Stored: 1/1, Time: 4ms
2025-06-04 12:20:43.174 +04:00 [INF] Starting quality assessment for document "d11a38a5-f248-4a87-b4e6-833838f92e65"
2025-06-04 12:20:43.177 +04:00 [INF] Quality assessment completed for document "d11a38a5-f248-4a87-b4e6-833838f92e65". Score: 0.67, Passed: false, Issues: 2, Time: 0ms
2025-06-04 12:20:43.193 +04:00 [INF] Document processing completed successfully for "d11a38a5-f248-4a87-b4e6-833838f92e65". Total time: 16210ms, Chunks: 1, Cost: $0.0000
2025-06-04 12:24:54.151 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 12:24:54.167 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-04 12:24:54.178 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID c51a4404-721b-43ca-998c-aaa6357e573d
2025-06-04 12:24:54.184 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 12ed2a0f-ad69-4a90-9972-2b4ac9371554
2025-06-04 12:24:54.185 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:24:54.187 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:24:54.188 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: c51a4404-721b-43ca-998c-aaa6357e573d)
2025-06-04 12:24:54.189 +04:00 [INF] Request OPTIONS /api/documents completed in 1ms with status 204 (Correlation ID: 12ed2a0f-ad69-4a90-9972-2b4ac9371554)
2025-06-04 12:24:54.191 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 40.0598ms
2025-06-04 12:24:54.202 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 35.5106ms
2025-06-04 12:24:54.200 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 12:24:54.222 +04:00 [INF] Request GET /api/documents started with correlation ID 4eb6f7c8-e852-4804-8b8d-55b455be069b
2025-06-04 12:24:54.224 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:24:54.225 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:24:54.226 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:24:54.230 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:24:54.239 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 12:24:54.244 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 12:24:54.252 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 18.0654ms
2025-06-04 12:24:54.254 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:24:54.255 +04:00 [INF] Request GET /api/documents completed in 31ms with status 200 (Correlation ID: 4eb6f7c8-e852-4804-8b8d-55b455be069b)
2025-06-04 12:24:54.254 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-04 12:24:54.258 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 57.8492ms
2025-06-04 12:24:54.261 +04:00 [INF] Request GET /api/documents started with correlation ID 29a82b14-9239-4001-878f-22914af683d3
2025-06-04 12:24:54.267 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:24:54.268 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:24:54.270 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:24:54.272 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:24:54.282 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-04 12:24:54.293 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 12:24:54.299 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 25.6727ms
2025-06-04 12:24:54.304 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-04 12:24:54.310 +04:00 [INF] Request GET /api/documents completed in 43ms with status 200 (Correlation ID: 29a82b14-9239-4001-878f-22914af683d3)
2025-06-04 12:24:54.318 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 64.3536ms
2025-06-04 12:25:06.299 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-04 12:25:06.322 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 83c8998c-7785-4279-84c7-c8a7b7edddf8
2025-06-04 12:25:06.331 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:25:06.336 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 5ms with status 204 (Correlation ID: 83c8998c-7785-4279-84c7-c8a7b7edddf8)
2025-06-04 12:25:06.345 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 46.1604ms
2025-06-04 12:25:06.351 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundarypOq4IERGj6TfzzGI 474196
2025-06-04 12:25:06.371 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 69e418c1-1e66-4616-b3a3-aa01c94aeeba
2025-06-04 12:25:06.375 +04:00 [INF] CORS policy execution successful.
2025-06-04 12:25:06.377 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 12:25:06.379 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 12:25:06.385 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-04 12:25:06.397 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:25:06.403 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 12:25:06.405 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 12:25:06.412 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-04 12:25:06.419 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> 87d408a9-357b-4c84-b007-ed37c13739b5.pdf
2025-06-04 12:25:06.437 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-04 12:25:06.444 +04:00 [INF] Document added successfully: "d3903fb3-4ea3-4f91-b423-81448cdaf718"
2025-06-04 12:25:06.448 +04:00 [INF] Document uploaded successfully: "d3903fb3-4ea3-4f91-b423-81448cdaf718" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:25:06.450 +04:00 [INF] Background processing started for document "d3903fb3-4ea3-4f91-b423-81448cdaf718"
2025-06-04 12:25:06.450 +04:00 [INF] Starting orchestrated processing for document "d3903fb3-4ea3-4f91-b423-81448cdaf718": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:25:06.451 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "d3903fb3-4ea3-4f91-b423-81448cdaf718", Processing started: true
2025-06-04 12:25:06.456 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-04 12:25:06.460 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 72.04ms
2025-06-04 12:25:06.462 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-04 12:25:06.464 +04:00 [INF] Request POST /api/documents/upload completed in 89ms with status 200 (Correlation ID: 69e418c1-1e66-4616-b3a3-aa01c94aeeba)
2025-06-04 12:25:06.468 +04:00 [INF] Starting text extraction for document "d3903fb3-4ea3-4f91-b423-81448cdaf718": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:25:06.469 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 117.5631ms
2025-06-04 12:25:06.471 +04:00 [INF] Extracting text from INTRODUCTION AU DROIT DU TRAVAIL.pdf (MIME: application/pdf)
2025-06-04 12:25:06.477 +04:00 [INF] Text extraction successful for INTRODUCTION AU DROIT DU TRAVAIL.pdf. Length: 179
2025-06-04 12:25:06.479 +04:00 [INF] Text extraction completed for document "d3903fb3-4ea3-4f91-b423-81448cdaf718". Length: 179, Confidence: 0.70, Time: 8ms
2025-06-04 12:25:06.500 +04:00 [INF] Starting classification for document "d3903fb3-4ea3-4f91-b423-81448cdaf718": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-04 12:25:06.503 +04:00 [INF] Classifying text into legal domains. Text length: 179
2025-06-04 12:25:06.510 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 12:25:06.512 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-04 12:25:08.834 +04:00 [INF] Received HTTP response headers after 2320.5909ms - 429
2025-06-04 12:25:08.837 +04:00 [INF] End processing HTTP request after 2326.947ms - 429
2025-06-04 12:25:08.840 +04:00 [WRN] AI classification failed, using rule-based result: "Labor" (0.33)
2025-06-04 12:25:08.845 +04:00 [INF] Classification completed for document "d3903fb3-4ea3-4f91-b423-81448cdaf718". Domain: "Labor", Confidence: 0.33, Keywords: 10, Entities: 0, Time: 2341ms
2025-06-04 12:25:08.874 +04:00 [INF] Starting chunking for document "d3903fb3-4ea3-4f91-b423-81448cdaf718" with strategy "Semantic"
2025-06-04 12:25:08.877 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 179
2025-06-04 12:25:08.880 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-04 12:25:08.883 +04:00 [INF] Chunking completed for document "d3903fb3-4ea3-4f91-b423-81448cdaf718". Chunks: 1, Average size: 179, Time: 5ms
2025-06-04 12:25:08.909 +04:00 [INF] Starting vectorization with model "HuggingFace" for 1 chunks
2025-06-04 12:25:08.914 +04:00 [INF] Generating embeddings for 1 texts using local model
2025-06-04 12:25:08.916 +04:00 [INF] Start processing HTTP request GET http://localhost:8000/health
2025-06-04 12:25:08.918 +04:00 [INF] Sending HTTP request GET http://localhost:8000/health
2025-06-04 12:25:10.964 +04:00 [INF] Received HTTP response headers after 2041.7155ms - 200
2025-06-04 12:25:10.969 +04:00 [INF] End processing HTTP request after 2053.38ms - 200
2025-06-04 12:25:10.978 +04:00 [INF] Start processing HTTP request POST http://localhost:8000/embeddings
2025-06-04 12:25:10.981 +04:00 [INF] Sending HTTP request POST http://localhost:8000/embeddings
2025-06-04 12:25:13.092 +04:00 [INF] Received HTTP response headers after 2109.0095ms - 200
2025-06-04 12:25:13.098 +04:00 [INF] End processing HTTP request after 2119.8513ms - 200
2025-06-04 12:25:13.106 +04:00 [INF] Generated 1/1 embeddings successfully
2025-06-04 12:25:13.109 +04:00 [INF] Vectorization completed. Successful: 1/1, Tokens: 44, Cost: $0.0000, Time: 4195ms
2025-06-04 12:25:13.123 +04:00 [INF] Starting routing for 1 chunks
2025-06-04 12:25:13.130 +04:00 [INF] Routing completed for 1 chunks in 1ms
2025-06-04 12:25:13.630 +04:00 [INF] Created Qdrant collection: legal-labor with dimension 384
2025-06-04 12:25:21.008 +04:00 [INF] Storing 1 vectors in "Qdrant"/legal-labor
2025-06-04 12:26:25.435 +04:00 [INF] Successfully stored 1 vectors in Qdrant collection legal-labor
2025-06-04 12:26:27.116 +04:00 [INF] Vector storage completed. Stored: 1/1, Time: 66098ms
2025-06-04 12:26:27.121 +04:00 [INF] Starting quality assessment for document "d3903fb3-4ea3-4f91-b423-81448cdaf718"
2025-06-04 12:26:27.123 +04:00 [INF] Quality assessment completed for document "d3903fb3-4ea3-4f91-b423-81448cdaf718". Score: 0.67, Passed: false, Issues: 2, Time: 0ms
2025-06-04 12:26:27.147 +04:00 [INF] Document processing completed successfully for "d3903fb3-4ea3-4f91-b423-81448cdaf718". Total time: 80672ms, Chunks: 1, Cost: $0.0000
