{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information", "Hangfire": "Information"}}, "ConnectionStrings": {"PostgreSql": "Host=localhost;Port=5434;Database=data_preprocessing_db;Username=lexai_user;Password=lexai_password_2024!", "MongoDB": "mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_preprocessing?authSource=admin", "Hangfire": "Host=localhost;Port=5434;Database=data_preprocessing_hangfire;Username=lexai_user;Password=lexai_password_2024!"}, "Jwt": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "LexAI.Identity.API", "Audience": "LexAI.Identity.API", "ExpirationMinutes": 60, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ClockSkewMinutes": 5}, "OpenAI": {"ApiKey": "********************************************************************************************************************************************************************", "BaseUrl": "https://api.openai.com/v1/", "MaxRetries": 3, "TimeoutSeconds": 30}, "Storage": {"DocumentPath": "./storage/documents", "MaxFileSize": 104857600, "AllowedExtensions": [".pdf", ".docx", ".doc", ".txt", ".html", ".rtf"]}, "Processing": {"DefaultChunkSize": 1000, "DefaultOverlapSize": 100, "DefaultEmbeddingModel": "OpenAISmall", "MaxConcurrentProcessing": 5, "ProcessingTimeoutMinutes": 30, "RetryAttempts": 3}, "VectorDatabases": {"MongoDB": {"DatabaseName": "lexai_preprocessing", "DefaultCollection": "legal_documents"}, "Qdrant": {"Url": "http://localhost:6333", "ApiKey": "", "DefaultCollection": "legal-labor"}, "Weaviate": {"Url": "http://localhost:8080", "ApiKey": "", "DefaultClass": "LegalDocument"}, "Pinecone": {"ApiKey": "", "Environment": "us-west1-gcp", "DefaultIndex": "legal-documents"}}, "RateLimiting": {"PermitLimit": 100, "WindowMinutes": 1, "QueueLimit": 50}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "https://localhost:7000"]}, "HealthChecks": {"Enabled": true, "DetailedErrors": true}, "Hangfire": {"DashboardEnabled": true, "DashboardPath": "/hangfire", "WorkerCount": 2, "Queues": ["default", "processing", "vectorization"]}}