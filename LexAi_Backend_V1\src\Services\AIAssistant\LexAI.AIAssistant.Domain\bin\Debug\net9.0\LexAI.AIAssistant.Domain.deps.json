{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"LexAI.AIAssistant.Domain/1.0.0": {"dependencies": {"LexAI.Shared.Domain": "1.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"LexAI.AIAssistant.Domain.dll": {}}}, "FluentValidation/11.9.2": {"runtime": {"lib/net8.0/FluentValidation.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.9.2.0"}}}, "MediatR/12.4.1": {"dependencies": {"MediatR.Contracts": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net6.0/MediatR.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.4.1.0"}}}, "MediatR.Contracts/2.0.1": {"runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "System.Text.Json/9.0.0": {}, "LexAI.Shared.Domain/1.0.0": {"dependencies": {"FluentValidation": "11.9.2", "MediatR": "12.4.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"LexAI.Shared.Domain.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"LexAI.AIAssistant.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FluentValidation/11.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-XeHp9LvFvu1fsQ/NvDCymV02GOCB1nz7ZUhfpI3uMhCcHTkV1K5bMkv+Nc/kuNYyAsX5+5bcmUanIEMd5QN+Eg==", "path": "fluentvalidation/11.9.2", "hashPath": "fluentvalidation.11.9.2.nupkg.sha512"}, "MediatR/12.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-0tLxCgEC5+r1OCuumR3sWyiVa+BMv3AgiU4+pz8xqTc+2q1WbUEXFOr7Orm96oZ9r9FsldgUtWvB2o7b9jDOaw==", "path": "mediatr/12.4.1", "hashPath": "mediatr.12.4.1.nupkg.sha512"}, "MediatR.Contracts/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "path": "mediatr.contracts/2.0.1", "hashPath": "mediatr.contracts.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "LexAI.Shared.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}