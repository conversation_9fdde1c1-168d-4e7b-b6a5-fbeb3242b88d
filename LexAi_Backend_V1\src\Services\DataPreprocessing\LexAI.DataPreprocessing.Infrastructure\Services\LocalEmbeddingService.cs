using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace LexAI.DataPreprocessing.Infrastructure.Services;

/// <summary>
/// Service d'embedding local utilisant Sentence Transformers
/// Alternative gratuite à OpenAI pour le développement
/// </summary>
public class LocalEmbeddingService : IEmbeddingService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<LocalEmbeddingService> _logger;
    private readonly string _baseUrl;

    /// <summary>
    /// Modèles supportés par le service local
    /// </summary>
    public IEnumerable<EmbeddingModelType> SupportedModels => new[]
    {
        EmbeddingModelType.HuggingFace,
        EmbeddingModelType.CustomLegal
    };

    /// <summary>
    /// Initialise le service d'embedding local
    /// </summary>
    public LocalEmbeddingService(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<LocalEmbeddingService> logger)
    {
        _httpClient = httpClientFactory.CreateClient();
        _configuration = configuration;
        _logger = logger;
        _baseUrl = configuration["LocalEmbedding:BaseUrl"] ?? "http://localhost:8000";

        // Configuration du timeout
        _httpClient.Timeout = TimeSpan.FromMinutes(5);
    }

    /// <summary>
    /// Génère des embeddings pour une liste de textes
    /// </summary>
    public async Task<IEnumerable<EmbeddingResult>> GenerateEmbeddingsAsync(
        IEnumerable<string> textList,
        EmbeddingModelType modelType,
        CancellationToken cancellationToken = default)
    {
        var texts = textList.ToList();
        _logger.LogInformation("Generating embeddings for {Count} texts using local model", texts.Count);

        try
        {
            // Vérifier que le service local est disponible
            await CheckServiceHealthAsync(cancellationToken);

            // Préparer la requête
            var request = new
            {
                input = texts,
                model = GetModelName(modelType)
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Envoyer la requête
            var response = await _httpClient.PostAsync($"{_baseUrl}/embeddings", content, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var embeddingResponse = JsonSerializer.Deserialize<LocalEmbeddingResponse>(responseContent);

                if (embeddingResponse?.Data != null)
                {
                    var results = new List<EmbeddingResult>();

                    for (int i = 0; i < texts.Count; i++)
                    {
                        if (i < embeddingResponse.Data.Length)
                        {
                            results.Add(new EmbeddingResult
                            {
                                Text = texts[i],
                                Embedding = embeddingResponse.Data[i].Embedding,
                                Success = true,
                                TokenCount = EstimateTokenCount(texts[i]),
                                Vector = embeddingResponse.Data[i].Embedding
                            });
                        }
                        else
                        {
                            results.Add(new EmbeddingResult
                            {
                                Text = texts[i],
                                Success = false,
                                ErrorMessage = "No embedding returned for this text"
                            });
                        }
                    }

                    _logger.LogInformation("Generated {Count}/{Total} embeddings successfully",
                        results.Count(r => r.Success), texts.Count);

                    return results;
                }
            }

            _logger.LogError("Local embedding service returned error: {StatusCode}", response.StatusCode);
            return texts.Select(text => new EmbeddingResult
            {
                Text = text,
                Success = false,
                ErrorMessage = $"Service error: {response.StatusCode}"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embeddings with local service");
            return texts.Select(text => new EmbeddingResult
            {
                Text = text,
                Success = false,
                ErrorMessage = ex.Message
            });
        }
    }

    /// <summary>
    /// Obtient la dimension des embeddings pour un modèle
    /// </summary>
    public int GetEmbeddingDimension(EmbeddingModelType modelType)
    {
        return modelType switch
        {
            EmbeddingModelType.HuggingFace => 384, // all-MiniLM-L6-v2
            EmbeddingModelType.CustomLegal => 384,
            _ => 384
        };
    }

    /// <summary>
    /// Estime le coût (gratuit pour le service local)
    /// </summary>
    public decimal EstimateCost(int tokenCount, EmbeddingModelType modelType)
    {
        // Service local = gratuit
        return 0m;
    }

    /// <summary>
    /// Vérifie la santé du service local
    /// </summary>
    private async Task CheckServiceHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/health", cancellationToken);
            if (!response.IsSuccessStatusCode)
            {
                throw new InvalidOperationException($"Local embedding service is not healthy: {response.StatusCode}");
            }
        }
        catch (HttpRequestException ex)
        {
            throw new InvalidOperationException(
                "Local embedding service is not available. Please start the Python service with: python local_embedding_service.py",
                ex);
        }
    }

    /// <summary>
    /// Obtient le nom du modèle pour le service local
    /// </summary>
    private static string GetModelName(EmbeddingModelType modelType)
    {
        return modelType switch
        {
            EmbeddingModelType.HuggingFace => "all-MiniLM-L6-v2",
            EmbeddingModelType.CustomLegal => "all-MiniLM-L6-v2",
            _ => "all-MiniLM-L6-v2"
        };
    }

    /// <summary>
    /// Estime le nombre de tokens
    /// </summary>
    private static int EstimateTokenCount(string text)
    {
        return Math.Max(1, text.Length / 4);
    }

    /// <summary>
    /// Réponse du service local d'embedding
    /// </summary>
    private class LocalEmbeddingResponse
    {
        [JsonPropertyName("object")]
        public string Object { get; set; } = string.Empty;
        [JsonPropertyName("data")]
        public EmbeddingData[] Data { get; set; } = Array.Empty<EmbeddingData>();
        [JsonPropertyName("model")]
        public string Model { get; set; } = string.Empty;
        [JsonPropertyName("usage")]
        public Usage Usage { get; set; } = new();
    }

    /// <summary>
    /// Données d'embedding
    /// </summary>
    private class EmbeddingData
    {
        [JsonPropertyName("object")]
        public string Object { get; set; } = string.Empty;
        [JsonPropertyName("index")]
        public int Index { get; set; }
        [JsonPropertyName("embedding")]
        public float[] Embedding { get; set; } = Array.Empty<float>();
    }

    /// <summary>
    /// Informations d'utilisation
    /// </summary>
    private class Usage
    {
        [JsonPropertyName("prompt_tokens")]
        public int PromptTokens { get; set; }
        [JsonPropertyName("total_tokens")]
        public int TotalTokens { get; set; }
    }
}
