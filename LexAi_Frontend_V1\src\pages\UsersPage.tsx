import React, { useEffect, useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Users, 
  Search, 
  Filter, 
  UserPlus, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Eye,
  Shield,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card'
import { useUsersStore } from '../store/usersStore'
import { useAuthStore } from '../store/authStore'
import {type User,  UserRole } from '../types/index'

export function UsersPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<UserRole | 'all'>('all')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all')
  
  const { 
    users, 
    isLoading, 
    getUsers, 
    deleteUser,
    setSelectedUser 
  } = useUsersStore()

  const { user: currentUser } = useAuthStore()

  useEffect(() => {
    getUsers()
  }, [getUsers])

  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRole = roleFilter === 'all' || user.role === roleFilter
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'active' && user.isActive) ||
      (statusFilter === 'inactive' && !user.isActive)
    
    return matchesSearch && matchesRole && matchesStatus
  })

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case UserRole.Administrator:
        return <Shield className="h-4 w-4 text-red-500" />
      case UserRole.SeniorLawyer:
        return <Users className="h-4 w-4 text-purple-500" />
      case UserRole.Lawyer:
        return <Users className="h-4 w-4 text-blue-500" />
      case UserRole.LegalAssistant:
        return <Users className="h-4 w-4 text-green-500" />
      case UserRole.Client:
        return <Users className="h-4 w-4 text-gray-500" />
      default:
        return <Users className="h-4 w-4 text-gray-500" />
    }
  }

  const getRoleLabel = (role: UserRole) => {
    switch (role) {
      case UserRole.Administrator:
        return 'Administrateur'
      case UserRole.SeniorLawyer:
        return 'Avocat Senior'
      case UserRole.Lawyer:
        return 'Avocat'
      case UserRole.LegalAssistant:
        return 'Assistant Juridique'
      case UserRole.Client:
        return 'Client'
      default:
        return role
    }
  }

  const handleDeleteUser = async (id: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {
      try {
        await deleteUser(id)
      } catch (error) {
        console.error('Erreur lors de la suppression:', error)
      }
    }
  }

  // Vérifier si l'utilisateur actuel est administrateur
  const isAdmin = currentUser?.role === UserRole.Administrator

  if (!isAdmin) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <Shield className="mx-auto h-12 w-12 text-red-500" />
              <h2 className="mt-4 text-xl font-semibold text-gray-900">Accès restreint</h2>
              <p className="mt-2 text-gray-600">
                Vous devez être administrateur pour accéder à la gestion des utilisateurs.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestion des utilisateurs</h1>
          <p className="text-gray-600">Gérez les comptes utilisateurs et leurs permissions</p>
        </div>
        <Link to="/users/create">
          <Button>
            <UserPlus className="h-4 w-4 mr-2" />
            Nouvel utilisateur
          </Button>
        </Link>
      </div>

      {/* Filtres et recherche */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Rechercher par nom ou email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value as UserRole | 'all')}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tous les rôles</option>
                <option value={UserRole.Administrator}>Administrateur</option>
                <option value={UserRole.SeniorLawyer}>Avocat Senior</option>
                <option value={UserRole.Lawyer}>Avocat</option>
                <option value={UserRole.LegalAssistant}>Assistant Juridique</option>
                <option value={UserRole.Client}>Client</option>
              </select>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tous les statuts</option>
                <option value="active">Actif</option>
                <option value="inactive">Inactif</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Liste des utilisateurs */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Chargement des utilisateurs...</p>
          </div>
        </div>
      ) : filteredUsers.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                {users.length === 0 ? 'Aucun utilisateur' : 'Aucun utilisateur trouvé'}
              </h3>
              <p className="mt-2 text-gray-600">
                {users.length === 0 
                  ? 'Commencez par créer le premier utilisateur.'
                  : 'Essayez de modifier vos critères de recherche.'
                }
              </p>
              {users.length === 0 && (
                <Link to="/users/create">
                  <Button className="mt-4">
                    <UserPlus className="h-4 w-4 mr-2" />
                    Créer un utilisateur
                  </Button>
                </Link>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {filteredUsers.map((user) => (
            <Card key={user.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 flex-1">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600">
                          {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900">
                        {user.firstName} {user.lastName}
                      </h3>
                      <p className="text-sm text-gray-500">{user.email}</p>
                      
                      <div className="flex items-center space-x-4 mt-2">
                        <div className="flex items-center space-x-1">
                          {getRoleIcon(user.role)}
                          <span className="text-sm text-gray-600">
                            {getRoleLabel(user.role)}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          {user.isActive ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                          <span className="text-sm text-gray-600">
                            {user.isActive ? 'Actif' : 'Inactif'}
                          </span>
                        </div>

                        {user.isEmailVerified ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                            Email vérifié
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
                            Email non vérifié
                          </span>
                        )}

                        {user.lastLoginAt && (
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span className="text-xs text-gray-500">
                              Dernière connexion: {new Date(user.lastLoginAt).toLocaleDateString('fr-FR')}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Link to={`/users/${user.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        Voir
                      </Button>
                    </Link>
                    
                    <Link to={`/users/${user.id}/edit`}>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Modifier
                      </Button>
                    </Link>

                    {user.id !== currentUser?.id && (
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleDeleteUser(user.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Statistiques */}
      {users.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {users.length}
                </div>
                <div className="text-sm text-gray-600">Total utilisateurs</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {users.filter(u => u.isActive).length}
                </div>
                <div className="text-sm text-gray-600">Actifs</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {users.filter(u => u.isEmailVerified).length}
                </div>
                <div className="text-sm text-gray-600">Emails vérifiés</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {users.filter(u => u.role === UserRole.Administrator).length}
                </div>
                <div className="text-sm text-gray-600">Administrateurs</div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
