using LexAI.DataPreprocessing.Application.DTOs;
using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace LexAI.DataPreprocessing.Infrastructure.Agents;

/// <summary>
/// Orchestration agent that coordinates the entire document processing pipeline
/// </summary>
public class OrchestrationAgent : IOrchestrationAgent
{
    private readonly IExtractionAgent _extractionAgent;
    private readonly IClassificationAgent _classificationAgent;
    private readonly IChunkingAgent _chunkingAgent;
    private readonly IVectorizationAgent _vectorizationAgent;
    private readonly IRoutingAgent _routingAgent;
    private readonly IQualityAssuranceAgent _qualityAssuranceAgent;
    private readonly IDocumentRepository _documentRepository;
    private readonly IVectorStorageService _vectorStorageService;
    private readonly ILogger<OrchestrationAgent> _logger;

    /// <summary>
    /// Agent name
    /// </summary>
    public string AgentName => "OrchestrationAgent";

    /// <summary>
    /// Agent type
    /// </summary>
    public AgentType AgentType => AgentType.Orchestration;

    /// <summary>
    /// Initializes a new instance of the OrchestrationAgent
    /// </summary>
    /// <param name="extractionAgent">Extraction agent</param>
    /// <param name="classificationAgent">Classification agent</param>
    /// <param name="chunkingAgent">Chunking agent</param>
    /// <param name="vectorizationAgent">Vectorization agent</param>
    /// <param name="routingAgent">Routing agent</param>
    /// <param name="qualityAssuranceAgent">Quality assurance agent</param>
    /// <param name="documentRepository">Document repository</param>
    /// <param name="vectorStorageService">Vector storage service</param>
    /// <param name="logger">Logger</param>
    public OrchestrationAgent(
        IExtractionAgent extractionAgent,
        IClassificationAgent classificationAgent,
        IChunkingAgent chunkingAgent,
        IVectorizationAgent vectorizationAgent,
        IRoutingAgent routingAgent,
        IQualityAssuranceAgent qualityAssuranceAgent,
        IDocumentRepository documentRepository,
        IVectorStorageService vectorStorageService,
        ILogger<OrchestrationAgent> logger)
    {
        _extractionAgent = extractionAgent;
        _classificationAgent = classificationAgent;
        _chunkingAgent = chunkingAgent;
        _vectorizationAgent = vectorizationAgent;
        _routingAgent = routingAgent;
        _qualityAssuranceAgent = qualityAssuranceAgent;
        _documentRepository = documentRepository;
        _vectorStorageService = vectorStorageService;
        _logger = logger;
    }

    /// <summary>
    /// Orchestrates the complete document processing pipeline
    /// </summary>
    /// <param name="document">Document to process</param>
    /// <param name="configuration">Processing configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing result</returns>
    public async Task<ProcessingResultDto> ProcessDocumentAsync(
        Document document, 
        ProcessingConfigurationDto configuration, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting orchestrated processing for document {DocumentId}: {FileName}", 
            document.Id, document.FileName);

        var overallStopwatch = Stopwatch.StartNew();
        var result = new ProcessingResultDto
        {
            DocumentId = document.Id,
            Success = false,
            FinalStatus = DocumentStatus.Failed
        };

        try
        {
            // Step 1: Text Extraction
            var extractionResult = await ExecuteExtractionStep(document, cancellationToken);
            result.CompletedSteps.Add(extractionResult);

            if (!extractionResult.Success)
            {
                result.Errors.AddRange(extractionResult.Errors);
                await UpdateDocumentWithError(document, "Extraction", extractionResult.Errors.FirstOrDefault() ?? "Extraction failed");
                return result;
            }

            // Step 2: Classification
            var classificationResult = await ExecuteClassificationStep(document, cancellationToken);
            result.CompletedSteps.Add(classificationResult);

            if (!classificationResult.Success)
            {
                result.Errors.AddRange(classificationResult.Errors);
                await UpdateDocumentWithError(document, "Classification", classificationResult.Errors.FirstOrDefault() ?? "Classification failed");
                return result;
            }

            // Step 3: Chunking
            var chunkingConfig = MapToChunkingConfiguration(configuration.Chunking);
            var chunkingResult = await ExecuteChunkingStep(document, chunkingConfig, cancellationToken);
            result.CompletedSteps.Add(chunkingResult);

            if (!chunkingResult.Success)
            {
                result.Errors.AddRange(chunkingResult.Errors);
                await UpdateDocumentWithError(document, "Chunking", chunkingResult.Errors.FirstOrDefault() ?? "Chunking failed");
                return result;
            }

            result.TotalChunks = chunkingResult.Metadata.GetValueOrDefault("ChunkCount", 0) as int? ?? 0;

            // Step 4: Vectorization
            var vectorizationResult = await ExecuteVectorizationStep(document, configuration.EmbeddingModel, cancellationToken);
            result.CompletedSteps.Add(vectorizationResult);

            if (!vectorizationResult.Success)
            {
                result.Errors.AddRange(vectorizationResult.Errors);
                await UpdateDocumentWithError(document, "Vectorization", vectorizationResult.Errors.FirstOrDefault() ?? "Vectorization failed");
                return result;
            }

            result.TotalTokens = vectorizationResult.Metadata.GetValueOrDefault("TotalTokens", 0) as int? ?? 0;
            result.EstimatedCost = vectorizationResult.Metadata.GetValueOrDefault("EstimatedCost", 0m) as decimal? ?? 0m;

            // Step 5: Routing and Storage
            var routingResult = await ExecuteRoutingStep(document, configuration.TargetDatabases, cancellationToken);
            result.CompletedSteps.Add(routingResult);

            if (!routingResult.Success)
            {
                result.Errors.AddRange(routingResult.Errors);
                await UpdateDocumentWithError(document, "Routing", routingResult.Errors.FirstOrDefault() ?? "Routing failed");
                return result;
            }

            result.VectorDatabasesUsed = routingResult.Metadata.GetValueOrDefault("DatabasesUsed", new List<string>()) as List<string> ?? new List<string>();

            // Step 6: Quality Assurance (if enabled)
            if (configuration.PerformQualityAssurance)
            {
                var qaResult = await ExecuteQualityAssuranceStep(document, cancellationToken);
                result.CompletedSteps.Add(qaResult);

                if (qaResult.Success && qaResult.Metadata.TryGetValue("QualityAssessment", out var qaData) && qaData is QualityAssessmentDto qa)
                {
                    result.QualityAssessment = qa;
                    if (!qa.QualityPassed)
                    {
                        result.Warnings.Add("Document quality assessment failed");
                        result.Warnings.AddRange(qa.Recommendations);
                    }
                }
            }

            // Mark as completed
            overallStopwatch.Stop();
            result.TotalProcessingTime = overallStopwatch.Elapsed;
            result.Success = true;
            result.FinalStatus = DocumentStatus.Completed;

            // Update document status
            document.CompleteVectorization(
                result.VectorDatabasesUsed.FirstOrDefault() ?? "Unknown",
                "default",
                AgentName,
                overallStopwatch.Elapsed,
                result.EstimatedCost);

            await _documentRepository.UpdateAsync(document, cancellationToken);

            _logger.LogInformation("Document processing completed successfully for {DocumentId}. " +
                "Total time: {TotalTime}ms, Chunks: {ChunkCount}, Cost: ${Cost:F4}", 
                document.Id, overallStopwatch.ElapsedMilliseconds, result.TotalChunks, result.EstimatedCost);

            return result;
        }
        catch (Exception ex)
        {
            overallStopwatch.Stop();
            result.TotalProcessingTime = overallStopwatch.Elapsed;
            result.Errors.Add($"Processing failed: {ex.Message}");

            _logger.LogError(ex, "Error in orchestrated processing for document {DocumentId}", document.Id);

            await UpdateDocumentWithError(document, "Orchestration", ex.Message);
            return result;
        }
    }

    /// <summary>
    /// Retries failed processing steps
    /// </summary>
    /// <param name="document">Document to retry</param>
    /// <param name="fromStep">Step to retry from</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Retry result</returns>
    public async Task<ProcessingResultDto> RetryProcessingAsync(
        Document document, 
        string fromStep, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Retrying processing for document {DocumentId} from step {FromStep}", 
            document.Id, fromStep);

        try
        {
            // Create default configuration for retry
            var configuration = new ProcessingConfigurationDto
            {
                Chunking = new ChunkingConfigurationDto(),
                EmbeddingModel = EmbeddingModelType.OpenAISmall,
                TargetDatabases = new List<VectorDatabaseType> { VectorDatabaseType.MongoDB },
                PerformQualityAssurance = true,
                ExtractNamedEntities = true,
                ExtractKeywords = true,
                Priority = ProcessingPriority.Normal
            };

            return await ProcessDocumentAsync(document, configuration, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrying processing for document {DocumentId}", document.Id);
            throw;
        }
    }

    /// <summary>
    /// Gets processing status for a document
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing status</returns>
    public async Task<ProcessingStatusDto> GetProcessingStatusAsync(
        Guid documentId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var document = await _documentRepository.GetByIdAsync(documentId, cancellationToken);
            if (document == null)
            {
                throw new InvalidOperationException($"Document {documentId} not found");
            }

            var status = new ProcessingStatusDto
            {
                DocumentId = documentId,
                Status = document.Status,
                StartedAt = document.CreatedAt,
                LastUpdatedAt = document.UpdatedAt ?? document.CreatedAt
            };

            // Calculate progress based on status
            status.ProgressPercentage = document.Status switch
            {
                DocumentStatus.Uploaded => 10,
                DocumentStatus.Extracting => 20,
                DocumentStatus.Extracted => 30,
                DocumentStatus.Classifying => 40,
                DocumentStatus.Classified => 50,
                DocumentStatus.Chunking => 60,
                DocumentStatus.Chunked => 70,
                DocumentStatus.Vectorizing => 80,
                DocumentStatus.Completed => 100,
                DocumentStatus.Failed => 0,
                _ => 0
            };

            // Set current step and remaining steps
            var allSteps = new[] { "Upload", "Extraction", "Classification", "Chunking", "Vectorization", "Storage" };
            var currentStepIndex = document.Status switch
            {
                DocumentStatus.Uploaded => 0,
                DocumentStatus.Extracting => 1,
                DocumentStatus.Extracted => 1,
                DocumentStatus.Classifying => 2,
                DocumentStatus.Classified => 2,
                DocumentStatus.Chunking => 3,
                DocumentStatus.Chunked => 3,
                DocumentStatus.Vectorizing => 4,
                DocumentStatus.Completed => 5,
                _ => 0
            };

            status.CompletedSteps = allSteps.Take(currentStepIndex).ToList();
            status.CurrentStep = currentStepIndex < allSteps.Length ? allSteps[currentStepIndex] : null;
            status.RemainingSteps = allSteps.Skip(currentStepIndex + 1).ToList();

            // Set current agent based on status
            status.CurrentAgent = document.Status switch
            {
                DocumentStatus.Extracting => _extractionAgent.AgentName,
                DocumentStatus.Classifying => _classificationAgent.AgentName,
                DocumentStatus.Chunking => _chunkingAgent.AgentName,
                DocumentStatus.Vectorizing => _vectorizationAgent.AgentName,
                _ => null
            };

            // Add errors if any
            status.Errors = document.Errors.Select(e => e.ErrorMessage).ToList();

            return status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting processing status for document {DocumentId}", documentId);
            throw;
        }
    }

    /// <summary>
    /// Cancels document processing
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="reason">Cancellation reason</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cancellation result</returns>
    public async Task<bool> CancelProcessingAsync(
        Guid documentId, 
        string reason, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var document = await _documentRepository.GetByIdAsync(documentId, cancellationToken);
            if (document == null)
            {
                _logger.LogWarning("Cannot cancel processing for non-existent document {DocumentId}", documentId);
                return false;
            }

            // Check if document can be cancelled
            if (document.Status == DocumentStatus.Completed || document.Status == DocumentStatus.Failed)
            {
                _logger.LogWarning("Cannot cancel processing for document {DocumentId} with status {Status}", 
                    documentId, document.Status);
                return false;
            }

            // Mark as failed with cancellation reason
            var error = ProcessingError.Create(
                "CANCELLED",
                $"Processing cancelled: {reason}",
                ErrorSeverity.Medium,
                "Orchestration",
                AgentName);

            document.MarkAsFailed(error, AgentName);
            await _documentRepository.UpdateAsync(document, cancellationToken);

            _logger.LogInformation("Processing cancelled for document {DocumentId}. Reason: {Reason}", 
                documentId, reason);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling processing for document {DocumentId}", documentId);
            return false;
        }
    }

    private async Task<ProcessingStepResultDto> ExecuteExtractionStep(Document document, CancellationToken cancellationToken)
    {
        var stepStopwatch = Stopwatch.StartNew();
        var stepResult = new ProcessingStepResultDto
        {
            StepName = "Extraction",
            AgentName = _extractionAgent.AgentName,
            Success = false
        };

        try
        {
            document.StartExtraction(_extractionAgent.AgentName);
            await _documentRepository.UpdateAsync(document, cancellationToken);

            var extractionResult = await _extractionAgent.ExtractTextAsync(document, cancellationToken);

            if (extractionResult.Success)
            {
                document.CompleteExtraction(extractionResult.ExtractedText, _extractionAgent.AgentName, stepStopwatch.Elapsed);
                stepResult.Success = true;
                stepResult.Metadata["TextLength"] = extractionResult.ExtractedText.Length;
                stepResult.Metadata["Confidence"] = extractionResult.Confidence;
            }
            else
            {
                stepResult.Errors.AddRange(extractionResult.Errors);
            }

            await _documentRepository.UpdateAsync(document, cancellationToken);
        }
        catch (Exception ex)
        {
            stepResult.Errors.Add(ex.Message);
            _logger.LogError(ex, "Error in extraction step for document {DocumentId}", document.Id);
        }

        stepStopwatch.Stop();
        stepResult.Duration = stepStopwatch.Elapsed;
        return stepResult;
    }

    private async Task<ProcessingStepResultDto> ExecuteClassificationStep(Document document, CancellationToken cancellationToken)
    {
        var stepStopwatch = Stopwatch.StartNew();
        var stepResult = new ProcessingStepResultDto
        {
            StepName = "Classification",
            AgentName = _classificationAgent.AgentName,
            Success = false
        };

        try
        {
            document.StartClassification(_classificationAgent.AgentName);
            await _documentRepository.UpdateAsync(document, cancellationToken);

            var classificationResult = await _classificationAgent.ClassifyDocumentAsync(document, cancellationToken);

            if (classificationResult.Success)
            {
                document.CompleteClassification(
                    classificationResult.DetectedDomain, 
                    classificationResult.Confidence, 
                    _classificationAgent.AgentName, 
                    stepStopwatch.Elapsed);
                
                stepResult.Success = true;
                stepResult.Metadata["DetectedDomain"] = classificationResult.DetectedDomain.ToString();
                stepResult.Metadata["Confidence"] = classificationResult.Confidence;
                stepResult.Metadata["KeywordCount"] = classificationResult.Keywords.Count;
                stepResult.Metadata["EntityCount"] = classificationResult.NamedEntities.Count;
            }
            else
            {
                stepResult.Errors.AddRange(classificationResult.Errors);
            }

            await _documentRepository.UpdateAsync(document, cancellationToken);
        }
        catch (Exception ex)
        {
            stepResult.Errors.Add(ex.Message);
            _logger.LogError(ex, "Error in classification step for document {DocumentId}", document.Id);
        }

        stepStopwatch.Stop();
        stepResult.Duration = stepStopwatch.Elapsed;
        return stepResult;
    }

    private async Task<ProcessingStepResultDto> ExecuteChunkingStep(
        Document document, 
        ChunkingConfiguration configuration, 
        CancellationToken cancellationToken)
    {
        var stepStopwatch = Stopwatch.StartNew();
        var stepResult = new ProcessingStepResultDto
        {
            StepName = "Chunking",
            AgentName = _chunkingAgent.AgentName,
            Success = false
        };

        try
        {
            document.StartChunking(_chunkingAgent.AgentName);
            await _documentRepository.UpdateAsync(document, cancellationToken);

            var chunkingResult = await _chunkingAgent.ChunkDocumentAsync(document, configuration, cancellationToken);

            if (chunkingResult.Success)
            {
                // Add chunks to document
                foreach (var chunkDto in chunkingResult.Chunks)
                {
                    var chunk = DocumentChunk.Create(
                        document.Id,
                        chunkDto.SequenceNumber,
                        chunkDto.Content,
                        chunkDto.Type,
                        chunkDto.StartPosition,
                        chunkDto.EndPosition,
                        _chunkingAgent.AgentName);

                    document.AddChunk(chunk);
                }

                document.CompleteChunking(_chunkingAgent.AgentName, stepStopwatch.Elapsed);
                
                stepResult.Success = true;
                stepResult.Metadata["ChunkCount"] = chunkingResult.TotalChunks;
                stepResult.Metadata["AverageChunkSize"] = chunkingResult.AverageChunkSize;
                stepResult.Metadata["Strategy"] = chunkingResult.Strategy.ToString();
            }
            else
            {
                stepResult.Errors.AddRange(chunkingResult.Errors);
            }

            await _documentRepository.UpdateAsync(document, cancellationToken);
        }
        catch (Exception ex)
        {
            stepResult.Errors.Add(ex.Message);
            _logger.LogError(ex, "Error in chunking step for document {DocumentId}", document.Id);
        }

        stepStopwatch.Stop();
        stepResult.Duration = stepStopwatch.Elapsed;
        return stepResult;
    }

    private async Task<ProcessingStepResultDto> ExecuteVectorizationStep(
        Document document, 
        EmbeddingModelType modelType, 
        CancellationToken cancellationToken)
    {
        var stepStopwatch = Stopwatch.StartNew();
        var stepResult = new ProcessingStepResultDto
        {
            StepName = "Vectorization",
            AgentName = _vectorizationAgent.AgentName,
            Success = false
        };

        try
        {
            document.StartVectorization(_vectorizationAgent.AgentName);
            await _documentRepository.UpdateAsync(document, cancellationToken);

            var vectorizationResult = await _vectorizationAgent.VectorizeChunksAsync(document.Chunks, modelType, cancellationToken);

            if (vectorizationResult.Success)
            {
                // Update chunks with vector information
                foreach (var vectorizedChunk in vectorizationResult.VectorizedChunks)
                {
                    var chunk = document.Chunks.FirstOrDefault(c => c.Id == vectorizedChunk.ChunkId);
                    if (chunk != null)
                    {
                        chunk.SetEmbedding(
                            vectorizedChunk.EmbeddingVector, 
                            vectorizedChunk.Metadata.EmbeddingModel, 
                            vectorizedChunk.VectorId);
                    }
                }

                stepResult.Success = true;
                stepResult.Metadata["TotalTokens"] = vectorizationResult.TotalTokens;
                stepResult.Metadata["EstimatedCost"] = vectorizationResult.EstimatedCost;
                stepResult.Metadata["EmbeddingModel"] = vectorizationResult.EmbeddingModel.ToString();
                stepResult.Metadata["VectorDimension"] = vectorizationResult.VectorDimension;
            }
            else
            {
                stepResult.Errors.AddRange(vectorizationResult.Errors);
            }

            await _documentRepository.UpdateAsync(document, cancellationToken);
        }
        catch (Exception ex)
        {
            stepResult.Errors.Add(ex.Message);
            _logger.LogError(ex, "Error in vectorization step for document {DocumentId}", document.Id);
        }

        stepStopwatch.Stop();
        stepResult.Duration = stepStopwatch.Elapsed;
        return stepResult;
    }

    private async Task<ProcessingStepResultDto> ExecuteRoutingStep(
        Document document, 
        List<VectorDatabaseType> targetDatabases, 
        CancellationToken cancellationToken)
    {
        var stepStopwatch = Stopwatch.StartNew();
        var stepResult = new ProcessingStepResultDto
        {
            StepName = "Routing",
            AgentName = _routingAgent.AgentName,
            Success = false
        };

        try
        {
            var routingResult = await _routingAgent.RouteChunksAsync(document.Chunks, cancellationToken);

            if (routingResult.Success)
            {
                var databasesUsed = new List<string>();

                // Store vectors in target databases
                foreach (var routedChunk in routingResult.RoutedChunks)
                {
                    var chunk = document.Chunks.FirstOrDefault(c => c.Id == routedChunk.ChunkId);
                    if (chunk != null && chunk.IsVectorized)
                    {
                        await _vectorStorageService.EnsureCollectionExistsAsync(
                            routedChunk.TargetDatabase,
                            routedChunk.TargetCollection,
                            chunk.VectorDimension ?? 1536,
                            cancellationToken);

                        var storageResult = await _vectorStorageService.StoreVectorsAsync(
                            new[] { chunk },
                            routedChunk.TargetDatabase,
                            routedChunk.TargetCollection,
                            cancellationToken);

                        if (storageResult.Success)
                        {
                            var dbName = $"{routedChunk.TargetDatabase}/{routedChunk.TargetCollection}";
                            if (!databasesUsed.Contains(dbName))
                            {
                                databasesUsed.Add(dbName);
                            }
                        }
                    }
                }

                stepResult.Success = true;
                stepResult.Metadata["DatabasesUsed"] = databasesUsed;
                stepResult.Metadata["ChunksRouted"] = routingResult.RoutedChunks.Count;
            }
            else
            {
                stepResult.Errors.AddRange(routingResult.Errors);
            }
        }
        catch (Exception ex)
        {
            stepResult.Errors.Add(ex.Message);
            _logger.LogError(ex, "Error in routing step for document {DocumentId}", document.Id);
        }

        stepStopwatch.Stop();
        stepResult.Duration = stepStopwatch.Elapsed;
        return stepResult;
    }

    private async Task<ProcessingStepResultDto> ExecuteQualityAssuranceStep(Document document, CancellationToken cancellationToken)
    {
        var stepStopwatch = Stopwatch.StartNew();
        var stepResult = new ProcessingStepResultDto
        {
            StepName = "QualityAssurance",
            AgentName = _qualityAssuranceAgent.AgentName,
            Success = false
        };

        try
        {
            var qaResult = await _qualityAssuranceAgent.AssessDocumentQualityAsync(document, cancellationToken);
            
            stepResult.Success = true;
            stepResult.Metadata["QualityAssessment"] = qaResult;
            stepResult.Metadata["QualityScore"] = qaResult.OverallScore;
            stepResult.Metadata["QualityPassed"] = qaResult.QualityPassed;
        }
        catch (Exception ex)
        {
            stepResult.Errors.Add(ex.Message);
            _logger.LogError(ex, "Error in quality assurance step for document {DocumentId}", document.Id);
        }

        stepStopwatch.Stop();
        stepResult.Duration = stepStopwatch.Elapsed;
        return stepResult;
    }

    private async Task UpdateDocumentWithError(Document document, string step, string errorMessage)
    {
        try
        {
            var error = ProcessingError.Create(
                "PROCESSING_ERROR",
                errorMessage,
                ErrorSeverity.High,
                step,
                AgentName);

            document.MarkAsFailed(error, AgentName);
            await _documentRepository.UpdateAsync(document, CancellationToken.None);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document {DocumentId} with error", document.Id);
        }
    }

    private static ChunkingConfiguration MapToChunkingConfiguration(ChunkingConfigurationDto dto)
    {
        return ChunkingConfiguration.Create(
            dto.Strategy,
            dto.MaxChunkSize,
            dto.OverlapSize,
            dto.MinChunkSize,
            dto.PreserveSentences,
            dto.PreserveParagraphs);
    }
}
