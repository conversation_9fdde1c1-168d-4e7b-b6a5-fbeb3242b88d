import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from './components/layout/Layout'
import { ProtectedRoute } from './components/ProtectedRoute'
import { ThemeProvider } from './components/ThemeProvider'
import { LoginPage } from './pages/auth/LoginPage'
import { RegisterPage } from './pages/auth/RegisterPage'
import { ProfilePage } from './pages/auth/ProfilePage'
import { ChangePasswordPage } from './pages/auth/ChangePasswordPage'
import { ForgotPasswordPage } from './pages/auth/ForgotPasswordPage'
import { ResetPasswordPage } from './pages/auth/ResetPasswordPage'
import { DashboardPage } from './pages/DashboardPage'
import { SearchPage } from './pages/SearchPage'
import { AiAssistantPage } from './pages/AiAssistantPage'
import { DocumentsPage } from './pages/DocumentsPage'
import { DocumentUploadPage } from './pages/DocumentUploadPage'
import { DocumentProcessPage } from './pages/DocumentProcessPage'
import { DocumentDetailPage } from './pages/DocumentDetailPage'
import { UsersPage } from './pages/UsersPage'
import { UnauthorizedPage } from './pages/UnauthorizedPage'
import { useAuthStore } from './store/authStore'
import { UserRole } from './types/index'

import './index.css'

function App() {
  const { isAuthenticated } = useAuthStore()
const requiredRoles : UserRole[] = [UserRole.Administrator, UserRole.SeniorLawyer, UserRole.Lawyer];
  return (
    <ThemeProvider>
      <Router>
      <Routes>
        {/* Routes publiques */}
        <Route
          path="/login"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginPage />
          }
        />
        <Route
          path="/register"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <RegisterPage />
          }
        />
        <Route
          path="/forgot-password"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <ForgotPasswordPage />
          }
        />
        <Route
          path="/reset-password"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <ResetPasswordPage />
          }
        />

        {/* Routes protégées */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />

          <Route
            path="dashboard"
            element={
              <ProtectedRoute requiredRoles={requiredRoles}>
                <DashboardPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="search"
            element={
              <ProtectedRoute requiredRoles={requiredRoles}>
                <SearchPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="chat"
            element={
              <ProtectedRoute>
                <AiAssistantPage />
              </ProtectedRoute>
            }
          />

          {/* Routes Identity */}
          <Route
            path="profile"
            element={
              <ProtectedRoute>
                <ProfilePage />
              </ProtectedRoute>
            }
          />

          <Route
            path="change-password"
            element={
              <ProtectedRoute>
                <ChangePasswordPage />
              </ProtectedRoute>
            }
          />

          {/* Routes DataProcessing */}
          <Route
            path="documents"
            element={
              <ProtectedRoute>
                <DocumentsPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="documents/upload"
            element={
              <ProtectedRoute>
                <DocumentUploadPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="documents/:id"
            element={
              <ProtectedRoute>
                <DocumentDetailPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="documents/:id/process"
            element={
              <ProtectedRoute>
                <DocumentProcessPage />
              </ProtectedRoute>
            }
          />

          {/* Routes Administration */}
          <Route
            path="users"
            element={
              <ProtectedRoute requiredRoles={[UserRole.Administrator]}>
                <UsersPage />
              </ProtectedRoute>
            }
          />

          {/* Page d'erreur */}
          <Route path="unauthorized" element={<UnauthorizedPage />} />

          {/* Placeholder pour les autres pages */}
          <Route path="document-analysis" element={<div className="p-6"><h1 className="text-2xl font-bold">Analyse de Documents</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
          <Route path="document-generator" element={<div className="p-6"><h1 className="text-2xl font-bold">Générateur de Documents</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
          <Route path="clients" element={<div className="p-6"><h1 className="text-2xl font-bold">Gestion des Clients</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
          <Route path="cases" element={<div className="p-6"><h1 className="text-2xl font-bold">Gestion des Dossiers</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
          <Route path="calendar" element={<div className="p-6"><h1 className="text-2xl font-bold">Agenda</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
          <Route path="settings" element={<div className="p-6"><h1 className="text-2xl font-bold">Paramètres</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
        </Route>

        {/* Page d'erreur pour les routes non autorisées */}
        <Route
          path="/unauthorized"
          element={
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">403</h1>
                <p className="text-xl text-gray-600 mb-8">Accès non autorisé</p>
                <p className="text-gray-500">Vous n'avez pas les permissions nécessaires pour accéder à cette page.</p>
              </div>
            </div>
          }
        />

        {/* Page 404 */}
        <Route
          path="*"
          element={
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                <p className="text-xl text-gray-600 mb-8">Page non trouvée</p>
                <p className="text-gray-500">La page que vous recherchez n'existe pas.</p>
              </div>
            </div>
          }
        />
      </Routes>
    </Router>
    </ThemeProvider>
  )
}

export default App
