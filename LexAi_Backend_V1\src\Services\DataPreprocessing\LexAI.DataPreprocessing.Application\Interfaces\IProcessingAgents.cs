using LexAI.DataPreprocessing.Application.DTOs;
using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;

namespace LexAI.DataPreprocessing.Application.Interfaces;

/// <summary>
/// Base interface for all processing agents
/// </summary>
public interface IAgent
{
    /// <summary>
    /// Agent name
    /// </summary>
    string AgentName { get; }

    /// <summary>
    /// Agent type
    /// </summary>
    AgentType AgentType { get; }
}

/// <summary>
/// Interface for text extraction agent
/// </summary>
public interface IExtractionAgent
{
    /// <summary>
    /// Agent name
    /// </summary>
    string AgentName { get; }

    /// <summary>
    /// Agent type
    /// </summary>
    AgentType AgentType { get; }

    /// <summary>
    /// Supported MIME types
    /// </summary>
    IEnumerable<string> SupportedMimeTypes { get; }

    /// <summary>
    /// Extracts text from a document
    /// </summary>
    /// <param name="document">Document to extract text from</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extraction result</returns>
    Task<ExtractionResultDto> ExtractTextAsync(Document document, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if the agent can handle the document
    /// </summary>
    /// <param name="mimeType">Document MIME type</param>
    /// <returns>True if agent can handle the document</returns>
    bool CanHandle(string mimeType);

    /// <summary>
    /// Gets extraction confidence for a document
    /// </summary>
    /// <param name="document">Document to evaluate</param>
    /// <returns>Confidence score (0-1)</returns>
    Task<double> GetExtractionConfidenceAsync(Document document);
}

/// <summary>
/// Interface for document classification agent
/// </summary>
public interface IClassificationAgent
{
    /// <summary>
    /// Agent name
    /// </summary>
    string AgentName { get; }

    /// <summary>
    /// Agent type
    /// </summary>
    AgentType AgentType { get; }

    /// <summary>
    /// Classifies a document into legal domains
    /// </summary>
    /// <param name="document">Document to classify</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Classification result</returns>
    Task<ClassificationResultDto> ClassifyDocumentAsync(Document document, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets classification confidence for a document
    /// </summary>
    /// <param name="text">Document text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Domain confidence scores</returns>
    Task<Dictionary<LegalDomain, double>> GetDomainConfidenceAsync(string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// Extracts keywords from document text
    /// </summary>
    /// <param name="text">Document text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extracted keywords</returns>
    Task<IEnumerable<string>> ExtractKeywordsAsync(string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// Extracts named entities from document text
    /// </summary>
    /// <param name="text">Document text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Named entities</returns>
    Task<IEnumerable<Domain.ValueObjects.NamedEntity>> ExtractNamedEntitiesAsync(string text, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for document chunking agent
/// </summary>
public interface IChunkingAgent
{
    /// <summary>
    /// Agent name
    /// </summary>
    string AgentName { get; }

    /// <summary>
    /// Agent type
    /// </summary>
    AgentType AgentType { get; }

    /// <summary>
    /// Supported chunking strategies
    /// </summary>
    IEnumerable<ChunkingStrategy> SupportedStrategies { get; }

    /// <summary>
    /// Chunks a document into smaller pieces
    /// </summary>
    /// <param name="document">Document to chunk</param>
    /// <param name="configuration">Chunking configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Chunking result</returns>
    Task<ChunkingResultDto> ChunkDocumentAsync(
        Document document,
        ChunkingConfiguration configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Estimates the number of chunks for a document
    /// </summary>
    /// <param name="text">Document text</param>
    /// <param name="configuration">Chunking configuration</param>
    /// <returns>Estimated chunk count</returns>
    int EstimateChunkCount(string text, ChunkingConfiguration configuration);

    /// <summary>
    /// Validates chunking configuration
    /// </summary>
    /// <param name="configuration">Configuration to validate</param>
    /// <returns>Validation result</returns>
    ValidationResult ValidateConfiguration(ChunkingConfiguration configuration);
}

/// <summary>
/// Interface for vectorization agent
/// </summary>
public interface IVectorizationAgent : IAgent
{

    /// <summary>
    /// Supported embedding models
    /// </summary>
    IEnumerable<EmbeddingModelType> SupportedModels { get; }

    /// <summary>
    /// Vectorizes document chunks
    /// </summary>
    /// <param name="chunks">Chunks to vectorize</param>
    /// <param name="modelType">Embedding model to use</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Vectorization result</returns>
    Task<VectorizationResultDto> VectorizeChunksAsync(
        IEnumerable<DocumentChunk> chunks,
        EmbeddingModelType modelType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates embedding for a single text
    /// </summary>
    /// <param name="text">Text to embed</param>
    /// <param name="modelType">Embedding model</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Embedding vector</returns>
    Task<float[]> GenerateEmbeddingAsync(
        string text,
        EmbeddingModelType modelType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the dimension of the embedding model
    /// </summary>
    /// <param name="modelType">Embedding model</param>
    /// <returns>Vector dimension</returns>
    int GetEmbeddingDimension(EmbeddingModelType modelType);

    /// <summary>
    /// Estimates the cost of vectorization
    /// </summary>
    /// <param name="tokenCount">Number of tokens</param>
    /// <param name="modelType">Embedding model</param>
    /// <returns>Estimated cost</returns>
    decimal EstimateVectorizationCost(int tokenCount, EmbeddingModelType modelType);
}

/// <summary>
/// Interface for routing agent
/// </summary>
public interface IRoutingAgent : IAgent
{

    /// <summary>
    /// Supported vector databases
    /// </summary>
    IEnumerable<VectorDatabaseType> SupportedDatabases { get; }

    /// <summary>
    /// Routes chunks to appropriate vector databases
    /// </summary>
    /// <param name="chunks">Chunks to route</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Routing result</returns>
    Task<RoutingResultDto> RouteChunksAsync(
        IEnumerable<DocumentChunk> chunks,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Determines the best vector database for a domain
    /// </summary>
    /// <param name="domain">Legal domain</param>
    /// <returns>Recommended vector database</returns>
    VectorDatabaseType GetRecommendedDatabase(LegalDomain domain);

    /// <summary>
    /// Gets the collection name for a domain
    /// </summary>
    /// <param name="domain">Legal domain</param>
    /// <param name="databaseType">Database type</param>
    /// <returns>Collection name</returns>
    string GetCollectionName(LegalDomain domain, VectorDatabaseType databaseType);
}

/// <summary>
/// Interface for quality assurance agent
/// </summary>
public interface IQualityAssuranceAgent : IAgent
{

    /// <summary>
    /// Validates document quality
    /// </summary>
    /// <param name="document">Document to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Quality assessment</returns>
    Task<QualityAssessmentDto> AssessDocumentQualityAsync(
        Document document,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates chunk quality
    /// </summary>
    /// <param name="chunk">Chunk to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Quality assessment</returns>
    Task<QualityAssessmentDto> AssessChunkQualityAsync(
        DocumentChunk chunk,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates processing pipeline results
    /// </summary>
    /// <param name="document">Processed document</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Pipeline validation result</returns>
    Task<PipelineValidationDto> ValidatePipelineResultsAsync(
        Document document,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Suggests improvements for document processing
    /// </summary>
    /// <param name="document">Document to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Improvement suggestions</returns>
    Task<IEnumerable<ImprovementSuggestionDto>> SuggestImprovementsAsync(
        Document document,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for orchestration agent
/// </summary>
public interface IOrchestrationAgent : IAgent
{

    /// <summary>
    /// Orchestrates the complete document processing pipeline
    /// </summary>
    /// <param name="document">Document to process</param>
    /// <param name="configuration">Processing configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing result</returns>
    Task<ProcessingResultDto> ProcessDocumentAsync(
        Document document,
        ProcessingConfigurationDto configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Retries failed processing steps
    /// </summary>
    /// <param name="document">Document to retry</param>
    /// <param name="fromStep">Step to retry from</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Retry result</returns>
    Task<ProcessingResultDto> RetryProcessingAsync(
        Document document,
        string fromStep,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets processing status for a document
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing status</returns>
    Task<ProcessingStatusDto> GetProcessingStatusAsync(
        Guid documentId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancels document processing
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="reason">Cancellation reason</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cancellation result</returns>
    Task<bool> CancelProcessingAsync(
        Guid documentId,
        string reason,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Validation result
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// Whether validation passed
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Validation errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Validation warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}
