2025-06-04 00:53:40.534 +04:00 [FTL] LexAI AI Assistant Service failed to start
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.AIAssistant.Application.Commands.SendMessageCommand,LexAI.AIAssistant.Application.DTOs.ChatResponseDto] Lifetime: Transient ImplementationType: LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler': Unable to resolve service for type 'LexAI.AIAssistant.Application.Interfaces.IMessageProcessingService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.) (Error while validating the service descriptor 'ServiceType: LexAI.AIAssistant.Application.Interfaces.IAIAssistantService Lifetime: Scoped ImplementationType: LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService': Unable to resolve service for type 'LexA<PERSON>.AIAssistant.Application.Interfaces.IMessageProcessingService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: MediatR.IRequestHandler`2[LexAI.AIAssistant.Application.Commands.SendMessageCommand,LexAI.AIAssistant.Application.DTOs.ChatResponseDto] Lifetime: Transient ImplementationType: LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler': Unable to resolve service for type 'LexAI.AIAssistant.Application.Interfaces.IMessageProcessingService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'LexAI.AIAssistant.Application.Interfaces.IMessageProcessingService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Program.cs:line 225
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: LexAI.AIAssistant.Application.Interfaces.IAIAssistantService Lifetime: Scoped ImplementationType: LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService': Unable to resolve service for type 'LexAI.AIAssistant.Application.Interfaces.IMessageProcessingService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'LexAI.AIAssistant.Application.Interfaces.IMessageProcessingService' while attempting to activate 'LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

