2025-06-01 02:14:55.091 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 02:14:55.135 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 02:14:55.144 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 02:14:55.666 +04:00 [INF] Executed DbCommand (90ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 02:14:55.683 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 02:14:55.740 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 02:15:12.801 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 02:15:12.802 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 02:15:12.856 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 02:15:12.861 +04:00 [INF] Hosting environment: Development
2025-06-01 02:15:12.863 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 02:15:14.398 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 02:15:14.639 +04:00 [INF] Request GET / started with correlation ID 7b777b6b-04d3-4213-85ac-6d0d34a84920
2025-06-01 02:15:14.719 +04:00 [INF] Request GET / completed in 69ms with status 404 (Correlation ID: 7b777b6b-04d3-4213-85ac-6d0d34a84920)
2025-06-01 02:15:14.731 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 349.3473ms
2025-06-01 02:15:14.740 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 02:16:47.791 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-06-01 02:16:47.852 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 119.0071ms
2025-06-01 02:16:47.893 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 02:16:47.893 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-06-01 02:16:47.910 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-06-01 02:16:47.912 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 19.6492ms
2025-06-01 02:16:47.996 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 103.7549ms
2025-06-01 02:16:48.033 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 123.1665ms
2025-06-01 02:16:48.084 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-06-01 02:16:48.115 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 31.9124ms
2025-06-01 02:19:54.214 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/register - null null
2025-06-01 02:19:54.252 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID 777a0f84-3c1e-4df2-94b4-df1bc573798a
2025-06-01 02:19:54.255 +04:00 [INF] CORS policy execution failed.
2025-06-01 02:19:54.257 +04:00 [INF] Request origin http://localhost:5173 does not have permission to access the resource.
2025-06-01 02:19:54.259 +04:00 [INF] Request OPTIONS /api/auth/register completed in 5ms with status 204 (Correlation ID: 777a0f84-3c1e-4df2-94b4-df1bc573798a)
2025-06-01 02:19:54.261 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/register - 204 null null 47.5893ms
2025-06-01 02:21:30.221 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 02:21:30.251 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 02:21:30.257 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 02:21:30.604 +04:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 02:21:30.615 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 02:21:30.640 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 02:21:30.838 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 02:21:30.840 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 02:21:30.881 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 02:21:30.883 +04:00 [INF] Hosting environment: Development
2025-06-01 02:21:30.884 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 02:21:31.972 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 02:21:32.145 +04:00 [INF] Request GET / started with correlation ID b7cbefce-3a06-447c-8499-8f4063adc2b9
2025-06-01 02:21:32.299 +04:00 [INF] Request GET / completed in 150ms with status 404 (Correlation ID: b7cbefce-3a06-447c-8499-8f4063adc2b9)
2025-06-01 02:21:32.310 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 347.3687ms
2025-06-01 02:21:32.325 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 02:21:37.768 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.html - null null
2025-06-01 02:21:37.895 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.html - 200 null text/html;charset=utf-8 127.2016ms
2025-06-01 02:21:37.966 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/index.js - null null
2025-06-01 02:21:37.979 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/index.js - 200 null application/javascript;charset=utf-8 13.7896ms
2025-06-01 02:21:37.985 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - null null
2025-06-01 02:21:37.994 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/_vs/browserLink - null null
2025-06-01 02:21:38.011 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_framework/aspnetcore-browser-refresh.js - 200 16521 application/javascript; charset=utf-8 26.4378ms
2025-06-01 02:21:38.047 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/_vs/browserLink - 200 null text/javascript; charset=UTF-8 52.4337ms
2025-06-01 02:21:38.303 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - null null
2025-06-01 02:21:38.332 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 28.5073ms
2025-06-01 02:21:52.464 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/register - null null
2025-06-01 02:21:52.471 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID fec4ed4a-f6de-4d4c-af00-c019dae7c2b7
2025-06-01 02:21:52.475 +04:00 [INF] CORS policy execution successful.
2025-06-01 02:21:52.480 +04:00 [INF] Request OPTIONS /api/auth/register completed in 6ms with status 204 (Correlation ID: fec4ed4a-f6de-4d4c-af00-c019dae7c2b7)
2025-06-01 02:21:52.483 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/register - 204 null null 19.3571ms
2025-06-01 02:21:52.487 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/register - application/json 273
2025-06-01 02:21:52.502 +04:00 [INF] Request POST /api/auth/register started with correlation ID da8bfe9d-2973-4d6d-8745-39e147388ef2
2025-06-01 02:21:52.508 +04:00 [INF] CORS policy execution successful.
2025-06-01 02:21:52.518 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 02:21:52.567 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 02:21:52.649 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-01 02:21:52.684 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 106.359ms
2025-06-01 02:21:52.688 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 02:21:52.690 +04:00 [INF] Request POST /api/auth/register completed in 183ms with status 400 (Correlation ID: da8bfe9d-2973-4d6d-8745-39e147388ef2)
2025-06-01 02:21:52.696 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/register - 400 null application/json; charset=utf-8 208.8786ms
2025-06-01 02:39:13.540 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/register - null null
2025-06-01 02:39:13.556 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID 5d1c190f-63dc-4cef-bbef-544274dbe648
2025-06-01 02:39:13.559 +04:00 [INF] CORS policy execution successful.
2025-06-01 02:39:13.563 +04:00 [INF] Request OPTIONS /api/auth/register completed in 5ms with status 204 (Correlation ID: 5d1c190f-63dc-4cef-bbef-544274dbe648)
2025-06-01 02:39:13.569 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/register - 204 null null 28.7223ms
2025-06-01 02:39:13.571 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/register - application/json 273
2025-06-01 02:39:13.582 +04:00 [INF] Request POST /api/auth/register started with correlation ID fe8c7b64-2ea2-4da6-b3e4-275ce28b98d6
2025-06-01 02:39:13.586 +04:00 [INF] CORS policy execution successful.
2025-06-01 02:39:13.591 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 02:39:13.596 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 02:39:13.601 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-01 02:39:13.604 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 5.239ms
2025-06-01 02:39:13.606 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 02:39:13.607 +04:00 [INF] Request POST /api/auth/register completed in 21ms with status 400 (Correlation ID: fe8c7b64-2ea2-4da6-b3e4-275ce28b98d6)
2025-06-01 02:39:13.612 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/register - 400 null application/json; charset=utf-8 40.9627ms
2025-06-01 03:04:32.586 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/register - null null
2025-06-01 03:04:32.598 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID d4458d77-5353-42e2-8468-955da16955ad
2025-06-01 03:04:32.604 +04:00 [INF] CORS policy execution successful.
2025-06-01 03:04:32.609 +04:00 [INF] Request OPTIONS /api/auth/register completed in 4ms with status 204 (Correlation ID: d4458d77-5353-42e2-8468-955da16955ad)
2025-06-01 03:04:32.616 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/register - 204 null null 30.0925ms
2025-06-01 03:04:32.621 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/register - application/json 256
2025-06-01 03:04:32.636 +04:00 [INF] Request POST /api/auth/register started with correlation ID 6e7b001c-dfe7-4a77-9b13-b93fc0b4bf73
2025-06-01 03:04:32.639 +04:00 [INF] CORS policy execution successful.
2025-06-01 03:04:32.641 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 03:04:32.643 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 03:04:32.677 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-01 03:04:32.698 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-01 03:04:33.737 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 03:04:33.976 +04:00 [INF] Executed DbCommand (63ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 03:04:35.236 +04:00 [INF] Executed DbCommand (65ms) [Parameters=[@p0='7015ff85-150a-495a-8fa8-e944fb620d1c', @p1='2025-05-31T23:04:34.9721471Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Jules' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$lFVKSXNS2TX6WtLvvlQG8uS3nAsIP8wGgDPmyzF98gVXea95BzZZK' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-05-31T23:04:34.5645432Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+334', @p24='+3346457776', @p25='55c0c064-249f-43ed-b701-3be5a0233dc3', @p26='ProfileUpdated' (Nullable = false), @p27='null' (DbType = Object), @p28='2025-05-31T23:04:34.9722885Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='7015ff85-150a-495a-8fa8-e944fb620d1c', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-05-31T23:04:34.5636051Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='60d8a7b2-23ca-439d-b178-3c53d561f08f', @p42='PasswordChanged' (Nullable = false), @p43='null' (DbType = Object), @p44='2025-05-31T23:04:34.9722880Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='7015ff85-150a-495a-8fa8-e944fb620d1c', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-05-31T23:04:34.5532554Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='6ff7176e-b820-4890-883f-eef338134464', @p58='Created' (Nullable = false), @p59='"User created with role SeniorLawyer"' (DbType = Object), @p60='2025-05-31T23:04:34.9722869Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='7015ff85-150a-495a-8fa8-e944fb620d1c', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-05-31T23:04:34.0267557Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='7c973238-673d-4e92-aaf0-d869c022d8f5', @p74='PreferencesUpdated' (Nullable = false), @p75='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p76='2025-05-31T23:04:34.9722890Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='7015ff85-150a-495a-8fa8-e944fb620d1c', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-05-31T23:04:34.5645499Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING xmin;
2025-06-01 03:04:35.313 +04:00 [INF] User added successfully: "7015ff85-150a-495a-8fa8-e944fb620d1c"
2025-06-01 03:04:35.318 +04:00 [INF] User "7015ff85-150a-495a-8fa8-e944fb620d1c" registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 03:04:35.324 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 03:04:35.334 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-06-01 03:04:35.415 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 2768.0375ms
2025-06-01 03:04:35.420 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 03:04:35.422 +04:00 [INF] Request POST /api/auth/register completed in 2782ms with status 201 (Correlation ID: 6e7b001c-dfe7-4a77-9b13-b93fc0b4bf73)
2025-06-01 03:04:35.433 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/register - 201 null application/json; charset=utf-8 2811.5213ms
2025-06-01 03:06:21.726 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 03:06:21.750 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 17bd6287-e59e-4cd5-91cf-043cde4aa2c9
2025-06-01 03:06:21.755 +04:00 [INF] CORS policy execution successful.
2025-06-01 03:06:21.758 +04:00 [INF] Request OPTIONS /api/auth/login completed in 3ms with status 204 (Correlation ID: 17bd6287-e59e-4cd5-91cf-043cde4aa2c9)
2025-06-01 03:06:21.764 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 38.2142ms
2025-06-01 03:06:21.767 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 03:06:21.780 +04:00 [INF] Request POST /api/auth/login started with correlation ID d26cb5bf-09cd-4d50-a621-9e84d8de5774
2025-06-01 03:06:21.783 +04:00 [INF] CORS policy execution successful.
2025-06-01 03:06:21.786 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 03:06:21.805 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 03:06:21.821 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 03:06:21.847 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 03:06:21.962 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 03:06:29.656 +04:00 [INF] Executed DbCommand (22ms) [Parameters=[@p15='936fed9d-c169-40ff-a733-dd16133a7461', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-05-31T23:06:22.4745124Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='7015ff85-150a-495a-8fa8-e944fb620d1c', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-05-31T23:06:22.4745151Z' (DbType = DateTime), @p11='2025-05-31T23:06:29.6295351Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='7015ff85-150a-495a-8fa8-e944fb620d1c' (Nullable = false), @p16='0' (DbType = Object), @p38='7015ff85-150a-495a-8fa8-e944fb620d1c', @p17='2025-05-31T23:04:34.9721470Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-05-31T23:06:22.4741719Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Jules' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$lFVKSXNS2TX6WtLvvlQG8uS3nAsIP8wGgDPmyzF98gVXea95BzZZK' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-05-31T23:06:29.6295329Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='793' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND xmin = @p16
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND xmin = @p39
RETURNING xmin;
2025-06-01 03:06:29.933 +04:00 [ERR] Error during login for user "7015ff85-150a-495a-8fa8-e944fb620d1c"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-06-01 03:06:30.100 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-01 03:06:30.152 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 03:06:30.156 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 8343.8543ms
2025-06-01 03:06:30.158 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 03:06:30.163 +04:00 [INF] Request POST /api/auth/login completed in 8380ms with status 401 (Correlation ID: d26cb5bf-09cd-4d50-a621-9e84d8de5774)
2025-06-01 03:06:30.168 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 8400.9108ms
2025-06-01 03:08:04.093 +04:00 [INF] Application is shutting down...
2025-06-01 03:29:16.920 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 03:29:16.970 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 03:29:16.977 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 03:29:17.496 +04:00 [INF] Executed DbCommand (61ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 03:29:17.510 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 03:29:17.553 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 03:29:17.928 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 03:29:17.930 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 03:29:17.994 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 03:29:17.997 +04:00 [INF] Hosting environment: Development
2025-06-01 03:29:18.000 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 03:29:19.347 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 03:29:19.638 +04:00 [INF] Request GET / started with correlation ID 5521f406-f05e-4da5-a001-ee32c74a8882
2025-06-01 03:29:19.768 +04:00 [INF] Request GET / completed in 124ms with status 404 (Correlation ID: 5521f406-f05e-4da5-a001-ee32c74a8882)
2025-06-01 03:29:19.786 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 473.8486ms
2025-06-01 03:29:19.807 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 03:29:26.741 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 03:29:26.764 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 4cad792d-e103-4ebc-b8e8-75d1b8f98be9
2025-06-01 03:29:26.773 +04:00 [INF] CORS policy execution successful.
2025-06-01 03:29:26.781 +04:00 [INF] Request OPTIONS /api/auth/login completed in 12ms with status 204 (Correlation ID: 4cad792d-e103-4ebc-b8e8-75d1b8f98be9)
2025-06-01 03:29:26.791 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 50.8899ms
2025-06-01 03:29:26.796 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 03:29:26.808 +04:00 [INF] Request POST /api/auth/login started with correlation ID f2bbc4f0-df5d-4636-a651-9b3f49c4ed83
2025-06-01 03:29:26.810 +04:00 [INF] CORS policy execution successful.
2025-06-01 03:29:26.816 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 03:29:26.848 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 03:29:26.908 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 03:29:26.928 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 03:29:27.589 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 03:29:27.733 +04:00 [INF] Executed DbCommand (28ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 03:35:24.869 +04:00 [INF] Executed DbCommand (30ms) [Parameters=[@p15='c105e92c-f661-4a86-ae34-cfa2295ff002', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-05-31T23:31:27.8896540Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='7015ff85-150a-495a-8fa8-e944fb620d1c', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-05-31T23:31:27.8898807Z' (DbType = DateTime), @p11='2025-05-31T23:35:16.9999323Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='7015ff85-150a-495a-8fa8-e944fb620d1c' (Nullable = false), @p16='0' (DbType = Object), @p38='7015ff85-150a-495a-8fa8-e944fb620d1c', @p17='2025-05-31T23:04:34.9721470Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-05-31T23:31:04.3607108Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Jules' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$lFVKSXNS2TX6WtLvvlQG8uS3nAsIP8wGgDPmyzF98gVXea95BzZZK' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-05-31T23:34:49.7201403Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='793' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND xmin = @p16
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND xmin = @p39
RETURNING xmin;
2025-06-01 03:35:55.524 +04:00 [ERR] Error during login for user "7015ff85-150a-495a-8fa8-e944fb620d1c"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-06-01 03:35:55.702 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-01 03:35:55.717 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 03:35:55.752 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 388895.8301ms
2025-06-01 03:35:55.760 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 03:35:55.767 +04:00 [INF] Request POST /api/auth/login completed in 388957ms with status 401 (Correlation ID: f2bbc4f0-df5d-4636-a651-9b3f49c4ed83)
2025-06-01 03:35:55.779 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 388983.305ms
2025-06-01 03:52:35.680 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 03:52:35.718 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 03:52:35.725 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 03:52:36.212 +04:00 [INF] Executed DbCommand (86ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 03:52:36.236 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 03:52:36.295 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 03:52:36.603 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 03:52:36.606 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 03:52:36.646 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 03:52:36.649 +04:00 [INF] Hosting environment: Development
2025-06-01 03:52:36.651 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 03:52:38.013 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 03:52:38.156 +04:00 [INF] Request GET / started with correlation ID 292d9c1d-ea83-4157-aefc-b8c4e2135be5
2025-06-01 03:52:38.264 +04:00 [INF] Request GET / completed in 103ms with status 404 (Correlation ID: 292d9c1d-ea83-4157-aefc-b8c4e2135be5)
2025-06-01 03:52:38.284 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 278.6856ms
2025-06-01 03:52:38.328 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 03:53:46.958 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/register - null null
2025-06-01 03:53:46.991 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID 7261eb94-3773-452f-ad54-5d6c3357d9a2
2025-06-01 03:53:47.002 +04:00 [INF] CORS policy execution successful.
2025-06-01 03:53:47.011 +04:00 [INF] Request OPTIONS /api/auth/register completed in 13ms with status 204 (Correlation ID: 7261eb94-3773-452f-ad54-5d6c3357d9a2)
2025-06-01 03:53:47.017 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/register - 204 null null 59.8016ms
2025-06-01 03:53:47.020 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/register - application/json 260
2025-06-01 03:53:47.033 +04:00 [INF] Request POST /api/auth/register started with correlation ID c2d8988b-57f9-4f65-ab7a-622d4085832d
2025-06-01 03:53:47.038 +04:00 [INF] CORS policy execution successful.
2025-06-01 03:53:47.045 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 03:53:47.109 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 03:53:47.224 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-01 03:53:47.242 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-01 03:53:48.249 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 03:53:48.472 +04:00 [INF] Executed DbCommand (32ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 03:54:26.440 +04:00 [INF] Executed DbCommand (26ms) [Parameters=[@p0='83905163-5bf9-47ed-b06b-a3e6aca4320b', @p1='2025-05-31T23:54:26.3365121Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Jules' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$yBoGnEvkmQLMjon3eSQxcuYxww.dNCAu6QezuvfukU7pFRqJBncNa' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-05-31T23:53:49.0621169Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+230', @p24='+23054878091', @p25='5baf253e-fe69-4689-9a9f-309307630311', @p26='Created' (Nullable = false), @p27='"User created with role SeniorLawyer"' (DbType = Object), @p28='2025-05-31T23:54:26.3373000Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='83905163-5bf9-47ed-b06b-a3e6aca4320b', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-05-31T23:53:48.5337761Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='0', @p42='7f53f06c-08ec-424f-86a6-5ae210cd056d', @p43='ProfileUpdated' (Nullable = false), @p44='null' (DbType = Object), @p45='2025-05-31T23:54:26.3373021Z' (DbType = DateTime), @p46=NULL, @p47=NULL (DbType = DateTime), @p48=NULL, @p49='83905163-5bf9-47ed-b06b-a3e6aca4320b', @p50='User' (Nullable = false), @p51=NULL, @p52='False', @p53='2025-05-31T23:53:49.0613837Z' (DbType = DateTime), @p54=NULL (DbType = DateTime), @p55=NULL, @p56=NULL, @p57='system' (Nullable = false), @p58='0', @p59='d6de561e-2ba0-451d-9066-f3bc12cc1607', @p60='PreferencesUpdated' (Nullable = false), @p61='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p62='2025-05-31T23:54:26.3373047Z' (DbType = DateTime), @p63=NULL, @p64=NULL (DbType = DateTime), @p65=NULL, @p66='83905163-5bf9-47ed-b06b-a3e6aca4320b', @p67='User' (Nullable = false), @p68=NULL, @p69='False', @p70='2025-05-31T23:53:49.0621225Z' (DbType = DateTime), @p71=NULL (DbType = DateTime), @p72=NULL, @p73=NULL, @p74='system' (Nullable = false), @p75='0', @p76='ef771a0c-6bd4-4b32-815c-30e3f095c202', @p77='PasswordChanged' (Nullable = false), @p78='null' (DbType = Object), @p79='2025-05-31T23:54:26.3373011Z' (DbType = DateTime), @p80=NULL, @p81=NULL (DbType = DateTime), @p82=NULL, @p83='83905163-5bf9-47ed-b06b-a3e6aca4320b', @p84='User' (Nullable = false), @p85=NULL, @p86='False', @p87='2025-05-31T23:53:49.0565736Z' (DbType = DateTime), @p88=NULL (DbType = DateTime), @p89=NULL, @p90=NULL, @p91='system' (Nullable = false), @p92='0'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", xmin)
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40, @p41);
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", xmin)
VALUES (@p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56, @p57, @p58);
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", xmin)
VALUES (@p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72, @p73, @p74, @p75);
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", xmin)
VALUES (@p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88, @p89, @p90, @p91, @p92);
2025-06-01 03:54:26.539 +04:00 [ERR] An exception occurred in the database while saving changes for context type 'LexAI.Identity.Infrastructure.Data.IdentityDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 42703: column "xmin" of relation "AuditEntries" does not exist

POSITION: 222
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column "xmin" of relation "AuditEntries" does not exist
    Position: 222
    File: parse_target.c
    Line: 1066
    Routine: checkInsertTargets
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 42703: column "xmin" of relation "AuditEntries" does not exist

POSITION: 222
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column "xmin" of relation "AuditEntries" does not exist
    Position: 222
    File: parse_target.c
    Line: 1066
    Routine: checkInsertTargets
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-06-01 03:54:26.759 +04:00 [ERR] Error registering user <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 42703: column "xmin" of relation "AuditEntries" does not exist

POSITION: 222
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column "xmin" of relation "AuditEntries" does not exist
    Position: 222
    File: parse_target.c
    Line: 1066
    Routine: checkInsertTargets
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.AddAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 175
   at LexAI.Identity.Application.Commands.RegisterUserCommandHandler.Handle(RegisterUserCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\RegisterUserCommand.cs:line 176
2025-06-01 03:54:26.854 +04:00 [ERR] Registration failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 42703: column "xmin" of relation "AuditEntries" does not exist

POSITION: 222
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column "xmin" of relation "AuditEntries" does not exist
    Position: 222
    File: parse_target.c
    Line: 1066
    Routine: checkInsertTargets
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 295
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.AddAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 175
   at LexAI.Identity.Application.Commands.RegisterUserCommandHandler.Handle(RegisterUserCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\RegisterUserCommand.cs:line 176
   at LexAI.Identity.API.Controllers.AuthController.Register(RegisterUserDto registerDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 325
2025-06-01 03:54:26.873 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 03:54:26.887 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 39767.8568ms
2025-06-01 03:54:26.889 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 03:54:26.891 +04:00 [INF] Request POST /api/auth/register completed in 39854ms with status 400 (Correlation ID: c2d8988b-57f9-4f65-ab7a-622d4085832d)
2025-06-01 03:54:26.897 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/register - 400 null application/json; charset=utf-8 39877.2523ms
2025-06-01 04:03:27.242 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 04:03:27.293 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 04:03:27.305 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 04:03:27.861 +04:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 04:03:27.888 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 04:03:27.929 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 04:03:28.270 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 04:03:28.274 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 04:03:28.321 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 04:03:28.322 +04:00 [INF] Hosting environment: Development
2025-06-01 04:03:28.324 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 04:03:29.763 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 04:03:29.961 +04:00 [INF] Request GET / started with correlation ID a49bcadb-ad16-4d5c-a347-bb995b38bd28
2025-06-01 04:03:30.052 +04:00 [INF] Request GET / completed in 84ms with status 404 (Correlation ID: a49bcadb-ad16-4d5c-a347-bb995b38bd28)
2025-06-01 04:03:30.063 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 312.6246ms
2025-06-01 04:03:30.085 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 04:03:54.917 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/register - null null
2025-06-01 04:03:54.951 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID 5e45309b-d957-4987-89fa-ab38f0852849
2025-06-01 04:03:54.957 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:03:54.960 +04:00 [INF] Request OPTIONS /api/auth/register completed in 4ms with status 204 (Correlation ID: 5e45309b-d957-4987-89fa-ab38f0852849)
2025-06-01 04:03:54.964 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/register - 204 null null 47.5177ms
2025-06-01 04:03:54.972 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/register - application/json 260
2025-06-01 04:03:54.979 +04:00 [INF] Request POST /api/auth/register started with correlation ID 89471e8a-f2ed-4a7f-acf6-714631d83f54
2025-06-01 04:03:54.983 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:03:54.992 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 04:03:55.025 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 04:03:55.069 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-01 04:03:55.078 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-01 04:03:55.381 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 04:03:55.466 +04:00 [INF] Executed DbCommand (18ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 04:04:10.499 +04:00 [INF] Executed DbCommand (19ms) [Parameters=[@p0='a64cac48-8930-49a0-993c-eaf3170e652d', @p1='2025-06-01T00:04:10.4205737Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Jules' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$sWPcDx0KEX6HSDLR2hb2YeaCVuc6qqn2MH23VOvDLUApz8KUhyvxi' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-06-01T00:03:55.6811193Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+230', @p24='+23054878091', @p25='20341a31-3a07-4914-b4c5-fae5b2246fc5', @p26='Created' (Nullable = false), @p27='"User created with role SeniorLawyer"' (DbType = Object), @p28='2025-06-01T00:04:10.4207260Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='a64cac48-8930-49a0-993c-eaf3170e652d', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-06-01T00:03:55.4885568Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='56287981-59e4-41e3-8ffb-d6d2e7e2b5a9', @p42='ProfileUpdated' (Nullable = false), @p43='null' (DbType = Object), @p44='2025-06-01T00:04:10.4207279Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='a64cac48-8930-49a0-993c-eaf3170e652d', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-06-01T00:03:55.6805365Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='7248b6c8-0258-4a56-933b-0e039df1eb02', @p58='PasswordChanged' (Nullable = false), @p59='null' (DbType = Object), @p60='2025-06-01T00:04:10.4207272Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='a64cac48-8930-49a0-993c-eaf3170e652d', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-06-01T00:03:55.6778065Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='a47391ac-8cc0-431b-a5a6-9b8764bbb479', @p74='PreferencesUpdated' (Nullable = false), @p75='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p76='2025-06-01T00:04:10.4207283Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='a64cac48-8930-49a0-993c-eaf3170e652d', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-06-01T00:03:55.6811240Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING xmin;
2025-06-01 04:04:10.528 +04:00 [INF] User added successfully: "a64cac48-8930-49a0-993c-eaf3170e652d"
2025-06-01 04:04:10.530 +04:00 [INF] User "a64cac48-8930-49a0-993c-eaf3170e652d" registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 04:04:10.533 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 04:04:10.541 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-06-01 04:04:10.573 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 15541.5978ms
2025-06-01 04:04:10.576 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 04:04:10.578 +04:00 [INF] Request POST /api/auth/register completed in 15595ms with status 201 (Correlation ID: 89471e8a-f2ed-4a7f-acf6-714631d83f54)
2025-06-01 04:04:10.585 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/register - 201 null application/json; charset=utf-8 15613.2123ms
2025-06-01 04:05:48.902 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 04:05:48.974 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 923de5e4-a589-4cb5-883c-90ffdc791c89
2025-06-01 04:05:49.013 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:05:49.015 +04:00 [INF] Request OPTIONS /api/auth/login completed in 1ms with status 204 (Correlation ID: 923de5e4-a589-4cb5-883c-90ffdc791c89)
2025-06-01 04:05:49.019 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 117.6977ms
2025-06-01 04:05:49.021 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 04:05:49.037 +04:00 [INF] Request POST /api/auth/login started with correlation ID e48bf548-edc4-4f19-919e-e7d44e8d61c2
2025-06-01 04:05:49.042 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:05:49.050 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 04:05:49.067 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 04:05:49.089 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 04:05:49.124 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 04:05:49.356 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 04:06:48.675 +04:00 [INF] Executed DbCommand (15ms) [Parameters=[@p15='ca4d00af-8911-4480-b918-b13d0742ac01', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-06-01T00:06:03.5928760Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='a64cac48-8930-49a0-993c-eaf3170e652d', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-06-01T00:06:03.5928784Z' (DbType = DateTime), @p11='2025-06-01T00:06:48.5873270Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='a64cac48-8930-49a0-993c-eaf3170e652d' (Nullable = false), @p16='0' (DbType = Object), @p38='a64cac48-8930-49a0-993c-eaf3170e652d', @p17='2025-06-01T00:04:10.4205730Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-06-01T00:06:03.5927227Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Jules' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$sWPcDx0KEX6HSDLR2hb2YeaCVuc6qqn2MH23VOvDLUApz8KUhyvxi' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-06-01T00:06:48.5873176Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='794' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND xmin = @p16
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND xmin = @p39
RETURNING xmin;
2025-06-01 04:06:48.895 +04:00 [ERR] Error during login for user "a64cac48-8930-49a0-993c-eaf3170e652d"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 308
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-06-01 04:06:49.069 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 308
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-01 04:06:49.077 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 04:06:49.090 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 60017.1283ms
2025-06-01 04:06:49.093 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 04:06:49.097 +04:00 [INF] Request POST /api/auth/login completed in 60055ms with status 401 (Correlation ID: e48bf548-edc4-4f19-919e-e7d44e8d61c2)
2025-06-01 04:06:49.101 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 60080.8044ms
2025-06-01 04:07:35.771 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 04:07:35.780 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 74db88b9-4a62-4767-983e-58c355bb74ac
2025-06-01 04:07:35.785 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:07:35.789 +04:00 [INF] Request OPTIONS /api/auth/login completed in 4ms with status 204 (Correlation ID: 74db88b9-4a62-4767-983e-58c355bb74ac)
2025-06-01 04:07:35.801 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 30.3407ms
2025-06-01 04:07:35.807 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 04:07:35.841 +04:00 [INF] Request POST /api/auth/login started with correlation ID 068def15-6c6f-41b7-967c-5e5ce1221d44
2025-06-01 04:07:35.844 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:07:35.846 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 04:07:35.847 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 04:07:35.852 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 04:07:35.854 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 04:07:35.862 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 04:09:12.751 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p15='d0f21165-2140-4565-b1ea-e8604ba9f88f', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-06-01T00:07:41.9351241Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='a64cac48-8930-49a0-993c-eaf3170e652d', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-06-01T00:07:41.9351266Z' (DbType = DateTime), @p11='2025-06-01T00:09:12.6838187Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='a64cac48-8930-49a0-993c-eaf3170e652d' (Nullable = false), @p16='0' (DbType = Object), @p38='a64cac48-8930-49a0-993c-eaf3170e652d', @p17='2025-06-01T00:04:10.4205730Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-06-01T00:07:41.9351056Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Jules' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$sWPcDx0KEX6HSDLR2hb2YeaCVuc6qqn2MH23VOvDLUApz8KUhyvxi' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-06-01T00:09:12.6514313Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='794' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND xmin = @p16
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND xmin = @p39
RETURNING xmin;
2025-06-01 04:09:12.904 +04:00 [ERR] Error during login for user "a64cac48-8930-49a0-993c-eaf3170e652d"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 308
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-06-01 04:09:12.998 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 308
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-01 04:09:13.005 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 04:09:13.006 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 97155.8891ms
2025-06-01 04:09:13.008 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 04:09:13.009 +04:00 [INF] Request POST /api/auth/login completed in 97164ms with status 401 (Correlation ID: 068def15-6c6f-41b7-967c-5e5ce1221d44)
2025-06-01 04:09:13.012 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 97204.9414ms
2025-06-01 04:14:21.038 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 04:14:21.075 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 04:14:21.080 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 04:14:21.593 +04:00 [INF] Executed DbCommand (83ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 04:14:21.618 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 04:14:21.661 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 04:14:21.967 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 04:14:21.969 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 04:14:22.036 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 04:14:22.038 +04:00 [INF] Hosting environment: Development
2025-06-01 04:14:22.039 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 04:14:23.259 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 04:14:23.552 +04:00 [INF] Request GET / started with correlation ID f24bd430-0c5d-421d-aaf8-02df6d10dcbe
2025-06-01 04:14:23.698 +04:00 [INF] Request GET / completed in 141ms with status 404 (Correlation ID: f24bd430-0c5d-421d-aaf8-02df6d10dcbe)
2025-06-01 04:14:23.706 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 469.7921ms
2025-06-01 04:14:23.722 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 04:15:23.495 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/register - null null
2025-06-01 04:15:23.516 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID a21abd42-478a-45db-83e2-cac4bd518005
2025-06-01 04:15:23.530 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:15:23.540 +04:00 [INF] Request OPTIONS /api/auth/register completed in 13ms with status 204 (Correlation ID: a21abd42-478a-45db-83e2-cac4bd518005)
2025-06-01 04:15:23.553 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/register - 204 null null 118.1236ms
2025-06-01 04:15:23.560 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/register - application/json 260
2025-06-01 04:15:23.586 +04:00 [INF] Request POST /api/auth/register started with correlation ID 7171bd50-3ff1-4368-8aaa-ecda0a6cbe8c
2025-06-01 04:15:23.592 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:15:23.597 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 04:15:23.640 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 04:15:23.735 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-01 04:15:23.754 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-01 04:15:24.703 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 04:15:24.925 +04:00 [INF] Executed DbCommand (43ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 04:16:10.072 +04:00 [INF] Executed DbCommand (17ms) [Parameters=[@p0='01afa4f8-94b0-4013-989f-bba748a00536', @p1='2025-06-01T00:16:09.8750908Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Jules' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$8JfVl2XI8bNh.bXmfTUf0uL2xGdolvEw9GILmnrqL6HQNm.Ks8uVu' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-06-01T00:15:25.5378772Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+230', @p24='+23054878091', @p25='17a1016b-3464-4b2d-853a-15a8d27440e2', @p26='Created' (Nullable = false), @p27='"User created with role SeniorLawyer"' (DbType = Object), @p28='2025-06-01T00:16:09.8752487Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='01afa4f8-94b0-4013-989f-bba748a00536', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-06-01T00:15:24.9826755Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='1b079d1a-1ce1-458a-862d-1f9e2868e6da', @p42='PreferencesUpdated' (Nullable = false), @p43='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p44='2025-06-01T00:16:09.8752496Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='01afa4f8-94b0-4013-989f-bba748a00536', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-06-01T00:15:25.5378836Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='98f27d03-b702-4b4b-893d-854e7de0d934', @p58='ProfileUpdated' (Nullable = false), @p59='null' (DbType = Object), @p60='2025-06-01T00:16:09.8752493Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='01afa4f8-94b0-4013-989f-bba748a00536', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-06-01T00:15:25.5371514Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='bce64a8a-6617-4efb-b05e-3a766e8c48c6', @p74='PasswordChanged' (Nullable = false), @p75='null' (DbType = Object), @p76='2025-06-01T00:16:09.8752490Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='01afa4f8-94b0-4013-989f-bba748a00536', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-06-01T00:15:25.5320770Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING xmin;
2025-06-01 04:16:10.104 +04:00 [INF] User added successfully: "01afa4f8-94b0-4013-989f-bba748a00536"
2025-06-01 04:16:10.108 +04:00 [INF] User "01afa4f8-94b0-4013-989f-bba748a00536" registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 04:16:10.114 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 04:16:10.120 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-06-01 04:16:10.146 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 46496.2018ms
2025-06-01 04:16:10.150 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 04:16:10.151 +04:00 [INF] Request POST /api/auth/register completed in 46560ms with status 201 (Correlation ID: 7171bd50-3ff1-4368-8aaa-ecda0a6cbe8c)
2025-06-01 04:16:10.157 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/register - 201 null application/json; charset=utf-8 46597.2447ms
2025-06-01 04:16:22.042 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 04:16:22.055 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 7443f1ee-53d1-4e9c-807a-1319302019bd
2025-06-01 04:16:22.061 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:16:22.063 +04:00 [INF] Request OPTIONS /api/auth/login completed in 1ms with status 204 (Correlation ID: 7443f1ee-53d1-4e9c-807a-1319302019bd)
2025-06-01 04:16:22.069 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 27.172ms
2025-06-01 04:16:22.074 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 04:16:22.083 +04:00 [INF] Request POST /api/auth/login started with correlation ID 1367f848-1e90-42b1-962a-66883d2c494a
2025-06-01 04:16:22.085 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:16:22.090 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 04:16:22.097 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 04:16:22.111 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 04:16:22.124 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 04:16:22.192 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 04:17:51.907 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p15='9a98457f-227a-4f98-a3bd-8fb05e87caef', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-06-01T00:16:38.3994032Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='01afa4f8-94b0-4013-989f-bba748a00536', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-06-01T00:16:38.3994047Z' (DbType = DateTime), @p11='2025-06-01T00:17:41.6074081Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='01afa4f8-94b0-4013-989f-bba748a00536' (Nullable = false), @p16='0' (DbType = Object), @p38='01afa4f8-94b0-4013-989f-bba748a00536', @p17='2025-06-01T00:16:09.8750900Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-06-01T00:16:38.3992379Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Jules' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$8JfVl2XI8bNh.bXmfTUf0uL2xGdolvEw9GILmnrqL6HQNm.Ks8uVu' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-06-01T00:17:35.0868529Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='793' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND xmin = @p16
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND xmin = @p39
RETURNING xmin;
2025-06-01 04:17:52.122 +04:00 [ERR] Error during login for user "01afa4f8-94b0-4013-989f-bba748a00536"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 308
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-06-01 04:17:52.229 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 308
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-01 04:17:52.240 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 04:17:52.254 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 90152.4563ms
2025-06-01 04:17:52.257 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 04:17:52.259 +04:00 [INF] Request POST /api/auth/login completed in 90174ms with status 401 (Correlation ID: 1367f848-1e90-42b1-962a-66883d2c494a)
2025-06-01 04:17:52.264 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 90190.6425ms
2025-06-01 04:39:30.827 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 04:39:30.867 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 04:39:30.874 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 04:39:31.326 +04:00 [INF] Executed DbCommand (83ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 04:39:31.350 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 04:39:31.400 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 04:39:31.735 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 04:39:31.737 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 04:39:31.788 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 04:39:31.791 +04:00 [INF] Hosting environment: Development
2025-06-01 04:39:31.793 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 04:39:33.375 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 04:39:33.648 +04:00 [INF] Request GET / started with correlation ID 0aa26a39-4c05-494e-8acb-8ebf7ff8c3ac
2025-06-01 04:39:33.892 +04:00 [INF] Request GET / completed in 235ms with status 404 (Correlation ID: 0aa26a39-4c05-494e-8acb-8ebf7ff8c3ac)
2025-06-01 04:39:33.993 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 631.1538ms
2025-06-01 04:39:34.014 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 04:40:39.466 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/register - null null
2025-06-01 04:40:39.510 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID ac411d94-834f-460e-b619-09ffd02442db
2025-06-01 04:40:39.521 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:40:39.529 +04:00 [INF] Request OPTIONS /api/auth/register completed in 12ms with status 204 (Correlation ID: ac411d94-834f-460e-b619-09ffd02442db)
2025-06-01 04:40:39.539 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/register - 204 null null 72.8886ms
2025-06-01 04:40:39.545 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/register - application/json 260
2025-06-01 04:40:39.564 +04:00 [INF] Request POST /api/auth/register started with correlation ID 249ea2a7-55fc-4cc5-932f-39243e129597
2025-06-01 04:40:39.570 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:40:39.577 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 04:40:39.628 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 04:40:39.737 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-01 04:40:39.756 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-01 04:40:40.660 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 04:40:40.863 +04:00 [INF] Executed DbCommand (35ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 04:40:48.656 +04:00 [INF] Executed DbCommand (20ms) [Parameters=[@p0='f3c48451-9831-4dfe-b6bb-0f2a92d7fb81', @p1='2025-06-01T00:40:48.5520425Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Jules' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$Dk5SMj2ZqGQGJcLWnIp6UuMjvRVu7j2ZQ6I0JOBiPWCr.kwv/Y/0S' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-06-01T00:40:41.4632798Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+230', @p24='+23054878091', @p25='03803a16-09e0-4dc4-953b-8b036cc5b2c5', @p26='PreferencesUpdated' (Nullable = false), @p27='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p28='2025-06-01T00:40:48.5522021Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='f3c48451-9831-4dfe-b6bb-0f2a92d7fb81', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-06-01T00:40:41.4632917Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='3d93c0a5-e97f-44e7-ae75-219ff040c36d', @p42='ProfileUpdated' (Nullable = false), @p43='null' (DbType = Object), @p44='2025-06-01T00:40:48.5522015Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='f3c48451-9831-4dfe-b6bb-0f2a92d7fb81', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-06-01T00:40:41.4622282Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='5df20a5e-f245-4bf7-bb6c-4f6c38ebb862', @p58='PasswordChanged' (Nullable = false), @p59='null' (DbType = Object), @p60='2025-06-01T00:40:48.5522006Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='f3c48451-9831-4dfe-b6bb-0f2a92d7fb81', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-06-01T00:40:41.4568067Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='8830f2de-6d05-465d-800e-f3ff2f53d922', @p74='Created' (Nullable = false), @p75='"User created with role SeniorLawyer"' (DbType = Object), @p76='2025-06-01T00:40:48.5521994Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='f3c48451-9831-4dfe-b6bb-0f2a92d7fb81', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-06-01T00:40:40.9409454Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING xmin;
2025-06-01 04:40:48.696 +04:00 [INF] User added successfully: "f3c48451-9831-4dfe-b6bb-0f2a92d7fb81"
2025-06-01 04:40:48.700 +04:00 [INF] User "f3c48451-9831-4dfe-b6bb-0f2a92d7fb81" registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 04:40:48.704 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 04:40:48.712 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-06-01 04:40:48.740 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 9096.7835ms
2025-06-01 04:40:48.743 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 04:40:48.747 +04:00 [INF] Request POST /api/auth/register completed in 9177ms with status 201 (Correlation ID: 249ea2a7-55fc-4cc5-932f-39243e129597)
2025-06-01 04:40:48.759 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/register - 201 null application/json; charset=utf-8 9213.6721ms
2025-06-01 04:40:58.161 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 04:40:58.172 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 835bee33-13aa-460a-a882-11b6ae04800b
2025-06-01 04:40:58.174 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:40:58.176 +04:00 [INF] Request OPTIONS /api/auth/login completed in 2ms with status 204 (Correlation ID: 835bee33-13aa-460a-a882-11b6ae04800b)
2025-06-01 04:40:58.181 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 19.6361ms
2025-06-01 04:40:58.183 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 04:40:58.191 +04:00 [INF] Request POST /api/auth/login started with correlation ID 8191b587-dbd3-423c-847e-6138cb105fb1
2025-06-01 04:40:58.194 +04:00 [INF] CORS policy execution successful.
2025-06-01 04:40:58.200 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 04:40:58.220 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 04:40:58.243 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 04:40:58.260 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 04:40:58.365 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 04:42:59.131 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p15='d22a6dd5-bdee-4158-b547-7cd3da47a515', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-06-01T00:41:29.1755266Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='f3c48451-9831-4dfe-b6bb-0f2a92d7fb81', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-06-01T00:41:29.1755290Z' (DbType = DateTime), @p11='2025-06-01T00:42:59.0819187Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='f3c48451-9831-4dfe-b6bb-0f2a92d7fb81' (Nullable = false), @p16='0' (DbType = Object), @p38='f3c48451-9831-4dfe-b6bb-0f2a92d7fb81', @p17='2025-06-01T00:40:48.5520420Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-06-01T00:41:29.1753654Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Jules' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$Dk5SMj2ZqGQGJcLWnIp6UuMjvRVu7j2ZQ6I0JOBiPWCr.kwv/Y/0S' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-06-01T00:42:59.0819118Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='793' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND xmin = @p16
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND xmin = @p39
RETURNING xmin;
2025-06-01 04:42:59.279 +04:00 [ERR] Error during login for user "f3c48451-9831-4dfe-b6bb-0f2a92d7fb81"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 308
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-06-01 04:42:59.394 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 308
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-01 04:42:59.403 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 04:42:59.418 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 121192.5047ms
2025-06-01 04:42:59.421 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 04:42:59.423 +04:00 [INF] Request POST /api/auth/login completed in 121229ms with status 401 (Correlation ID: 8191b587-dbd3-423c-847e-6138cb105fb1)
2025-06-01 04:42:59.430 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 121247.5411ms
2025-06-01 05:08:15.639 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 05:08:15.675 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 05:08:15.683 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 05:08:16.099 +04:00 [INF] Executed DbCommand (66ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 05:08:16.135 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 05:08:16.204 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 05:08:16.702 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 05:08:16.708 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 05:08:16.784 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 05:08:16.786 +04:00 [INF] Hosting environment: Development
2025-06-01 05:08:16.788 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 05:08:18.202 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 05:08:18.441 +04:00 [INF] Request GET / started with correlation ID 811175e8-2ff4-4e50-8be6-f1416f374edc
2025-06-01 05:08:18.592 +04:00 [INF] Request GET / completed in 142ms with status 404 (Correlation ID: 811175e8-2ff4-4e50-8be6-f1416f374edc)
2025-06-01 05:08:18.601 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 418.6173ms
2025-06-01 05:08:18.611 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 05:09:01.744 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/register - null null
2025-06-01 05:09:01.808 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID d09e6607-f816-40cf-ac52-c7d37aba95b9
2025-06-01 05:09:01.821 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:09:01.834 +04:00 [INF] Request OPTIONS /api/auth/register completed in 18ms with status 204 (Correlation ID: d09e6607-f816-40cf-ac52-c7d37aba95b9)
2025-06-01 05:09:01.844 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/register - 204 null null 112.9072ms
2025-06-01 05:09:01.848 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/register - application/json 260
2025-06-01 05:09:01.864 +04:00 [INF] Request POST /api/auth/register started with correlation ID 9106639c-259a-4071-bb90-5dc89133035d
2025-06-01 05:09:01.868 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:09:01.874 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 05:09:01.937 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 05:09:02.034 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-01 05:09:02.165 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-01 05:09:03.053 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 05:09:03.247 +04:00 [INF] Executed DbCommand (31ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 05:09:04.454 +04:00 [INF] Executed DbCommand (51ms) [Parameters=[@p0='8ba2bfa6-0947-46f4-b719-68ce7b479df4', @p1='2025-06-01T01:09:04.2327269Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Jules' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$/xm6r/z1xU5grUnQYUuQSO6Bis4Dy06jqBqyEq5h7VbJlF4vBlqIm' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-06-01T01:09:03.8427772Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+230', @p24='+23054878091', @p25='0a7e296e-5e2e-4042-88c3-9ff02c53deda', @p26='PasswordChanged' (Nullable = false), @p27='null' (DbType = Object), @p28='2025-06-01T01:09:04.2328672Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='8ba2bfa6-0947-46f4-b719-68ce7b479df4', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-06-01T01:09:03.8360015Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='20188d52-33c1-4c0f-90da-10354c699dcf', @p42='Created' (Nullable = false), @p43='"User created with role SeniorLawyer"' (DbType = Object), @p44='2025-06-01T01:09:04.2328660Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='8ba2bfa6-0947-46f4-b719-68ce7b479df4', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-06-01T01:09:03.3079031Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='3458e26e-97e4-42db-adc3-c29ff53fe1aa', @p58='ProfileUpdated' (Nullable = false), @p59='null' (DbType = Object), @p60='2025-06-01T01:09:04.2328679Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='8ba2bfa6-0947-46f4-b719-68ce7b479df4', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-06-01T01:09:03.8417463Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='ac763180-973d-4e22-a3ae-68df24875f4c', @p74='PreferencesUpdated' (Nullable = false), @p75='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p76='2025-06-01T01:09:04.2328684Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='8ba2bfa6-0947-46f4-b719-68ce7b479df4', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-06-01T01:09:03.8427862Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING xmin;
2025-06-01 05:09:04.555 +04:00 [INF] User added successfully: "8ba2bfa6-0947-46f4-b719-68ce7b479df4"
2025-06-01 05:09:04.560 +04:00 [INF] User "8ba2bfa6-0947-46f4-b719-68ce7b479df4" registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 05:09:04.567 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 05:09:04.586 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-06-01 05:09:04.685 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 2737.078ms
2025-06-01 05:09:04.693 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 05:09:04.698 +04:00 [INF] Request POST /api/auth/register completed in 2830ms with status 201 (Correlation ID: 9106639c-259a-4071-bb90-5dc89133035d)
2025-06-01 05:09:04.712 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/register - 201 null application/json; charset=utf-8 2864.1016ms
2025-06-01 05:09:15.060 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 05:09:15.068 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID c9998c3d-1505-4b0c-96e9-3fa64806f6fe
2025-06-01 05:09:15.072 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:09:15.074 +04:00 [INF] Request OPTIONS /api/auth/login completed in 2ms with status 204 (Correlation ID: c9998c3d-1505-4b0c-96e9-3fa64806f6fe)
2025-06-01 05:09:15.079 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 18.6296ms
2025-06-01 05:09:15.083 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 05:09:15.090 +04:00 [INF] Request POST /api/auth/login started with correlation ID fdb0fb73-ad4c-432b-a993-9e99217e8944
2025-06-01 05:09:15.092 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:09:15.098 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 05:09:15.111 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 05:09:15.128 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 05:09:15.212 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 05:09:15.224 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 05:09:21.063 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p15='1bd216ee-f23d-44ee-894a-de7b678527c5', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-06-01T01:09:19.2332120Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8ba2bfa6-0947-46f4-b719-68ce7b479df4', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-06-01T01:09:19.2332150Z' (DbType = DateTime), @p11='2025-06-01T01:09:21.0536647Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='8ba2bfa6-0947-46f4-b719-68ce7b479df4' (Nullable = false), @p16='0' (DbType = Object), @p38='8ba2bfa6-0947-46f4-b719-68ce7b479df4', @p17='2025-06-01T01:09:04.2327260Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-06-01T01:09:19.2330957Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Jules' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$/xm6r/z1xU5grUnQYUuQSO6Bis4Dy06jqBqyEq5h7VbJlF4vBlqIm' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-06-01T01:09:21.0536628Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='793' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND xmin = @p16
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND xmin = @p39
RETURNING xmin;
2025-06-01 05:09:21.227 +04:00 [ERR] Error during login for user "8ba2bfa6-0947-46f4-b719-68ce7b479df4"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 316
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-06-01 05:09:21.348 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 316
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-01 05:09:21.356 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 05:09:21.366 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 6247.2353ms
2025-06-01 05:09:21.368 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 05:09:21.370 +04:00 [INF] Request POST /api/auth/login completed in 6278ms with status 401 (Correlation ID: fdb0fb73-ad4c-432b-a993-9e99217e8944)
2025-06-01 05:09:21.372 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 6289.5639ms
2025-06-01 05:10:36.134 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 05:10:36.190 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID d83e5dfb-e182-4019-ae48-24d70352e3fd
2025-06-01 05:10:36.199 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:10:36.203 +04:00 [INF] Request OPTIONS /api/auth/login completed in 4ms with status 204 (Correlation ID: d83e5dfb-e182-4019-ae48-24d70352e3fd)
2025-06-01 05:10:36.219 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 85.1436ms
2025-06-01 05:10:36.228 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 05:10:36.247 +04:00 [INF] Request POST /api/auth/login started with correlation ID 24f12e21-e7f0-4d9c-a7e2-4140218f3dfb
2025-06-01 05:10:36.250 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:10:36.252 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 05:10:36.255 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 05:10:36.260 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 05:10:36.263 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 05:10:36.268 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 05:10:43.633 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p15='e7792296-0514-477d-8f1a-00f00fddb4cb', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-06-01T01:10:40.3175515Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8ba2bfa6-0947-46f4-b719-68ce7b479df4', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-06-01T01:10:40.3175538Z' (DbType = DateTime), @p11='2025-06-01T01:10:43.6281859Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='8ba2bfa6-0947-46f4-b719-68ce7b479df4' (Nullable = false), @p16='0' (DbType = Object), @p38='8ba2bfa6-0947-46f4-b719-68ce7b479df4', @p17='2025-06-01T01:09:04.2327260Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-06-01T01:10:40.3175360Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Jules' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$/xm6r/z1xU5grUnQYUuQSO6Bis4Dy06jqBqyEq5h7VbJlF4vBlqIm' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-06-01T01:10:43.6281833Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='793' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND xmin = @p16
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND xmin = @p39
RETURNING xmin;
2025-06-01 05:10:43.770 +04:00 [ERR] Error during login for user "8ba2bfa6-0947-46f4-b719-68ce7b479df4"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 316
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-06-01 05:10:43.856 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 316
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-01 05:10:43.862 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 05:10:43.864 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 7605.9214ms
2025-06-01 05:10:43.866 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 05:10:43.869 +04:00 [INF] Request POST /api/auth/login completed in 7619ms with status 401 (Correlation ID: 24f12e21-e7f0-4d9c-a7e2-4140218f3dfb)
2025-06-01 05:10:43.872 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 7644.318ms
2025-06-01 05:10:50.849 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 05:10:50.863 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID f1658690-1488-4c20-89a3-b7de25cd8b12
2025-06-01 05:10:50.872 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:10:50.877 +04:00 [INF] Request OPTIONS /api/auth/login completed in 5ms with status 204 (Correlation ID: f1658690-1488-4c20-89a3-b7de25cd8b12)
2025-06-01 05:10:50.888 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 38.8646ms
2025-06-01 05:10:50.890 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 05:10:50.905 +04:00 [INF] Request POST /api/auth/login started with correlation ID 1f0eba3a-ffed-451e-962f-1119d58866a4
2025-06-01 05:10:50.909 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:10:50.910 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 05:10:50.911 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 05:10:50.913 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 05:10:50.920 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 05:10:50.925 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 05:12:17.575 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p15='f8596025-2e8f-4aa3-8dd6-e49cfe102cd3', @p0='Login' (Nullable = false), @p1='"Successful login from ::1"' (DbType = Object), @p2='2025-06-01T01:10:51.0500903Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8ba2bfa6-0947-46f4-b719-68ce7b479df4', @p7='User' (Nullable = false), @p8=NULL, @p9='False', @p10='2025-06-01T01:10:51.0500925Z' (DbType = DateTime), @p11='2025-06-01T01:12:17.5688646Z' (Nullable = true) (DbType = DateTime), @p12=NULL, @p13=NULL, @p14='8ba2bfa6-0947-46f4-b719-68ce7b479df4' (Nullable = false), @p16='0' (DbType = Object), @p38='8ba2bfa6-0947-46f4-b719-68ce7b479df4', @p17='2025-06-01T01:09:04.2327260Z' (DbType = DateTime), @p18='system', @p19=NULL (DbType = DateTime), @p20=NULL, @p21='0', @p22='Kevin' (Nullable = false), @p23='True', @p24='False', @p25='False', @p26='False', @p27='2025-06-01T01:10:51.0500771Z' (Nullable = true) (DbType = DateTime), @p28='::1', @p29='Jules' (Nullable = false), @p30=NULL (DbType = DateTime), @p31='$2a$11$/xm6r/z1xU5grUnQYUuQSO6Bis4Dy06jqBqyEq5h7VbJlF4vBlqIm' (Nullable = false), @p32='fr-FR' (Nullable = false), @p33=NULL, @p34='SeniorLawyer' (Nullable = false), @p35='Europe/Paris' (Nullable = false), @p36='2025-06-01T01:12:17.5688613Z' (Nullable = true) (DbType = DateTime), @p37='system', @p39='793' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "AuditEntries" SET "Action" = @p0, "Changes" = @p1, "CreatedAt" = @p2, "CreatedBy" = @p3, "DeletedAt" = @p4, "DeletedBy" = @p5, "EntityId" = @p6, "EntityType" = @p7, "IpAddress" = @p8, "IsDeleted" = @p9, "Timestamp" = @p10, "UpdatedAt" = @p11, "UpdatedBy" = @p12, "UserAgent" = @p13, "UserId" = @p14
WHERE "Id" = @p15 AND xmin = @p16
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p17, "CreatedBy" = @p18, "DeletedAt" = @p19, "DeletedBy" = @p20, "FailedLoginAttempts" = @p21, "FirstName" = @p22, "IsActive" = @p23, "IsDeleted" = @p24, "IsEmailVerified" = @p25, "IsLocked" = @p26, "LastLoginAt" = @p27, "LastLoginIpAddress" = @p28, "LastName" = @p29, "LockedAt" = @p30, "PasswordHash" = @p31, "PreferredLanguage" = @p32, "ProfilePictureUrl" = @p33, "Role" = @p34, "TimeZone" = @p35, "UpdatedAt" = @p36, "UpdatedBy" = @p37
WHERE "Id" = @p38 AND xmin = @p39
RETURNING xmin;
2025-06-01 05:12:17.739 +04:00 [ERR] Error during login for user "8ba2bfa6-0947-46f4-b719-68ce7b479df4"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 316
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
2025-06-01 05:12:17.829 +04:00 [WRN] Login failed <NAME_EMAIL>
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Data\IdentityDbContext.cs:line 316
   at LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(User user, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Infrastructure\Repositories\UserRepository.cs:line 192
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 116
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-01 05:12:17.836 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 05:12:17.838 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 86925.423ms
2025-06-01 05:12:17.850 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 05:12:17.851 +04:00 [INF] Request POST /api/auth/login completed in 86942ms with status 401 (Correlation ID: 1f0eba3a-ffed-451e-962f-1119d58866a4)
2025-06-01 05:12:17.855 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 401 null application/json; charset=utf-8 86964.6859ms
2025-06-01 05:23:33.447 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 05:23:33.486 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 05:23:33.493 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 05:23:33.958 +04:00 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 05:23:33.980 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 05:23:34.036 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 05:23:34.405 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 05:23:34.408 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 05:23:34.548 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 05:23:34.563 +04:00 [INF] Hosting environment: Development
2025-06-01 05:23:34.565 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 05:23:35.615 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 05:23:35.923 +04:00 [INF] Request GET / started with correlation ID 26b2bb2f-365b-418b-b699-3bd06655dce1
2025-06-01 05:23:36.069 +04:00 [INF] Request GET / completed in 141ms with status 404 (Correlation ID: 26b2bb2f-365b-418b-b699-3bd06655dce1)
2025-06-01 05:23:36.077 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 476.3284ms
2025-06-01 05:23:36.086 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 05:24:37.313 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/register - null null
2025-06-01 05:24:37.342 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID e294aa5d-c3f9-4393-b447-9e85358a0221
2025-06-01 05:24:37.352 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:24:37.361 +04:00 [INF] Request OPTIONS /api/auth/register completed in 11ms with status 204 (Correlation ID: e294aa5d-c3f9-4393-b447-9e85358a0221)
2025-06-01 05:24:37.368 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/register - 204 null null 55.7576ms
2025-06-01 05:24:37.374 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/register - application/json 260
2025-06-01 05:24:37.391 +04:00 [INF] Request POST /api/auth/register started with correlation ID fe76480b-b5f3-44a5-9f94-3bf19c61d54e
2025-06-01 05:24:37.395 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:24:37.401 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 05:24:37.443 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 05:24:37.610 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-01 05:24:37.708 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-01 05:24:38.538 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 05:24:38.735 +04:00 [INF] Executed DbCommand (34ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 05:24:47.074 +04:00 [INF] Executed DbCommand (24ms) [Parameters=[@p0='d98750f0-6419-43bf-a771-0c5832c916a7', @p1='2025-06-01T01:24:46.9037085Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Jules' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$6AvHVb7hV5NJJFVMAxWjCOtgZjxqsWqYy0fnGzwECrNt.OwDkZuAa' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-06-01T01:24:39.3030226Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+230', @p24='+23054878091', @p25='382f688c-b86b-4ca0-8baf-be5a31f5e5c6', @p26='PreferencesUpdated' (Nullable = false), @p27='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p28='2025-06-01T01:24:46.9038520Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='d98750f0-6419-43bf-a771-0c5832c916a7', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-06-01T01:24:39.3030313Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='9550d24b-2cb6-46c8-9e0d-d3e25f48294f', @p42='Created' (Nullable = false), @p43='"User created with role SeniorLawyer"' (DbType = Object), @p44='2025-06-01T01:24:46.9038498Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='d98750f0-6419-43bf-a771-0c5832c916a7', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-06-01T01:24:38.7963028Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='ac026cd6-1e16-4fa9-b345-d5698eff734e', @p58='ProfileUpdated' (Nullable = false), @p59='null' (DbType = Object), @p60='2025-06-01T01:24:46.9038513Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='d98750f0-6419-43bf-a771-0c5832c916a7', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-06-01T01:24:39.3021884Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='ff734f23-ddf2-467f-a709-252722697ab8', @p74='PasswordChanged' (Nullable = false), @p75='null' (DbType = Object), @p76='2025-06-01T01:24:46.9038509Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='d98750f0-6419-43bf-a771-0c5832c916a7', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-06-01T01:24:39.2976411Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING xmin;
2025-06-01 05:24:47.139 +04:00 [INF] User added successfully: "d98750f0-6419-43bf-a771-0c5832c916a7"
2025-06-01 05:24:47.143 +04:00 [INF] User "d98750f0-6419-43bf-a771-0c5832c916a7" registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 05:24:47.152 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 05:24:47.174 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-06-01 05:24:47.244 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 9788.2286ms
2025-06-01 05:24:47.251 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 05:24:47.256 +04:00 [INF] Request POST /api/auth/register completed in 9861ms with status 201 (Correlation ID: fe76480b-b5f3-44a5-9f94-3bf19c61d54e)
2025-06-01 05:24:47.269 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/register - 201 null application/json; charset=utf-8 9895.292ms
2025-06-01 05:24:59.326 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 05:24:59.345 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 0ca4bfdc-1dd5-4e28-9e75-6673f6470e2c
2025-06-01 05:24:59.355 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:24:59.360 +04:00 [INF] Request OPTIONS /api/auth/login completed in 5ms with status 204 (Correlation ID: 0ca4bfdc-1dd5-4e28-9e75-6673f6470e2c)
2025-06-01 05:24:59.366 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 40.3692ms
2025-06-01 05:24:59.368 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 05:24:59.376 +04:00 [INF] Request POST /api/auth/login started with correlation ID 758f4aaa-e53f-48f4-9ffc-5df83c24118e
2025-06-01 05:24:59.380 +04:00 [INF] CORS policy execution successful.
2025-06-01 05:24:59.384 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 05:24:59.391 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 05:24:59.399 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 05:24:59.479 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 05:24:59.490 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 05:25:05.412 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='1725a633-dac7-4405-adad-9be50fd2a848', @p1='Login' (Nullable = false), @p2='"Successful login from ::1"' (DbType = Object), @p3='2025-06-01T01:24:59.7548393Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='d98750f0-6419-43bf-a771-0c5832c916a7', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-01T01:24:59.7548410Z' (DbType = DateTime), @p12='2025-06-01T01:25:05.4016515Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='d98750f0-6419-43bf-a771-0c5832c916a7' (Nullable = false), @p37='d98750f0-6419-43bf-a771-0c5832c916a7', @p16='2025-06-01T01:24:46.9037080Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='0', @p21='Kevin' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-01T01:24:59.7547335Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Jules' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$6AvHVb7hV5NJJFVMAxWjCOtgZjxqsWqYy0fnGzwECrNt.OwDkZuAa' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='SeniorLawyer' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-01T01:25:05.4016477Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='793' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-01 05:25:05.418 +04:00 [INF] User updated successfully: "d98750f0-6419-43bf-a771-0c5832c916a7"
2025-06-01 05:25:13.263 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='22d601f2-4a57-4e62-8a47-2703f7035145', @p1='2025-06-01T01:25:13.2529234Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-08T01:25:10.3727946Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='BaXZ7lu4kBOL7KLPnFsUAidEiuD95vCKx5AruB7UkWGko+Nv8pZ86X6r8SpIhMAC32dve3twcngl5qyQi0QeSg==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='d98750f0-6419-43bf-a771-0c5832c916a7'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-01 05:25:13.268 +04:00 [INF] Refresh token added successfully: "22d601f2-4a57-4e62-8a47-2703f7035145"
2025-06-01 05:25:13.269 +04:00 [INF] User "d98750f0-6419-43bf-a771-0c5832c916a7" logged in successfully
2025-06-01 05:25:13.271 +04:00 [INF] Login successful <NAME_EMAIL>
2025-06-01 05:25:13.273 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-01 05:25:13.277 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 13883.5329ms
2025-06-01 05:25:13.279 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 05:25:13.281 +04:00 [INF] Request POST /api/auth/login completed in 13901ms with status 200 (Correlation ID: 758f4aaa-e53f-48f4-9ffc-5df83c24118e)
2025-06-01 05:25:13.284 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 200 null application/json; charset=utf-8 13915.6378ms
2025-06-01 17:00:27.115 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 17:00:27.158 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 17:00:27.163 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 17:00:27.710 +04:00 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 17:00:27.723 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 17:00:27.749 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 17:00:27.895 +04:00 [INF] Now listening on: http://localhost:5001
2025-06-01 17:00:27.899 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 17:00:27.900 +04:00 [INF] Hosting environment: Development
2025-06-01 17:00:27.901 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 17:00:38.908 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5001/swagger - null null
2025-06-01 17:00:38.977 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5001/swagger - 301 0 null 70.6131ms
2025-06-01 17:00:39.011 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5001/swagger/index.html - null null
2025-06-01 17:00:39.070 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5001/swagger/index.html - 200 null text/html;charset=utf-8 58.8264ms
2025-06-01 17:00:39.116 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5001/swagger/swagger-ui.css - null null
2025-06-01 17:00:39.140 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5001/swagger/swagger-ui-bundle.js - null null
2025-06-01 17:00:39.140 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5001/swagger/index.css - null null
2025-06-01 17:00:39.140 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5001/swagger/swagger-ui-standalone-preset.js - null null
2025-06-01 17:00:39.140 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5001/swagger/index.js - null null
2025-06-01 17:00:39.151 +04:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-06-01 17:00:39.163 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5001/swagger/index.js - 200 null application/javascript;charset=utf-8 23.0392ms
2025-06-01 17:00:39.165 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5001/swagger/index.css - 200 202 text/css 25.4914ms
2025-06-01 17:00:39.165 +04:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-01 17:00:39.180 +04:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-01 17:00:39.183 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5001/swagger/swagger-ui.css - 200 152035 text/css 67.6755ms
2025-06-01 17:00:39.184 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5001/swagger/swagger-ui-standalone-preset.js - 200 230007 text/javascript 43.6686ms
2025-06-01 17:00:39.249 +04:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-01 17:00:39.254 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5001/swagger/swagger-ui-bundle.js - 200 1426001 text/javascript 114.139ms
2025-06-01 17:00:39.530 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5001/swagger/v1/swagger.json - null null
2025-06-01 17:00:39.639 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5001/swagger/favicon-32x32.png - null null
2025-06-01 17:00:39.649 +04:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-01 17:00:39.656 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5001/swagger/favicon-32x32.png - 200 628 image/png 16.4228ms
2025-06-01 17:00:39.742 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5001/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 212.3293ms
2025-06-01 17:02:27.687 +04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5001/api/auth/register - application/json 105
2025-06-01 17:02:27.691 +04:00 [INF] Request POST /api/auth/register started with correlation ID 3dde0388-8e90-4b4a-89e0-ddcb920a323c
2025-06-01 17:02:27.695 +04:00 [WRN] Failed to determine the https port for redirect.
2025-06-01 17:02:29.770 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 17:02:29.795 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 17:02:29.828 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-01 17:02:29.846 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 45.6694ms
2025-06-01 17:02:29.849 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 17:02:29.850 +04:00 [INF] Request POST /api/auth/register completed in 2156ms with status 400 (Correlation ID: 3dde0388-8e90-4b4a-89e0-ddcb920a323c)
2025-06-01 17:02:29.852 +04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5001/api/auth/register - 400 null application/json; charset=utf-8 2165.0196ms
2025-06-01 17:03:14.150 +04:00 [INF] Application is shutting down...
