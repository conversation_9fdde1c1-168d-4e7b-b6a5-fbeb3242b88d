import { NavLink } from 'react-router-dom'
import {
  Search,
  MessageSquare,
  FileText,
  Users,
  FolderOpen,
  Settings,
  BarChart3,
  Scale,
  FileCheck,
  Calendar,
  Sun,
  Moon,
  Upload,
  Shield
} from 'lucide-react'
import { cn } from '../../lib/utils'
import { useAuthStore } from '../../store/authStore'
import { useThemeStore } from '../../store/themeStore'
import { UserRole } from '../../types'
import { getRoleLabel } from '../../utils/roleUtils'

const allowedRolesGroup1 = [UserRole.Administrator, UserRole.SeniorLawyer, UserRole.Lawyer] satisfies UserRole[];
const allowedRolesGroup2 = [UserRole.Administrator, UserRole.SeniorLawyer, UserRole.Lawyer, UserRole.LegalAssistant, UserRole.Client] satisfies UserRole[];
const adminOnly = [UserRole.Administrator] satisfies UserRole[];

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: BarChart3,
    roles: allowedRolesGroup1
  },
  {
    name: 'Recherche Juridique',
    href: '/search',
    icon: Search,
    roles: allowedRolesGroup1
  },
  {
    name: 'Assistant IA',
    href: '/chat',
    icon: MessageSquare,
    roles: allowedRolesGroup2
  },
  {
    name: 'Mes Documents',
    href: '/documents',
    icon: FileText,
    roles: allowedRolesGroup2
  },
  {
    name: 'Analyse Documents',
    href: '/document-analysis',
    icon: FileCheck,
    roles: allowedRolesGroup1
  },
  {
    name: 'Générateur Documents',
    href: '/document-generator',
    icon: Upload,
    roles: allowedRolesGroup1
  },
  {
    name: 'Clients',
    href: '/clients',
    icon: Users,
    roles: allowedRolesGroup1
  },
  {
    name: 'Dossiers',
    href: '/cases',
    icon: FolderOpen,
    roles: allowedRolesGroup1
  },
  {
    name: 'Agenda',
    href: '/calendar',
    icon: Calendar,
    roles: allowedRolesGroup1
  },
  {
    name: 'Utilisateurs',
    href: '/users',
    icon: Shield,
    roles: adminOnly
  },
  {
    name: 'Paramètres',
    href: '/settings',
    icon: Settings,
    roles: allowedRolesGroup2
  }
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const { user } = useAuthStore()
  const { theme, toggleTheme } = useThemeStore()

  const filteredNavigation = navigation.filter(item => {
    if (!user?.role) return false
    // Convertir le rôle utilisateur en nombre si nécessaire pour la comparaison
    const userRoleValue = typeof user.role === 'number' ? user.role : user.role
    return item.roles.includes(userRoleValue)
  })

  return (
    <div className={cn(
      "flex h-full w-64 flex-col",
      theme === 'dark' ? 'bg-gray-900' : 'bg-white border-r border-gray-200',
      className
    )}>
      {/* Logo */}
      <div className={cn(
        "flex h-16 items-center justify-center border-b",
        theme === 'dark' ? 'border-gray-800' : 'border-gray-200'
      )}>
        <div className="flex items-center space-x-2">
          <Scale className="h-8 w-8 text-blue-500" />
          <span className={cn(
            "text-xl font-bold",
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          )}>LexAi</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-2 py-4">
        {filteredNavigation.map((item) => (
          <NavLink
            key={item.name}
            to={item.href}
            className={({ isActive }) =>
              cn(
                "group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors",
                isActive
                  ? theme === 'dark'
                    ? "bg-gray-800 text-white"
                    : "bg-blue-50 text-blue-700"
                  : theme === 'dark'
                    ? "text-gray-300 hover:bg-gray-700 hover:text-white"
                    : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              )
            }
          >
            <item.icon
              className="mr-3 h-5 w-5 flex-shrink-0"
              aria-hidden="true"
            />
            {item.name}
          </NavLink>
        ))}
      </nav>

      {/* Theme Toggle */}
      <div className="px-2 pb-4">
        <button
          onClick={toggleTheme}
          className={cn(
            "w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors",
            theme === 'dark'
              ? "text-gray-300 hover:bg-gray-700 hover:text-white"
              : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
          )}
        >
          {theme === 'dark' ? (
            <Sun className="mr-3 h-5 w-5 flex-shrink-0" />
          ) : (
            <Moon className="mr-3 h-5 w-5 flex-shrink-0" />
          )}
          {theme === 'dark' ? 'Mode Clair' : 'Mode Sombre'}
        </button>
      </div>

      {/* User info */}
      {user && (
        <div className={cn(
          "border-t p-4",
          theme === 'dark' ? 'border-gray-800' : 'border-gray-200'
        )}>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              {user.avatar ? (
                <img
                  className="h-8 w-8 rounded-full"
                  src={user.avatar}
                  alt={`${user.firstName} ${user.lastName}`}
                />
              ) : (
                <div className={cn(
                  "h-8 w-8 rounded-full flex items-center justify-center",
                  theme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'
                )}>
                  <span className={cn(
                    "text-sm font-medium",
                    theme === 'dark' ? 'text-white' : 'text-gray-700'
                  )}>
                    {user.firstName[0]}{user.lastName[0]}
                  </span>
                </div>
              )}
            </div>
            <div className="ml-3">
              <p className={cn(
                "text-sm font-medium",
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              )}>
                {user.firstName} {user.lastName}
              </p>
              <p className={cn(
                "text-xs",
                theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
              )}>
                {getRoleLabel(user.role)}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
