using Microsoft.SemanticKernel;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using LexAI.DataPreprocessing.Application.DTOs;
using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using System.Text.Json;

namespace LexAI.DataPreprocessing.Infrastructure.Services;

/// <summary>
/// Service d'assurance qualité utilisant des LLM via Semantic Kernel
/// </summary>
public class LLMQualityAssuranceService
{
    private readonly Kernel _kernel;
    private readonly ILogger<LLMQualityAssuranceService> _logger;
    private readonly IConfiguration _configuration;

    public LLMQualityAssuranceService(
        IConfiguration configuration,
        ILogger<LLMQualityAssuranceService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _kernel = CreateKernel();
    }

    /// <summary>
    /// Évalue la qualité d'un document avec l'IA
    /// </summary>
    public async Task<QualityAssessmentDto> AssessDocumentQualityAsync(
        Document document,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Assessing document quality with LLM for document {DocumentId}", document.Id);

            var prompt = CreateDocumentQualityPrompt(document);
            var result = await _kernel.InvokePromptAsync(prompt, cancellationToken: cancellationToken);

            return ParseQualityAssessment(result.ToString(), document.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during LLM quality assessment for document {DocumentId}", document.Id);
            return CreateFallbackAssessment(document.Id, ex.Message);
        }
    }

    /// <summary>
    /// Évalue la qualité d'un chunk avec l'IA
    /// </summary>
    public async Task<QualityAssessmentDto> AssessChunkQualityAsync(
        DocumentChunk chunk,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var prompt = CreateChunkQualityPrompt(chunk);
            var result = await _kernel.InvokePromptAsync(prompt, cancellationToken: cancellationToken);

            return ParseQualityAssessment(result.ToString(), chunk.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during LLM chunk quality assessment for chunk {ChunkId}", chunk.Id);
            return CreateFallbackAssessment(chunk.Id, ex.Message);
        }
    }

    /// <summary>
    /// Valide les résultats du pipeline avec l'IA
    /// </summary>
    public async Task<PipelineValidationDto> ValidatePipelineResultsAsync(
        Document document,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var prompt = CreatePipelineValidationPrompt(document);
            var result = await _kernel.InvokePromptAsync(prompt, cancellationToken: cancellationToken);

            return ParsePipelineValidation(result.ToString(), document.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during LLM pipeline validation for document {DocumentId}", document.Id);
            return CreateFallbackPipelineValidation(document.Id, ex.Message);
        }
    }

    private Kernel CreateKernel()
    {
        var builder = Kernel.CreateBuilder();

        var modelType = _configuration["LLM:ModelType"] ?? "OpenAI";

        switch (modelType.ToLower())
        {
            case "openai":
                var apiKey = _configuration["OpenAI:ApiKey"];
                var model = _configuration["LLM:Model"] ?? "gpt-3.5-turbo";
                builder.AddOpenAIChatCompletion(model, apiKey);
                break;

            case "ollama":
                var ollamaEndpoint = _configuration["LLM:OllamaEndpoint"] ?? "http://localhost:11434";
                var ollamaModel = _configuration["LLM:OllamaModel"] ?? "llama3.2:3b";
                var httpClient = new HttpClient();
                httpClient.BaseAddress = new Uri(ollamaEndpoint);
                builder.AddOpenAIChatCompletion(ollamaModel, "dummy-key", httpClient: httpClient);
                break;
        }

        return builder.Build();
    }

    private string CreateDocumentQualityPrompt(Document document)
    {
        return $$"""
        Tu es un expert en qualité de documents juridiques.

        Évalue la qualité du document suivant selon ces critères :
        1. Lisibilité et clarté du texte
        2. Structure et organisation
        3. Complétude des informations
        4. Pertinence juridique
        5. Qualité de l'extraction de texte

        Informations du document :
        - Nom: {{document.FileName}}
        - Taille: {{document.FileSize}} bytes
        - Type: {{document.MimeType}}
        - Texte extrait: {{document.ExtractedText?.Substring(0, Math.Min(document.ExtractedText.Length, 1000)) ?? "Aucun"}}
        - Domaine détecté: {{document.DetectedDomain}}
        - Confiance classification: {{document.ClassificationConfidence}}

        Réponds au format JSON :
        {
            "overallScore": 0.85,
            "qualityPassed": true,
            "metrics": {
                "readability": 0.9,
                "structure": 0.8,
                "completeness": 0.85,
                "relevance": 0.9,
                "extraction": 0.8
            },
            "issues": [
                {
                    "type": "LowReadability",
                    "description": "Description du problème",
                    "severity": "Medium",
                    "suggestedFix": "Solution proposée"
                }
            ],
            "recommendations": [
                "Recommandation 1",
                "Recommandation 2"
            ]
        }
        """;
    }

    private string CreateChunkQualityPrompt(DocumentChunk chunk)
    {
        return $$"""
        Évalue la qualité de ce chunk de document juridique :

        Chunk :
        - Taille: {{chunk.CharacterCount}} caractères
        - Tokens: {{chunk.TokenCount}}
        - Contenu: {{chunk.Content?.Substring(0, Math.Min(chunk.Content.Length, 500)) ?? "Vide"}}

        Critères d'évaluation :
        1. Cohérence du contenu
        2. Taille appropriée
        3. Préservation du sens
        4. Qualité pour la vectorisation

        Réponds au format JSON avec score global (0-1) et détails.
        """;
    }

    private string CreatePipelineValidationPrompt(Document document)
    {
        return $$"""
        Valide les résultats complets du pipeline de traitement pour ce document juridique :

        Document: {{document.FileName}}
        - Extraction: {{(string.IsNullOrEmpty(document.ExtractedText) ? "Échec" : "Succès")}}
        - Classification: {{document.DetectedDomain}} (confiance: {{document.ClassificationConfidence}})
        - Chunks: {{document.ChunkCount}}
        - Vectorisé: {{document.IsVectorized}}
        - Statut: {{document.Status}}

        Vérifie la cohérence et la qualité globale du traitement.

        Réponds au format JSON avec validation globale et recommandations.
        """;
    }

    private QualityAssessmentDto ParseQualityAssessment(string response, Guid entityId)
    {
        try
        {
            var jsonResponse = JsonSerializer.Deserialize<JsonElement>(response);

            var assessment = new QualityAssessmentDto
            {
                OverallScore = jsonResponse.GetProperty("overallScore").GetDouble(),
                QualityPassed = jsonResponse.GetProperty("qualityPassed").GetBoolean(),
                AssessmentTime = TimeSpan.FromMilliseconds(100), // Temps d'évaluation simulé
                AgentName = "LLMQualityAssuranceAgent"
            };

            if (jsonResponse.TryGetProperty("metrics", out var metrics))
            {
                assessment.QualityMetrics = new Dictionary<string, double>();
                foreach (var metric in metrics.EnumerateObject())
                {
                    assessment.QualityMetrics[metric.Name] = metric.Value.GetDouble();
                }
            }

            if (jsonResponse.TryGetProperty("issues", out var issues))
            {
                assessment.Issues = issues.EnumerateArray().Select(issue => new QualityIssueDto
                {
                    IssueType = issue.GetProperty("type").GetString() ?? "",
                    Description = issue.GetProperty("description").GetString() ?? "",
                    Severity = Enum.Parse<ErrorSeverity>(issue.GetProperty("severity").GetString() ?? "Medium"),
                    SuggestedFix = issue.GetProperty("suggestedFix").GetString() ?? ""
                }).ToList();
            }

            if (jsonResponse.TryGetProperty("recommendations", out var recs))
            {
                assessment.Recommendations = recs.EnumerateArray()
                    .Select(r => r.GetString() ?? "")
                    .Where(r => !string.IsNullOrEmpty(r))
                    .ToList();
            }

            return assessment;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse quality assessment response: {Response}", response);
            return CreateFallbackAssessment(entityId, "Parse error");
        }
    }

    private PipelineValidationDto ParsePipelineValidation(string response, Guid documentId)
    {
        try
        {
            var jsonResponse = JsonSerializer.Deserialize<JsonElement>(response);

            return new PipelineValidationDto
            {
                ValidationPassed = jsonResponse.GetProperty("isValid").GetBoolean(),
                ValidationTime = TimeSpan.FromMilliseconds(200), // Temps de validation simulé
                AgentName = "LLMPipelineValidationAgent",
                ValidationErrors = jsonResponse.TryGetProperty("issues", out var issues)
                    ? issues.EnumerateArray().Select(i => i.GetString() ?? "").ToList()
                    : new List<string>(),
                ValidationWarnings = jsonResponse.TryGetProperty("recommendations", out var recs)
                    ? recs.EnumerateArray().Select(r => r.GetString() ?? "").ToList()
                    : new List<string>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse pipeline validation response: {Response}", response);
            return CreateFallbackPipelineValidation(documentId, "Parse error");
        }
    }

    private QualityAssessmentDto CreateFallbackAssessment(Guid entityId, string error)
    {
        return new QualityAssessmentDto
        {
            OverallScore = 0.5,
            QualityPassed = false,
            AssessmentTime = TimeSpan.FromMilliseconds(50),
            AgentName = "LLMQualityAssuranceAgent",
            Issues = new List<QualityIssueDto>
            {
                new QualityIssueDto
                {
                    IssueType = "LLMError",
                    Description = $"LLM quality assessment failed: {error}",
                    Severity = ErrorSeverity.Medium,
                    SuggestedFix = "Manual review recommended"
                }
            }
        };
    }

    private PipelineValidationDto CreateFallbackPipelineValidation(Guid documentId, string error)
    {
        return new PipelineValidationDto
        {
            ValidationPassed = false,
            ValidationTime = TimeSpan.FromMilliseconds(50),
            AgentName = "LLMPipelineValidationAgent",
            ValidationErrors = new List<string> { $"LLM validation failed: {error}" },
            ValidationWarnings = new List<string> { "Manual validation recommended" }
        };
    }
}
