using FluentAssertions;
using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using Xunit;

namespace LexAI.DataPreprocessing.UnitTests.Domain.Entities;

/// <summary>
/// Unit tests for Document entity
/// </summary>
public class DocumentTests
{
    [Fact]
    public void Create_WithValidParameters_ShouldCreateDocument()
    {
        // Arrange
        var fileName = "test-document.pdf";
        var fileSize = 1024L;
        var mimeType = "application/pdf";
        var fileHash = "abc123def456";
        var storagePath = "/storage/documents/test-document.pdf";
        var uploadedBy = "user123";

        // Act
        var document = Document.Create(fileName, fileSize, mimeType, fileHash, storagePath, uploadedBy);

        // Assert
        document.Should().NotBeNull();
        document.Id.Should().NotBeEmpty();
        document.FileName.Should().Be(fileName);
        document.FileSize.Should().Be(fileSize);
        document.MimeType.Should().Be(mimeType);
        document.FileHash.Should().Be(fileHash);
        document.StoragePath.Should().Be(storagePath);
        document.Status.Should().Be(DocumentStatus.Uploaded);
        document.CreatedBy.Should().Be(uploadedBy);
        document.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        document.ChunkCount.Should().Be(0);
        document.TotalTokens.Should().Be(0);
        document.EstimatedCost.Should().Be(0m);
        document.IsVectorized.Should().BeFalse();
        document.ProcessingSteps.Should().HaveCount(1);
        document.ProcessingSteps.First().StepName.Should().Be("Upload");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Create_WithInvalidFileName_ShouldThrowArgumentException(string invalidFileName)
    {
        // Arrange
        var fileSize = 1024L;
        var mimeType = "application/pdf";
        var fileHash = "abc123def456";
        var storagePath = "/storage/documents/test-document.pdf";
        var uploadedBy = "user123";

        // Act & Assert
        var action = () => Document.Create(invalidFileName, fileSize, mimeType, fileHash, storagePath, uploadedBy);
        action.Should().Throw<ArgumentException>().WithMessage("File name cannot be empty*");
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void Create_WithInvalidFileSize_ShouldThrowArgumentException(long invalidFileSize)
    {
        // Arrange
        var fileName = "test-document.pdf";
        var mimeType = "application/pdf";
        var fileHash = "abc123def456";
        var storagePath = "/storage/documents/test-document.pdf";
        var uploadedBy = "user123";

        // Act & Assert
        var action = () => Document.Create(fileName, invalidFileSize, mimeType, fileHash, storagePath, uploadedBy);
        action.Should().Throw<ArgumentException>().WithMessage("File size must be positive*");
    }

    [Fact]
    public void StartExtraction_WhenStatusIsUploaded_ShouldUpdateStatusAndAddStep()
    {
        // Arrange
        var document = CreateValidDocument();
        var agentName = "ExtractionAgent";

        // Act
        document.StartExtraction(agentName);

        // Assert
        document.Status.Should().Be(DocumentStatus.Extracting);
        document.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        document.ProcessingSteps.Should().HaveCount(2);
        document.ProcessingSteps.Last().StepName.Should().Be("Extraction");
        document.ProcessingSteps.Last().IsSuccessful.Should().BeTrue();
    }

    [Fact]
    public void StartExtraction_WhenStatusIsNotUploaded_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var document = CreateValidDocument();
        document.StartExtraction("ExtractionAgent");
        document.CompleteExtraction("Sample text", "ExtractionAgent", TimeSpan.FromSeconds(1));

        // Act & Assert
        var action = () => document.StartExtraction("ExtractionAgent");
        action.Should().Throw<InvalidOperationException>().WithMessage("Cannot start extraction. Current status: *");
    }

    [Fact]
    public void CompleteExtraction_WithValidText_ShouldUpdateDocumentAndStatus()
    {
        // Arrange
        var document = CreateValidDocument();
        document.StartExtraction("ExtractionAgent");
        var extractedText = "This is a sample legal document with important content.";
        var agentName = "ExtractionAgent";
        var extractionTime = TimeSpan.FromSeconds(2);

        // Act
        document.CompleteExtraction(extractedText, agentName, extractionTime);

        // Assert
        document.Status.Should().Be(DocumentStatus.Extracted);
        document.ExtractedText.Should().Be(extractedText);
        document.TotalTokens.Should().BeGreaterThan(0);
        document.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        document.ProcessingSteps.Last().IsSuccessful.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void CompleteExtraction_WithInvalidText_ShouldThrowArgumentException(string invalidText)
    {
        // Arrange
        var document = CreateValidDocument();
        document.StartExtraction("ExtractionAgent");
        var agentName = "ExtractionAgent";
        var extractionTime = TimeSpan.FromSeconds(2);

        // Act & Assert
        var action = () => document.CompleteExtraction(invalidText, agentName, extractionTime);
        action.Should().Throw<ArgumentException>().WithMessage("Extracted text cannot be empty*");
    }

    [Fact]
    public void StartClassification_WhenStatusIsExtracted_ShouldUpdateStatusAndAddStep()
    {
        // Arrange
        var document = CreateValidDocument();
        document.StartExtraction("ExtractionAgent");
        document.CompleteExtraction("Sample text", "ExtractionAgent", TimeSpan.FromSeconds(1));
        var agentName = "ClassificationAgent";

        // Act
        document.StartClassification(agentName);

        // Assert
        document.Status.Should().Be(DocumentStatus.Classifying);
        document.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        document.ProcessingSteps.Last().StepName.Should().Be("Classification");
    }

    [Fact]
    public void CompleteClassification_WithValidDomain_ShouldUpdateDocumentAndStatus()
    {
        // Arrange
        var document = CreateValidDocument();
        document.StartExtraction("ExtractionAgent");
        document.CompleteExtraction("Sample text", "ExtractionAgent", TimeSpan.FromSeconds(1));
        document.StartClassification("ClassificationAgent");
        var domain = LegalDomain.Commercial;
        var confidence = 0.85;
        var agentName = "ClassificationAgent";
        var classificationTime = TimeSpan.FromSeconds(3);

        // Act
        document.CompleteClassification(domain, confidence, agentName, classificationTime);

        // Assert
        document.Status.Should().Be(DocumentStatus.Classified);
        document.DetectedDomain.Should().Be(domain);
        document.ClassificationConfidence.Should().Be(confidence);
        document.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        document.ProcessingSteps.Last().IsSuccessful.Should().BeTrue();
    }

    [Theory]
    [InlineData(-0.1)]
    [InlineData(1.1)]
    public void CompleteClassification_WithInvalidConfidence_ShouldThrowArgumentException(double invalidConfidence)
    {
        // Arrange
        var document = CreateValidDocument();
        document.StartExtraction("ExtractionAgent");
        document.CompleteExtraction("Sample text", "ExtractionAgent", TimeSpan.FromSeconds(1));
        document.StartClassification("ClassificationAgent");
        var domain = LegalDomain.Commercial;
        var agentName = "ClassificationAgent";
        var classificationTime = TimeSpan.FromSeconds(3);

        // Act & Assert
        var action = () => document.CompleteClassification(domain, invalidConfidence, agentName, classificationTime);
        action.Should().Throw<ArgumentException>().WithMessage("Confidence must be between 0 and 1*");
    }

    [Fact]
    public void AddChunk_WithValidChunk_ShouldAddChunkAndUpdateCount()
    {
        // Arrange
        var document = CreateValidDocument();
        var chunk = DocumentChunk.Create(
            document.Id,
            0,
            "This is a sample chunk content.",
            ChunkType.Paragraph,
            0,
            32,
            "ChunkingAgent");

        // Act
        document.AddChunk(chunk);

        // Assert
        document.Chunks.Should().HaveCount(1);
        document.Chunks.First().Should().Be(chunk);
        document.ChunkCount.Should().Be(1);
        document.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void AddChunk_WithNullChunk_ShouldThrowArgumentNullException()
    {
        // Arrange
        var document = CreateValidDocument();

        // Act & Assert
        var action = () => document.AddChunk(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void AddChunk_WithMismatchedDocumentId_ShouldThrowArgumentException()
    {
        // Arrange
        var document = CreateValidDocument();
        var chunk = DocumentChunk.Create(
            Guid.NewGuid(), // Different document ID
            0,
            "This is a sample chunk content.",
            ChunkType.Paragraph,
            0,
            32,
            "ChunkingAgent");

        // Act & Assert
        var action = () => document.AddChunk(chunk);
        action.Should().Throw<ArgumentException>().WithMessage("Chunk document ID does not match*");
    }

    [Fact]
    public void MarkAsFailed_WithValidError_ShouldUpdateStatusAndAddError()
    {
        // Arrange
        var document = CreateValidDocument();
        var error = ProcessingError.Create(
            "TEST_ERROR",
            "Test error message",
            ErrorSeverity.High,
            "TestStep",
            "TestAgent");
        var agentName = "TestAgent";

        // Act
        document.MarkAsFailed(error, agentName);

        // Assert
        document.Status.Should().Be(DocumentStatus.Failed);
        document.Errors.Should().HaveCount(1);
        document.Errors.First().Should().Be(error);
        document.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        document.ProcessingSteps.Last().StepName.Should().Be("Error");
        document.ProcessingSteps.Last().IsSuccessful.Should().BeFalse();
    }

    [Fact]
    public void RetryProcessing_WhenStatusIsFailed_ShouldResetStatusAndClearErrors()
    {
        // Arrange
        var document = CreateValidDocument();
        var error = ProcessingError.Create(
            "TEST_ERROR",
            "Test error message",
            ErrorSeverity.High,
            "TestStep",
            "TestAgent");
        document.MarkAsFailed(error, "TestAgent");
        var fromStep = "extraction";
        var retriedBy = "user456";

        // Act
        document.RetryProcessing(fromStep, retriedBy);

        // Assert
        document.Status.Should().Be(DocumentStatus.Uploaded);
        document.Errors.Should().BeEmpty();
        document.UpdatedBy.Should().Be(retriedBy);
        document.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        document.ProcessingSteps.Last().StepName.Should().Be("Retry");
    }

    [Fact]
    public void RetryProcessing_WhenStatusIsNotFailed_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var document = CreateValidDocument();
        var fromStep = "extraction";
        var retriedBy = "user456";

        // Act & Assert
        var action = () => document.RetryProcessing(fromStep, retriedBy);
        action.Should().Throw<InvalidOperationException>().WithMessage("Cannot retry processing. Current status: *");
    }

    [Fact]
    public void IsReadyForStep_WithCorrectStatus_ShouldReturnTrue()
    {
        // Arrange
        var document = CreateValidDocument();

        // Act & Assert
        document.IsReadyForStep("extraction").Should().BeTrue();
        document.IsReadyForStep("classification").Should().BeFalse();
        document.IsReadyForStep("chunking").Should().BeFalse();
        document.IsReadyForStep("vectorization").Should().BeFalse();
    }

    [Fact]
    public void GetStatistics_ShouldReturnCorrectStatistics()
    {
        // Arrange
        var document = CreateValidDocument();
        document.StartExtraction("ExtractionAgent");
        document.CompleteExtraction("Sample text", "ExtractionAgent", TimeSpan.FromSeconds(1));

        // Act
        var statistics = document.GetStatistics();

        // Assert
        statistics.DocumentId.Should().Be(document.Id);
        statistics.FileName.Should().Be(document.FileName);
        statistics.FileSize.Should().Be(document.FileSize);
        statistics.Status.Should().Be(document.Status);
        statistics.ChunkCount.Should().Be(document.ChunkCount);
        statistics.TotalTokens.Should().Be(document.TotalTokens);
        statistics.StepCount.Should().Be(document.ProcessingSteps.Count);
        statistics.ErrorCount.Should().Be(document.Errors.Count);
        statistics.IsVectorized.Should().Be(document.IsVectorized);
    }

    private static Document CreateValidDocument()
    {
        return Document.Create(
            "test-document.pdf",
            1024L,
            "application/pdf",
            "abc123def456",
            "/storage/documents/test-document.pdf",
            "user123");
    }
}
