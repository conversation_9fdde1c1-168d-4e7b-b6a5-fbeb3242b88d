[{"ContainingType": "LexAI.DataPreprocessing.API.Controllers.DocumentsController", "Method": "GetUserDocuments", "RelativePath": "api/Documents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "limit", "Type": "System.Int32", "IsRequired": false}, {"Name": "offset", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.DataPreprocessing.API.Controllers.DocumentsController", "Method": "CancelDocumentProcessing", "RelativePath": "api/Documents/{documentId}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "LexAI.DataPreprocessing.API.Controllers.CancelProcessingRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.DataPreprocessing.API.Controllers.DocumentsController", "Method": "ProcessDocument", "RelativePath": "api/Documents/{documentId}/process", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Guid", "IsRequired": true}, {"Name": "configuration", "Type": "LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.DataPreprocessing.API.Controllers.DocumentsController", "Method": "RetryDocumentProcessing", "RelativePath": "api/Documents/{documentId}/retry", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "LexAI.DataPreprocessing.API.Controllers.RetryProcessingRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.DataPreprocessing.API.Controllers.DocumentsController", "Method": "GetProcessingStatus", "RelativePath": "api/Documents/{documentId}/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.DataPreprocessing.API.Controllers.DocumentsController", "Method": "UploadDocument", "RelativePath": "api/Documents/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 413}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 429}]}]