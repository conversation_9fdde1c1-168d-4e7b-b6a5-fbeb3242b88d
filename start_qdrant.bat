@echo off
echo ========================================
echo   Qdrant Vector Database - LexAI
echo ========================================
echo.

REM Vérifier si Docker est installé
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Docker n'est pas installé ou pas dans le PATH
    echo Veuillez installer Docker Desktop depuis https://docker.com
    pause
    exit /b 1
)

echo ✅ Docker détecté

REM Vérifier si Docker Compose est disponible
docker compose version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Docker Compose n'est pas disponible
    echo Veuillez mettre à jour Docker Desktop
    pause
    exit /b 1
)

echo ✅ Docker Compose détecté

echo.
echo 🚀 Démarrage de Qdrant...
echo 📍 HTTP API: http://localhost:6333
echo 📍 gRPC API: localhost:6334
echo 📍 Dashboard: http://localhost:6333/dashboard
echo.

REM Démarrer Qdrant avec Docker Compose
docker compose -f docker-compose.qdrant.yml up -d

if %errorlevel% equ 0 (
    echo.
    echo ✅ Qdrant démarré avec succès !
    echo.
    echo 📊 Vérification de l'état...
    timeout /t 5 /nobreak >nul
    
    REM Vérifier la santé du service
    curl -s http://localhost:6333/health >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Qdrant est opérationnel !
        echo 🌐 Dashboard disponible: http://localhost:6333/dashboard
    ) else (
        echo ⏳ Qdrant démarre... (peut prendre quelques secondes)
        echo 🔄 Vérifiez manuellement: http://localhost:6333/health
    )
    
    echo.
    echo 📋 Commandes utiles:
    echo   - Arrêter: docker compose -f docker-compose.qdrant.yml down
    echo   - Logs: docker compose -f docker-compose.qdrant.yml logs -f
    echo   - Redémarrer: docker compose -f docker-compose.qdrant.yml restart
    echo.
) else (
    echo ❌ Erreur lors du démarrage de Qdrant
    echo Vérifiez les logs: docker compose -f docker-compose.qdrant.yml logs
)

pause
