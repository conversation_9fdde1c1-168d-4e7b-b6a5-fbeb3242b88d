using LexAI.LegalResearch.Domain.ValueObjects;

namespace LexAI.LegalResearch.Application.DTOs;

/// <summary>
/// Document chunk DTO
/// </summary>
public class DocumentChunkDto
{
    /// <summary>
    /// Chunk ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Chunk content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Chunk type
    /// </summary>
    public ChunkType Type { get; set; }

    /// <summary>
    /// Sequence number
    /// </summary>
    public int SequenceNumber { get; set; }

    /// <summary>
    /// Start position in document
    /// </summary>
    public int StartPosition { get; set; }

    /// <summary>
    /// End position in document
    /// </summary>
    public int EndPosition { get; set; }

    /// <summary>
    /// Embedding vector
    /// </summary>
    public float[] EmbeddingVector { get; set; } = Array.Empty<float>();

    /// <summary>
    /// Keywords
    /// </summary>
    public List<string> Keywords { get; set; } = new();

    /// <summary>
    /// Metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Vector search result DTO
/// </summary>
public class VectorSearchResultDto
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Similarity score
    /// </summary>
    public double SimilarityScore { get; set; }

    /// <summary>
    /// Matched chunk
    /// </summary>
    public MatchedChunkDto? MatchedChunk { get; set; }

    /// <summary>
    /// Distance from query vector
    /// </summary>
    public double Distance { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Vector database statistics DTO
/// </summary>
public class VectorDatabaseStatsDto
{
    /// <summary>
    /// Total number of documents indexed
    /// </summary>
    public long TotalDocuments { get; set; }

    /// <summary>
    /// Total number of chunks stored
    /// </summary>
    public long TotalChunks { get; set; }

    /// <summary>
    /// Average chunks per document
    /// </summary>
    public double AverageChunksPerDocument { get; set; }

    /// <summary>
    /// Index size in bytes
    /// </summary>
    public long IndexSizeBytes { get; set; }

    /// <summary>
    /// Last indexing timestamp
    /// </summary>
    public DateTime? LastIndexed { get; set; }

    /// <summary>
    /// Embedding model used
    /// </summary>
    public string EmbeddingModel { get; set; } = string.Empty;

    /// <summary>
    /// Embedding dimension
    /// </summary>
    public int EmbeddingDimension { get; set; }
}

/// <summary>
/// Query analysis DTO
/// </summary>
public class QueryAnalysisDto
{
    /// <summary>
    /// Original query
    /// </summary>
    public string OriginalQuery { get; set; } = string.Empty;

    /// <summary>
    /// Processed query
    /// </summary>
    public string ProcessedQuery { get; set; } = string.Empty;

    /// <summary>
    /// Detected intent
    /// </summary>
    public QueryIntent Intent { get; set; }

    /// <summary>
    /// Extracted entities
    /// </summary>
    public List<LegalEntityDto> Entities { get; set; } = new();

    /// <summary>
    /// Expanded terms
    /// </summary>
    public List<string> ExpandedTerms { get; set; } = new();

    /// <summary>
    /// Confidence score
    /// </summary>
    public double ConfidenceScore { get; set; }

    /// <summary>
    /// Suggested filters
    /// </summary>
    public Dictionary<string, object> SuggestedFilters { get; set; } = new();
}

/// <summary>
/// Legal entity DTO
/// </summary>
public class LegalEntityDto
{
    /// <summary>
    /// Entity text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Entity type
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Start position in query
    /// </summary>
    public int StartPosition { get; set; }

    /// <summary>
    /// End position in query
    /// </summary>
    public int EndPosition { get; set; }

    /// <summary>
    /// Confidence score
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// User feedback DTO
/// </summary>
public class UserFeedbackDto
{
    /// <summary>
    /// Overall rating (1-5)
    /// </summary>
    public int OverallRating { get; set; }

    /// <summary>
    /// Relevance rating (1-5)
    /// </summary>
    public int RelevanceRating { get; set; }

    /// <summary>
    /// Completeness rating (1-5)
    /// </summary>
    public int CompletenessRating { get; set; }

    /// <summary>
    /// Usefulness rating (1-5)
    /// </summary>
    public int UsefulnessRating { get; set; }

    /// <summary>
    /// Additional comments
    /// </summary>
    public string? Comments { get; set; }

    /// <summary>
    /// Feedback timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Search analytics DTO
/// </summary>
public class SearchAnalyticsDto
{
    /// <summary>
    /// Total number of searches
    /// </summary>
    public int TotalSearches { get; set; }

    /// <summary>
    /// Average execution time in milliseconds
    /// </summary>
    public double AverageExecutionTime { get; set; }

    /// <summary>
    /// Average number of results per search
    /// </summary>
    public double AverageResultCount { get; set; }

    /// <summary>
    /// Most popular queries
    /// </summary>
    public List<string> TopQueries { get; set; } = new();

    /// <summary>
    /// Most searched legal domains
    /// </summary>
    public Dictionary<LegalDomain, int> TopDomains { get; set; } = new();

    /// <summary>
    /// Search trends over time
    /// </summary>
    public Dictionary<DateTime, int> SearchTrends { get; set; } = new();

    /// <summary>
    /// Success rate (searches with results)
    /// </summary>
    public double SuccessRate { get; set; }

    /// <summary>
    /// Average user satisfaction score
    /// </summary>
    public double AverageSatisfactionScore { get; set; }
}
