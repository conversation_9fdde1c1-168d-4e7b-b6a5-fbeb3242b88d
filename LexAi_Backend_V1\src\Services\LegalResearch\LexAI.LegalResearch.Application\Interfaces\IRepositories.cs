using LexAI.LegalResearch.Domain.Entities;
using LexAI.LegalResearch.Domain.ValueObjects;
using LexAI.Shared.Domain.Common;

namespace LexAI.LegalResearch.Application.Interfaces;

/// <summary>
/// Repository interface for legal documents
/// </summary>
public interface ILegalDocumentRepository
{
    /// <summary>
    /// Gets a document by ID
    /// </summary>
    /// <param name="id">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document or null if not found</returns>
    Task<LegalDocument?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new document
    /// </summary>
    /// <param name="document">Document to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task AddAsync(LegalDocument document, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates a document
    /// </summary>
    /// <param name="document">Document to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task UpdateAsync(LegalDocument document, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a document
    /// </summary>
    /// <param name="id">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets documents by legal domain
    /// </summary>
    /// <param name="domain">Legal domain</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Documents in the domain</returns>
    Task<IEnumerable<LegalDocument>> GetByDomainAsync(LegalDomain domain, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets documents by type
    /// </summary>
    /// <param name="type">Document type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Documents of the type</returns>
    Task<IEnumerable<LegalDocument>> GetByTypeAsync(DocumentType type, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets documents by source
    /// </summary>
    /// <param name="sourceName">Source name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Documents from the source</returns>
    Task<IEnumerable<LegalDocument>> GetBySourceAsync(string sourceName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets documents published within a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Documents published in the date range</returns>
    Task<IEnumerable<LegalDocument>> GetByPublicationDateRangeAsync(
        DateTime? startDate,
        DateTime? endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets documents that need indexing
    /// </summary>
    /// <param name="limit">Maximum number of documents</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Documents that need indexing</returns>
    Task<IEnumerable<LegalDocument>> GetUnindexedDocumentsAsync(int limit = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches documents by text content
    /// </summary>
    /// <param name="searchText">Search text</param>
    /// <param name="limit">Maximum number of results</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Matching documents</returns>
    Task<IEnumerable<LegalDocument>> SearchByContentAsync(
        string searchText,
        int limit = 50,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets documents with tags
    /// </summary>
    /// <param name="tags">Tags to search for</param>
    /// <param name="matchAll">Whether to match all tags or any tag</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Documents with matching tags</returns>
    Task<IEnumerable<LegalDocument>> GetByTagsAsync(
        IEnumerable<string> tags,
        bool matchAll = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets document statistics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document statistics</returns>
    Task<DocumentStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for search queries
/// </summary>
public interface ISearchQueryRepository
{
    /// <summary>
    /// Gets a search query by ID
    /// </summary>
    /// <param name="id">Query ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search query or null if not found</returns>
    Task<SearchQuery?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new search query
    /// </summary>
    /// <param name="query">Query to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task AddAsync(SearchQuery query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates a search query
    /// </summary>
    /// <param name="query">Query to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task UpdateAsync(SearchQuery query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a search query
    /// </summary>
    /// <param name="id">Query ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets search queries by user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="limit">Maximum number of queries</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User's search queries</returns>
    Task<IEnumerable<SearchQuery>> GetByUserAsync(Guid userId, int limit = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets search queries by session
    /// </summary>
    /// <param name="sessionId">Session ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Session's search queries</returns>
    Task<IEnumerable<SearchQuery>> GetBySessionAsync(string sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets search queries within a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search queries in the date range</returns>
    Task<IEnumerable<SearchQuery>> GetByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets most popular search queries
    /// </summary>
    /// <param name="limit">Maximum number of queries</param>
    /// <param name="timeRange">Time range for popularity calculation</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Popular search queries</returns>
    Task<IEnumerable<SearchQuery>> GetPopularQueriesAsync(
        int limit = 10,
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets search queries with feedback
    /// </summary>
    /// <param name="minRating">Minimum feedback rating</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search queries with feedback</returns>
    Task<IEnumerable<SearchQuery>> GetQueriesWithFeedbackAsync(
        int minRating = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets search analytics
    /// </summary>
    /// <param name="userId">Optional user ID filter</param>
    /// <param name="startDate">Start date for analytics</param>
    /// <param name="endDate">End date for analytics</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search analytics</returns>
    Task<SearchAnalytics> GetAnalyticsAsync(
        Guid? userId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Document statistics model
/// </summary>
public class DocumentStatistics
{
    /// <summary>
    /// Total number of documents
    /// </summary>
    public int TotalDocuments { get; set; }

    /// <summary>
    /// Number of indexed documents
    /// </summary>
    public int IndexedDocuments { get; set; }

    /// <summary>
    /// Number of documents by type
    /// </summary>
    public Dictionary<DocumentType, int> DocumentsByType { get; set; } = new();

    /// <summary>
    /// Number of documents by domain
    /// </summary>
    public Dictionary<LegalDomain, int> DocumentsByDomain { get; set; } = new();

    /// <summary>
    /// Number of documents by source
    /// </summary>
    public Dictionary<string, int> DocumentsBySource { get; set; } = new();

    /// <summary>
    /// Average document length in characters
    /// </summary>
    public double AverageDocumentLength { get; set; }

    /// <summary>
    /// Total storage size in bytes
    /// </summary>
    public long TotalStorageSize { get; set; }

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Search analytics model
/// </summary>
public class SearchAnalytics
{
    /// <summary>
    /// Total number of searches
    /// </summary>
    public int TotalSearches { get; set; }

    /// <summary>
    /// Number of successful searches
    /// </summary>
    public int SuccessfulSearches { get; set; }

    /// <summary>
    /// Average execution time in milliseconds
    /// </summary>
    public double AverageExecutionTime { get; set; }

    /// <summary>
    /// Average number of results per search
    /// </summary>
    public double AverageResultCount { get; set; }

    /// <summary>
    /// Most popular search terms
    /// </summary>
    public Dictionary<string, int> PopularTerms { get; set; } = new();

    /// <summary>
    /// Search trends by date
    /// </summary>
    public Dictionary<DateTime, int> SearchTrends { get; set; } = new();

    /// <summary>
    /// Average user satisfaction score
    /// </summary>
    public double AverageSatisfactionScore { get; set; }

    /// <summary>
    /// Number of searches by domain
    /// </summary>
    public Dictionary<LegalDomain, int> SearchesByDomain { get; set; } = new();

    /// <summary>
    /// Number of searches by method
    /// </summary>
    public Dictionary<SearchMethod, int> SearchesByMethod { get; set; } = new();

    /// <summary>
    /// Analytics generation timestamp
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}
