using LexAI.LegalResearch.Application.DTOs;
using LexAI.LegalResearch.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net.Http.Json;
using System.Text.Json;

namespace LexAI.LegalResearch.Infrastructure.Services;

/// <summary>
/// Qdrant vector database service implementation
/// </summary>
public class QdrantVectorDatabaseService : IVectorDatabaseService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<QdrantVectorDatabaseService> _logger;
    private readonly string _baseUrl;
    private readonly string _defaultCollection;

    /// <summary>
    /// Initializes a new instance of the QdrantVectorDatabaseService
    /// </summary>
    /// <param name="httpClient">HTTP client</param>
    /// <param name="configuration">Configuration</param>
    /// <param name="logger">Logger</param>
    public QdrantVectorDatabaseService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<QdrantVectorDatabaseService> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _baseUrl = configuration["DataProcessing:BaseUrl"] ?? "http://localhost:5001";
        _defaultCollection = configuration["Qdrant:DefaultCollection"] ?? "legal-documents";
    }

    /// <summary>
    /// Stores document chunks with embeddings in vector database
    /// </summary>
    /// <param name="chunks">Document chunks with embeddings</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task StoreChunksAsync(IEnumerable<DocumentChunkDto> chunks, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Storing {ChunkCount} chunks in vector database", chunks.Count());

        try
        {
            // For now, we'll use the DataProcessing service to store chunks
            // This would be called when documents are processed
            await Task.CompletedTask;
            
            _logger.LogInformation("Successfully stored {ChunkCount} chunks", chunks.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing chunks in vector database");
            throw;
        }
    }

    /// <summary>
    /// Performs vector similarity search
    /// </summary>
    /// <param name="queryVector">Query embedding vector</param>
    /// <param name="limit">Maximum number of results</param>
    /// <param name="threshold">Minimum similarity threshold</param>
    /// <param name="filters">Optional filters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Similar chunks with scores</returns>
    public async Task<IEnumerable<VectorSearchResultDto>> SearchSimilarAsync(
        float[] queryVector, 
        int limit = 10, 
        double threshold = 0.7,
        Dictionary<string, object>? filters = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Performing vector similarity search with limit {Limit} and threshold {Threshold}", limit, threshold);

        try
        {
            // Call DataProcessing service for vector search
            var searchRequest = new
            {
                vector = queryVector,
                limit = limit,
                threshold = threshold,
                filters = filters ?? new Dictionary<string, object>()
            };

            var response = await _httpClient.PostAsJsonAsync(
                $"{_baseUrl}/api/search/vector", 
                searchRequest, 
                cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var results = JsonSerializer.Deserialize<List<VectorSearchResultDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                _logger.LogInformation("Vector search returned {ResultCount} results", results?.Count ?? 0);
                return results ?? new List<VectorSearchResultDto>();
            }
            else
            {
                _logger.LogWarning("Vector search failed with status code: {StatusCode}", response.StatusCode);
                return new List<VectorSearchResultDto>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing vector similarity search");
            return new List<VectorSearchResultDto>();
        }
    }

    /// <summary>
    /// Updates embeddings for a document
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="chunks">Updated chunks</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task UpdateDocumentEmbeddingsAsync(Guid documentId, IEnumerable<DocumentChunkDto> chunks, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Updating embeddings for document: {DocumentId}", documentId);

        try
        {
            // Delete existing embeddings
            await DeleteDocumentEmbeddingsAsync(documentId, cancellationToken);
            
            // Store new embeddings
            await StoreChunksAsync(chunks, cancellationToken);
            
            _logger.LogInformation("Successfully updated embeddings for document: {DocumentId}", documentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating embeddings for document: {DocumentId}", documentId);
            throw;
        }
    }

    /// <summary>
    /// Deletes document embeddings from vector database
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task DeleteDocumentEmbeddingsAsync(Guid documentId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Deleting embeddings for document: {DocumentId}", documentId);

        try
        {
            var response = await _httpClient.DeleteAsync(
                $"{_baseUrl}/api/documents/{documentId}/embeddings", 
                cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully deleted embeddings for document: {DocumentId}", documentId);
            }
            else
            {
                _logger.LogWarning("Failed to delete embeddings for document: {DocumentId}, Status: {StatusCode}", 
                    documentId, response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting embeddings for document: {DocumentId}", documentId);
            throw;
        }
    }

    /// <summary>
    /// Gets vector database statistics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Database statistics</returns>
    public async Task<VectorDatabaseStatsDto> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting vector database statistics");

        try
        {
            var response = await _httpClient.GetAsync(
                $"{_baseUrl}/api/search/stats", 
                cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var stats = JsonSerializer.Deserialize<VectorDatabaseStatsDto>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return stats ?? new VectorDatabaseStatsDto();
            }
            else
            {
                _logger.LogWarning("Failed to get vector database stats, Status: {StatusCode}", response.StatusCode);
                return new VectorDatabaseStatsDto();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vector database statistics");
            return new VectorDatabaseStatsDto
            {
                TotalDocuments = 0,
                TotalChunks = 0,
                AverageChunksPerDocument = 0,
                IndexSizeBytes = 0,
                EmbeddingModel = "unknown",
                EmbeddingDimension = 384
            };
        }
    }
}
