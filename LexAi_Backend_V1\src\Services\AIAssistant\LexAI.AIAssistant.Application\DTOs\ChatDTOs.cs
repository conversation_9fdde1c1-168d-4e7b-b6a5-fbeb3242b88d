using LexAI.AIAssistant.Domain.ValueObjects;

namespace LexAI.AIAssistant.Application.DTOs;

/// <summary>
/// Chat request DTO
/// </summary>
public class ChatRequestDto
{
    /// <summary>
    /// User message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Session ID
    /// </summary>
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// Conversation ID (optional for new conversations)
    /// </summary>
    public Guid? ConversationId { get; set; }

    /// <summary>
    /// Conversation context
    /// </summary>
    public ConversationContextDto? Context { get; set; }

    /// <summary>
    /// Whether to include legal research
    /// </summary>
    public bool IncludeLegalResearch { get; set; } = true;

    /// <summary>
    /// Whether to include citations
    /// </summary>
    public bool IncludeCitations { get; set; } = true;

    /// <summary>
    /// Message attachments
    /// </summary>
    public List<MessageAttachmentDto> Attachments { get; set; } = new();

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Chat response DTO
/// </summary>
public class ChatResponseDto
{
    /// <summary>
    /// Conversation ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// AI response message
    /// </summary>
    public string Response { get; set; } = string.Empty;

    /// <summary>
    /// Message ID
    /// </summary>
    public Guid MessageId { get; set; }

    /// <summary>
    /// Response type
    /// </summary>
    public MessageType ResponseType { get; set; }

    /// <summary>
    /// Detected intent
    /// </summary>
    public MessageIntent? DetectedIntent { get; set; }

    /// <summary>
    /// Detected legal domain
    /// </summary>
    public LegalDomain? DetectedDomain { get; set; }

    /// <summary>
    /// Confidence score
    /// </summary>
    public double? ConfidenceScore { get; set; }

    /// <summary>
    /// Citations used in the response
    /// </summary>
    public List<CitationDto> Citations { get; set; } = new();

    /// <summary>
    /// Related legal documents
    /// </summary>
    public List<LegalDocumentSummaryDto> RelatedDocuments { get; set; } = new();

    /// <summary>
    /// Follow-up questions
    /// </summary>
    public List<string> FollowUpQuestions { get; set; } = new();

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// Tokens used
    /// </summary>
    public int TokensUsed { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// Response quality score
    /// </summary>
    public ResponseQuality Quality { get; set; }

    /// <summary>
    /// Whether the response was cached
    /// </summary>
    public bool IsCached { get; set; }

    /// <summary>
    /// Response metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Conversation context DTO
/// </summary>
public class ConversationContextDto
{
    /// <summary>
    /// AI model type
    /// </summary>
    public AIModelType ModelType { get; set; } = AIModelType.GPT4Turbo;

    /// <summary>
    /// Conversation mode
    /// </summary>
    public ConversationMode Mode { get; set; } = ConversationMode.Standard;

    /// <summary>
    /// System prompt
    /// </summary>
    public string? SystemPrompt { get; set; }

    /// <summary>
    /// Maximum tokens
    /// </summary>
    public int MaxTokens { get; set; } = 4000;

    /// <summary>
    /// Temperature
    /// </summary>
    public double Temperature { get; set; } = 0.7;

    /// <summary>
    /// Include legal research
    /// </summary>
    public bool IncludeLegalResearch { get; set; } = true;

    /// <summary>
    /// Include citations
    /// </summary>
    public bool IncludeCitations { get; set; } = true;

    /// <summary>
    /// Language
    /// </summary>
    public string Language { get; set; } = "fr";

    /// <summary>
    /// Jurisdiction
    /// </summary>
    public string? Jurisdiction { get; set; }

    /// <summary>
    /// User role
    /// </summary>
    public string? UserRole { get; set; }

    /// <summary>
    /// Additional preferences
    /// </summary>
    public Dictionary<string, object> Preferences { get; set; } = new();
}

/// <summary>
/// Message DTO
/// </summary>
public class MessageDto
{
    /// <summary>
    /// Message ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Conversation ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// Message content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Message role
    /// </summary>
    public MessageRole Role { get; set; }

    /// <summary>
    /// Message type
    /// </summary>
    public MessageType Type { get; set; }

    /// <summary>
    /// Message status
    /// </summary>
    public MessageStatus Status { get; set; }

    /// <summary>
    /// Detected domain
    /// </summary>
    public LegalDomain? DetectedDomain { get; set; }

    /// <summary>
    /// Detected intent
    /// </summary>
    public MessageIntent? DetectedIntent { get; set; }

    /// <summary>
    /// Confidence score
    /// </summary>
    public double? ConfidenceScore { get; set; }

    /// <summary>
    /// Citations
    /// </summary>
    public List<CitationDto> Citations { get; set; } = new();

    /// <summary>
    /// Attachments
    /// </summary>
    public List<MessageAttachmentDto> Attachments { get; set; } = new();

    /// <summary>
    /// Tokens used
    /// </summary>
    public int TokensUsed { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// Processing time
    /// </summary>
    public TimeSpan? ProcessingTime { get; set; }

    /// <summary>
    /// User rating
    /// </summary>
    public int? UserRating { get; set; }

    /// <summary>
    /// User feedback
    /// </summary>
    public string? UserFeedback { get; set; }

    /// <summary>
    /// Created timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// Whether message is edited
    /// </summary>
    public bool IsEdited { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Citation DTO
/// </summary>
public class CitationDto
{
    /// <summary>
    /// Citation ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Citation type
    /// </summary>
    public CitationType Type { get; set; }

    /// <summary>
    /// Document title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Document URL
    /// </summary>
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// Source name
    /// </summary>
    public string Source { get; set; } = string.Empty;

    /// <summary>
    /// Publication date
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Relevance score
    /// </summary>
    public double RelevanceScore { get; set; }

    /// <summary>
    /// Excerpt from the document
    /// </summary>
    public string? Excerpt { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Message attachment DTO
/// </summary>
public class MessageAttachmentDto
{
    /// <summary>
    /// Attachment ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// File name
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// MIME type
    /// </summary>
    public string MimeType { get; set; } = string.Empty;

    /// <summary>
    /// Attachment type
    /// </summary>
    public AttachmentType Type { get; set; }

    /// <summary>
    /// File URL or path
    /// </summary>
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// File content (for small files)
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// Upload timestamp
    /// </summary>
    public DateTime UploadedAt { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Message analysis DTO
/// </summary>
public class MessageAnalysisDto
{
    /// <summary>
    /// Original message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Detected intent
    /// </summary>
    public MessageIntent Intent { get; set; }

    /// <summary>
    /// Detected legal domain
    /// </summary>
    public LegalDomain? Domain { get; set; }

    /// <summary>
    /// Confidence score
    /// </summary>
    public double ConfidenceScore { get; set; }

    /// <summary>
    /// Extracted entities
    /// </summary>
    public List<ExtractedEntityDto> Entities { get; set; } = new();

    /// <summary>
    /// Suggested response type
    /// </summary>
    public MessageType SuggestedResponseType { get; set; }

    /// <summary>
    /// Complexity score
    /// </summary>
    public double ComplexityScore { get; set; }

    /// <summary>
    /// Requires legal research
    /// </summary>
    public bool RequiresLegalResearch { get; set; }

    /// <summary>
    /// Analysis metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Extracted entity DTO
/// </summary>
public class ExtractedEntityDto
{
    /// <summary>
    /// Entity text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Entity type
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Start position
    /// </summary>
    public int StartPosition { get; set; }

    /// <summary>
    /// End position
    /// </summary>
    public int EndPosition { get; set; }

    /// <summary>
    /// Confidence score
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Legal document summary DTO
/// </summary>
public class LegalDocumentSummaryDto
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Document title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Document summary
    /// </summary>
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// Document URL
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// Source name
    /// </summary>
    public string Source { get; set; } = string.Empty;

    /// <summary>
    /// Publication date
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Relevance score
    /// </summary>
    public double RelevanceScore { get; set; }

    /// <summary>
    /// Legal domain
    /// </summary>
    public LegalDomain Domain { get; set; }
}

/// <summary>
/// Legal document details DTO
/// </summary>
public class LegalDocumentDetailsDto
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Document title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Document content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Document summary
    /// </summary>
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// Document URL
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// Source information
    /// </summary>
    public string Source { get; set; } = string.Empty;

    /// <summary>
    /// Publication date
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Legal domain
    /// </summary>
    public LegalDomain Domain { get; set; }

    /// <summary>
    /// Document tags
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Keywords
    /// </summary>
    public List<string> Keywords { get; set; } = new();

    /// <summary>
    /// Document metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}
