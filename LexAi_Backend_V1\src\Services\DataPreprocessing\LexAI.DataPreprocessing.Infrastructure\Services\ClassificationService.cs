using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace LexAI.DataPreprocessing.Infrastructure.Services;

/// <summary>
/// Classification service implementation
/// </summary>
public class ClassificationService : IClassificationService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ClassificationService> _logger;
    private readonly string _apiKey;

    /// <summary>
    /// Initializes a new instance of the ClassificationService
    /// </summary>
    /// <param name="httpClient">HTTP client</param>
    /// <param name="configuration">Configuration</param>
    /// <param name="logger">Logger</param>
    public ClassificationService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<ClassificationService> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _apiKey = configuration["OpenAI:ApiKey"] ?? throw new InvalidOperationException("OpenAI API key not configured");

        _httpClient.BaseAddress = new Uri("https://api.openai.com/v1/");
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
    }

    /// <summary>
    /// Classifies text into legal domains
    /// </summary>
    /// <param name="text">Text to classify</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Classification result</returns>
    public async Task<ClassificationResult> ClassifyTextAsync(string text, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Classifying text into legal domains. Text length: {Length}", text.Length);

        try
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return new ClassificationResult
                {
                    Success = false,
                    ErrorMessage = "Text is empty or null"
                };
            }

            // First, try rule-based classification for quick results
            var ruleBasedResult = ClassifyUsingRules(text);
            
            // If rule-based classification has high confidence, use it
            if (ruleBasedResult.Confidence > 0.8)
            {
                _logger.LogInformation("Rule-based classification successful with high confidence: {Domain} ({Confidence:F2})", 
                    ruleBasedResult.DetectedDomain, ruleBasedResult.Confidence);
                return ruleBasedResult;
            }

            // Otherwise, use AI-based classification for better accuracy
            var aiResult = await ClassifyUsingAIAsync(text, cancellationToken);
            
            if (aiResult.Success)
            {
                _logger.LogInformation("AI-based classification successful: {Domain} ({Confidence:F2})", 
                    aiResult.DetectedDomain, aiResult.Confidence);
                return aiResult;
            }

            // Fallback to rule-based result if AI fails
            _logger.LogWarning("AI classification failed, using rule-based result: {Domain} ({Confidence:F2})", 
                ruleBasedResult.DetectedDomain, ruleBasedResult.Confidence);
            return ruleBasedResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error classifying text");
            return new ClassificationResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Extracts keywords from text
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="maxKeywords">Maximum keywords to extract</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extracted keywords</returns>
    public async Task<IEnumerable<string>> ExtractKeywordsAsync(string text, int maxKeywords = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(text))
                return Enumerable.Empty<string>();

            // Use a combination of rule-based and frequency-based keyword extraction
            var keywords = new List<string>();

            // 1. Extract legal terms using predefined patterns
            keywords.AddRange(ExtractLegalTerms(text));

            // 2. Extract important words using frequency analysis
            keywords.AddRange(ExtractFrequentWords(text, maxKeywords / 2));

            // 3. Extract named entities that look like legal concepts
            keywords.AddRange(ExtractLegalConcepts(text));

            // Remove duplicates and limit to maxKeywords
            var uniqueKeywords = keywords
                .Distinct(StringComparer.OrdinalIgnoreCase)
                .Take(maxKeywords)
                .ToList();

            _logger.LogDebug("Extracted {KeywordCount} keywords from text", uniqueKeywords.Count);
            
            return uniqueKeywords;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting keywords");
            return Enumerable.Empty<string>();
        }
    }

    /// <summary>
    /// Extracts named entities from text
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Named entities</returns>
    public async Task<IEnumerable<NamedEntity>> ExtractNamedEntitiesAsync(string text, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(text))
                return Enumerable.Empty<NamedEntity>();

            var entities = new List<NamedEntity>();

            // Extract different types of entities using regex patterns
            entities.AddRange(ExtractPersonNames(text));
            entities.AddRange(ExtractOrganizations(text));
            entities.AddRange(ExtractDates(text));
            entities.AddRange(ExtractMonetaryAmounts(text));
            entities.AddRange(ExtractLegalReferences(text));
            entities.AddRange(ExtractCaseNumbers(text));
            entities.AddRange(ExtractCourts(text));

            _logger.LogDebug("Extracted {EntityCount} named entities from text", entities.Count);
            
            return entities;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting named entities");
            return Enumerable.Empty<NamedEntity>();
        }
    }

    private ClassificationResult ClassifyUsingRules(string text)
    {
        var lowerText = text.ToLowerInvariant();
        var domainScores = new Dictionary<LegalDomain, double>();

        // Initialize all domains with base score
        foreach (var domain in Enum.GetValues<LegalDomain>())
        {
            domainScores[domain] = 0.0;
        }

        // Commercial law indicators
        var commercialTerms = new[] { "contrat", "société", "commercial", "entreprise", "vente", "achat", "facture", "commande", "livraison", "sasu", "sarl", "sa" };
        domainScores[LegalDomain.Commercial] = CalculateTermScore(lowerText, commercialTerms);

        // Labor law indicators
        var laborTerms = new[] { "travail", "salarié", "emploi", "licenciement", "démission", "congé", "salaire", "convention collective", "prud'hommes" };
        domainScores[LegalDomain.Labor] = CalculateTermScore(lowerText, laborTerms);

        // Civil law indicators
        var civilTerms = new[] { "civil", "responsabilité", "dommage", "préjudice", "réparation", "faute", "négligence", "indemnisation" };
        domainScores[LegalDomain.Civil] = CalculateTermScore(lowerText, civilTerms);

        // Criminal law indicators
        var criminalTerms = new[] { "pénal", "criminel", "délit", "crime", "infraction", "tribunal correctionnel", "cour d'assises", "procureur" };
        domainScores[LegalDomain.Criminal] = CalculateTermScore(lowerText, criminalTerms);

        // Tax law indicators
        var taxTerms = new[] { "impôt", "fiscal", "taxe", "tva", "is", "ir", "contribution", "redevance", "administration fiscale" };
        domainScores[LegalDomain.Tax] = CalculateTermScore(lowerText, taxTerms);

        // Real estate indicators
        var realEstateTerms = new[] { "immobilier", "propriété", "bail", "location", "vente immobilière", "notaire", "hypothèque", "servitude" };
        domainScores[LegalDomain.RealEstate] = CalculateTermScore(lowerText, realEstateTerms);

        // Family law indicators
        var familyTerms = new[] { "famille", "mariage", "divorce", "séparation", "garde", "pension alimentaire", "autorité parentale", "adoption" };
        domainScores[LegalDomain.Family] = CalculateTermScore(lowerText, familyTerms);

        // Administrative law indicators
        var adminTerms = new[] { "administratif", "préfet", "maire", "conseil d'état", "tribunal administratif", "service public", "fonction publique" };
        domainScores[LegalDomain.Administrative] = CalculateTermScore(lowerText, adminTerms);

        // Constitutional law indicators
        var constitutionalTerms = new[] { "constitution", "constitutionnel", "conseil constitutionnel", "droits fondamentaux", "liberté", "égalité" };
        domainScores[LegalDomain.Constitutional] = CalculateTermScore(lowerText, constitutionalTerms);

        // Find the domain with the highest score
        var bestDomain = domainScores.OrderByDescending(kvp => kvp.Value).First();
        var confidence = Math.Min(1.0, bestDomain.Value);

        return new ClassificationResult
        {
            DetectedDomain = bestDomain.Key,
            Confidence = confidence,
            DomainScores = domainScores,
            Success = true
        };
    }

    private async Task<ClassificationResult> ClassifyUsingAIAsync(string text, CancellationToken cancellationToken)
    {
        try
        {
            // Truncate text if too long for API
            var truncatedText = text.Length > 3000 ? text.Substring(0, 3000) + "..." : text;

            var prompt = $@"Classify the following French legal text into one of these legal domains:
- Commercial (droit commercial)
- Labor (droit du travail)
- Civil (droit civil)
- Criminal (droit pénal)
- Administrative (droit administratif)
- Constitutional (droit constitutionnel)
- Tax (droit fiscal)
- RealEstate (droit immobilier)
- Family (droit de la famille)
- IntellectualProperty (propriété intellectuelle)
- Environmental (droit de l'environnement)
- Health (droit de la santé)
- Immigration (droit de l'immigration)
- International (droit international)
- European (droit européen)
- Banking (droit bancaire)
- Insurance (droit des assurances)
- Technology (droit du numérique)
- Competition (droit de la concurrence)
- Consumer (droit de la consommation)
- Other (autre)

Text: {truncatedText}

Respond with only the domain name and a confidence score (0-1), separated by a comma. Example: Commercial,0.85";

            var requestBody = new
            {
                model = "gpt-3.5-turbo",
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = 50,
                temperature = 0.1
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("chat/completions", content, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                return new ClassificationResult
                {
                    Success = false,
                    ErrorMessage = $"AI classification failed: {response.StatusCode}"
                };
            }

            var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
            var chatResponse = JsonSerializer.Deserialize<ChatCompletionResponse>(responseJson);

            if (chatResponse?.Choices?.Length > 0)
            {
                var result = chatResponse.Choices[0].Message.Content.Trim();
                var parts = result.Split(',');

                if (parts.Length >= 2 && 
                    Enum.TryParse<LegalDomain>(parts[0].Trim(), out var domain) &&
                    double.TryParse(parts[1].Trim(), out var confidence))
                {
                    return new ClassificationResult
                    {
                        DetectedDomain = domain,
                        Confidence = confidence,
                        DomainScores = new Dictionary<LegalDomain, double> { [domain] = confidence },
                        Success = true
                    };
                }
            }

            return new ClassificationResult
            {
                Success = false,
                ErrorMessage = "Could not parse AI classification response"
            };
        }
        catch (Exception ex)
        {
            return new ClassificationResult
            {
                Success = false,
                ErrorMessage = $"AI classification error: {ex.Message}"
            };
        }
    }

    private static double CalculateTermScore(string text, string[] terms)
    {
        var score = 0.0;
        var totalWords = text.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;

        foreach (var term in terms)
        {
            var matches = Regex.Matches(text, $@"\b{Regex.Escape(term)}\b", RegexOptions.IgnoreCase);
            score += matches.Count * (1.0 + term.Length / 10.0); // Longer terms get slightly higher weight
        }

        // Normalize by text length
        return totalWords > 0 ? Math.Min(1.0, score / Math.Sqrt(totalWords)) : 0.0;
    }

    private static IEnumerable<string> ExtractLegalTerms(string text)
    {
        var legalTerms = new[]
        {
            "contrat", "accord", "convention", "protocole", "avenant",
            "responsabilité", "obligation", "droit", "devoir", "prérogative",
            "tribunal", "cour", "juridiction", "instance", "procédure",
            "loi", "décret", "arrêté", "ordonnance", "règlement",
            "article", "alinéa", "paragraphe", "section", "chapitre",
            "jurisprudence", "précédent", "doctrine", "commentaire"
        };

        var foundTerms = new List<string>();
        var lowerText = text.ToLowerInvariant();

        foreach (var term in legalTerms)
        {
            if (lowerText.Contains(term))
            {
                foundTerms.Add(term);
            }
        }

        return foundTerms;
    }

    private static IEnumerable<string> ExtractFrequentWords(string text, int maxWords)
    {
        var words = Regex.Matches(text.ToLowerInvariant(), @"\b[a-zàâäéèêëïîôöùûüÿç]{4,}\b")
            .Cast<Match>()
            .Select(m => m.Value)
            .Where(w => !IsStopWord(w))
            .GroupBy(w => w)
            .OrderByDescending(g => g.Count())
            .Take(maxWords)
            .Select(g => g.Key);

        return words;
    }

    private static IEnumerable<string> ExtractLegalConcepts(string text)
    {
        var concepts = new List<string>();
        
        // Extract capitalized phrases that might be legal concepts
        var matches = Regex.Matches(text, @"\b[A-ZÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ][a-zàâäéèêëïîôöùûüÿç]+(?:\s+[A-ZÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ][a-zàâäéèêëïîôöùûüÿç]+)*\b");
        
        foreach (Match match in matches)
        {
            var concept = match.Value;
            if (concept.Length > 3 && concept.Length < 50)
            {
                concepts.Add(concept);
            }
        }

        return concepts.Distinct();
    }

    private static bool IsStopWord(string word)
    {
        var stopWords = new[]
        {
            "le", "la", "les", "un", "une", "des", "du", "de", "et", "ou", "mais", "donc", "car", "ni", "or",
            "que", "qui", "quoi", "dont", "où", "quand", "comment", "pourquoi", "pour", "par", "avec", "sans",
            "dans", "sur", "sous", "entre", "parmi", "selon", "vers", "chez", "depuis", "pendant", "avant",
            "après", "être", "avoir", "faire", "dire", "aller", "voir", "savoir", "pouvoir", "vouloir",
            "cette", "celui", "celle", "ceux", "celles", "son", "sa", "ses", "leur", "leurs", "notre",
            "votre", "mon", "ma", "mes", "ton", "ta", "tes"
        };

        return stopWords.Contains(word);
    }

    private static IEnumerable<NamedEntity> ExtractPersonNames(string text)
    {
        // Simple pattern for French names (capitalized words)
        var pattern = @"\b[A-ZÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ][a-zàâäéèêëïîôöùûüÿç]+\s+[A-ZÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ][A-ZÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ]+\b";
        var matches = Regex.Matches(text, pattern);

        return matches.Cast<Match>().Select(m => NamedEntity.Create(
            m.Value,
            EntityType.Person,
            m.Index,
            m.Index + m.Length,
            0.6 // Medium confidence for simple pattern matching
        ));
    }

    private static IEnumerable<NamedEntity> ExtractOrganizations(string text)
    {
        var patterns = new[]
        {
            @"\b[A-ZÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ][a-zàâäéèêëïîôöùûüÿç]+\s+(?:SA|SARL|SAS|SASU|SNC|SCS|EURL)\b",
            @"\b(?:Société|Entreprise|Compagnie|Groupe)\s+[A-ZÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ][a-zàâäéèêëïîôöùûüÿç]+\b"
        };

        var entities = new List<NamedEntity>();

        foreach (var pattern in patterns)
        {
            var matches = Regex.Matches(text, pattern);
            entities.AddRange(matches.Cast<Match>().Select(m => NamedEntity.Create(
                m.Value,
                EntityType.Organization,
                m.Index,
                m.Index + m.Length,
                0.7
            )));
        }

        return entities;
    }

    private static IEnumerable<NamedEntity> ExtractDates(string text)
    {
        var patterns = new[]
        {
            @"\b\d{1,2}[/\-\.]\d{1,2}[/\-\.]\d{2,4}\b",
            @"\b\d{1,2}\s+(?:janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s+\d{4}\b"
        };

        var entities = new List<NamedEntity>();

        foreach (var pattern in patterns)
        {
            var matches = Regex.Matches(text, pattern, RegexOptions.IgnoreCase);
            entities.AddRange(matches.Cast<Match>().Select(m => NamedEntity.Create(
                m.Value,
                EntityType.Date,
                m.Index,
                m.Index + m.Length,
                0.8
            )));
        }

        return entities;
    }

    private static IEnumerable<NamedEntity> ExtractMonetaryAmounts(string text)
    {
        var patterns = new[]
        {
            @"\b\d+(?:[,\.]\d+)?\s*(?:€|euros?|EUR)\b",
            @"\b(?:€|euros?)\s*\d+(?:[,\.]\d+)?\b"
        };

        var entities = new List<NamedEntity>();

        foreach (var pattern in patterns)
        {
            var matches = Regex.Matches(text, pattern, RegexOptions.IgnoreCase);
            entities.AddRange(matches.Cast<Match>().Select(m => NamedEntity.Create(
                m.Value,
                EntityType.Money,
                m.Index,
                m.Index + m.Length,
                0.9
            )));
        }

        return entities;
    }

    private static IEnumerable<NamedEntity> ExtractLegalReferences(string text)
    {
        var patterns = new[]
        {
            @"\b(?:Article|Art\.?)\s+\d+(?:-\d+)?\b",
            @"\b(?:Code civil|Code pénal|Code du travail|Code de commerce)\b",
            @"\bL\.\s*\d+(?:-\d+)*\b",
            @"\bR\.\s*\d+(?:-\d+)*\b"
        };

        var entities = new List<NamedEntity>();

        foreach (var pattern in patterns)
        {
            var matches = Regex.Matches(text, pattern, RegexOptions.IgnoreCase);
            entities.AddRange(matches.Cast<Match>().Select(m => NamedEntity.Create(
                m.Value,
                EntityType.LegalReference,
                m.Index,
                m.Index + m.Length,
                0.9
            )));
        }

        return entities;
    }

    private static IEnumerable<NamedEntity> ExtractCaseNumbers(string text)
    {
        var patterns = new[]
        {
            @"\b\d{2}/\d{5}\b",
            @"\bn°\s*\d+(?:/\d+)*\b",
            @"\bRG\s*\d+/\d+\b"
        };

        var entities = new List<NamedEntity>();

        foreach (var pattern in patterns)
        {
            var matches = Regex.Matches(text, pattern, RegexOptions.IgnoreCase);
            entities.AddRange(matches.Cast<Match>().Select(m => NamedEntity.Create(
                m.Value,
                EntityType.CaseNumber,
                m.Index,
                m.Index + m.Length,
                0.8
            )));
        }

        return entities;
    }

    private static IEnumerable<NamedEntity> ExtractCourts(string text)
    {
        var patterns = new[]
        {
            @"\b(?:Tribunal|Cour)\s+(?:de grande instance|d'instance|de commerce|administratif|correctionnel)\b",
            @"\bCour\s+(?:de cassation|d'appel|des comptes)\b",
            @"\bConseil\s+(?:d'État|constitutionnel|de prud'hommes)\b"
        };

        var entities = new List<NamedEntity>();

        foreach (var pattern in patterns)
        {
            var matches = Regex.Matches(text, pattern, RegexOptions.IgnoreCase);
            entities.AddRange(matches.Cast<Match>().Select(m => NamedEntity.Create(
                m.Value,
                EntityType.Court,
                m.Index,
                m.Index + m.Length,
                0.9
            )));
        }

        return entities;
    }

    private class ChatCompletionResponse
    {
        public Choice[] Choices { get; set; } = Array.Empty<Choice>();
    }

    private class Choice
    {
        public Message Message { get; set; } = new();
    }

    private class Message
    {
        public string Content { get; set; } = string.Empty;
    }
}
