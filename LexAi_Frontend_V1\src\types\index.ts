// Types pour l'authentification et les utilisateurs
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  avatar?: string
  phoneNumber?: string
  isActive: boolean
  isEmailVerified: boolean
  isLocked?: boolean
  lastLoginAt?: Date
  preferredLanguage?: string
  timeZone?: string
  profilePictureUrl?: string
  createdAt: Date
  updatedAt: Date
}

export const UserRole = {
    Administrator: 1,
    SeniorLawyer: 2,
    Lawyer: 3,
    LegalAssistant: 4,
    Client: 5,
    Guest: 6
} as const

export type UserRole = typeof UserRole[keyof typeof UserRole]
export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface RegisterData {
  email: string
  firstName: string
  lastName: string
  phoneNumber?: string
  password: string
  confirmPassword: string
  role: UserRole
  preferredLanguage: string
  timeZone: string
  acceptTerms: boolean
  acceptPrivacyPolicy: boolean
}

export interface LoginData {
  email: string
  password: string
  rememberMe?: boolean
}

export interface AuthResponse {
  accessToken: string
  refreshToken: string
  tokenType: string
  expiresIn: number
  user: User
}

// Types pour les clients
export interface Client {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  address?: string
  company?: string
  notes?: string
  createdAt: Date
  updatedAt: Date
}

// Types pour DataProcessing
export interface Document {
  id: string
  fileName: string
  fileSize: number
  mimeType: string
  status: DocumentStatus
  detectedDomain?: string
  classificationConfidence?: number
  chunkCount: number
  totalTokens: number
  estimatedCost: number
  isVectorized: boolean
  vectorDatabase?: string
  estimatedProcessingTime?: string
  createdAt: Date
  updatedAt: Date
  // Propriétés calculées pour compatibilité
  originalFileName?: string
  uploadedAt?: Date
  uploadedBy?: string
  processingStatus?: ProcessingStatus
  metadata?: DocumentMetadata
}

export interface DocumentMetadata {
  title?: string
  author?: string
  subject?: string
  keywords?: string[]
  pageCount?: number
  wordCount?: number
  characterCount?: number
}

export const DocumentStatus = {
  Uploaded: 0,
  Extracting: 1,
  Extracted: 2,
  Classifying: 3,
  Classified: 4,
  Chunking: 5,
  Chunked: 6,
  Vectorizing: 7,
  Completed: 8,
  Failed: 9,
  Reprocessing: 10
} as const

export type DocumentStatus = typeof DocumentStatus[keyof typeof DocumentStatus]

export const ProcessingStatus = {
  Pending: 'Pending',
  InProgress: 'InProgress',
  Completed: 'Completed',
  Failed: 'Failed'
} as const

export type ProcessingStatus = typeof ProcessingStatus[keyof typeof ProcessingStatus]

export interface ProcessingConfiguration {
  chunking: ChunkingConfiguration
  embeddingModel: EmbeddingModelType
  targetDatabases: VectorDatabaseType[]
  performQualityAssurance: boolean
  extractNamedEntities: boolean
}

export interface ChunkingConfiguration {
  strategy: ChunkingStrategy
  maxTokens: number
  overlapTokens: number
  preserveStructure: boolean
}

export const ChunkingStrategy = {
  FixedSize: 'FixedSize',
  Semantic: 'Semantic',
  Paragraph: 'Paragraph',
  Sentence: 'Sentence'
} as const

export type ChunkingStrategy = typeof ChunkingStrategy[keyof typeof ChunkingStrategy]

export const EmbeddingModelType = {
  OpenAISmall: 'OpenAISmall',
  OpenAILarge: 'OpenAILarge',
  HuggingFace: 'HuggingFace',
  CustomLegal: 'CustomLegal'
} as const

export type EmbeddingModelType = typeof EmbeddingModelType[keyof typeof EmbeddingModelType]

export const VectorDatabaseType = {
  Pinecone: 'Pinecone',
  Weaviate: 'Weaviate',
  Qdrant: 'Qdrant'
} as const

export type VectorDatabaseType = typeof VectorDatabaseType[keyof typeof VectorDatabaseType]

export interface ProcessingResult {
  success: boolean
  documentId: string
  chunksCreated: number
  processingTimeMs: number
  totalTokens: number
  estimatedCost: number
  vectorDatabasesUsed: string[]
  errors: string[]
  warnings: string[]
}

// Types pour la gestion des mots de passe
export interface ChangePasswordData {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface ForgotPasswordData {
  email: string
}

export interface ResetPasswordData {
  token: string
  newPassword: string
  confirmPassword: string
}

// Types pour les dossiers
export interface CaseFile {
  id: string
  title: string
  description: string
  clientId: string
  client?: Client
  status: CaseStatus
  priority: CasePriority
  assignedLawyerId: string
  assignedLawyer?: User
  documents: CaseDocument[]
  notes: Note[]
  deadlines: Deadline[]
  createdAt: Date
  updatedAt: Date
}

export type CaseStatus = 'draft'|'active'| 'on_hold'| 'closed'|'archived';

export type CasePriority = 'low' | 'medium' | 'high' | 'urgent';

// Types pour les documents de dossiers (renommé pour éviter le conflit)
export interface CaseDocument {
  id: string
  name: string
  type: DocumentType
  size: number
  url: string
  caseFileId?: string
  uploadedBy: string
  uploadedAt: Date
  tags: string[]
}

export type DocumentType = 'contract' | 'letter' | 'court_document' | 'evidence' | 'correspondence' | 'other';

// Types pour les notes
export interface Note {
  id: string
  content: string
  caseFileId: string
  authorId: string
  author?: User
  createdAt: Date
  updatedAt: Date
}

// Types pour les échéances
export interface Deadline {
  id: string
  title: string
  description?: string
  dueDate: Date
  caseFileId: string
  assignedTo: string
  completed: boolean
  createdAt: Date
  updatedAt: Date
}

// Types pour la recherche juridique
export interface LegalSearchQuery {
  query: string
  domain?: LegalDomain
  jurisdiction?: string
  dateRange?: {
    from: Date
    to: Date
  }
}

export interface LegalSearchResult {
  id: string
  title: string
  content: string
  source: LegalSource
  relevanceScore: number
  url?: string
  date?: Date
  citations: string[]
}

export type LegalDomain = 'civil' | 'criminal' | 'commercial' | 'labor' | 'administrative' | 'tax' | 'family' | 'real_estate';


export interface LegalSource {
  type: SourceType
  name: string
  reference: string
}

export type SourceType = 'law' | 'jurisprudence' | 'doctrine' | 'regulation';


// Types pour le chat IA
export interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  attachments?: ChatAttachment[]
}

export interface ChatAttachment {
  id: string
  name: string
  type: string
  url: string
}

export interface ChatSession {
  id: string
  title: string
  messages: ChatMessage[]
  createdAt: Date
  updatedAt: Date
}

// Types pour la génération de documents
export interface DocumentTemplate {
  id: string
  name: string
  description: string
  category: DocumentCategory
  template: string
  fields: TemplateField[]
  createdAt: Date
  updatedAt: Date
}

export type DocumentCategory = 'contract' | 'letter' | 'legal_notice' | 'court_filing' | 'agreement';

export interface TemplateField {
  id: string
  name: string
  label: string
  type: FieldType
  required: boolean
  defaultValue?: string
  options?: string[]
}

export type FieldType = 'text' | 'textarea' | 'date' | 'number' | 'select' | 'checkbox';

export interface GeneratedDocument {
  id: string
  templateId: string
  template?: DocumentTemplate
  content: string
  data: Record<string, any>
  generatedAt: Date
  generatedBy: string
}

// Types pour l'analyse de documents
export interface DocumentAnalysis {
  id: string
  documentId: string
  document?: Document
  summary: string
  keyPoints: string[]
  risks: Risk[]
  suggestions: string[]
  confidence: number
  analyzedAt: Date
}

export interface Risk {
  level: RiskLevel
  description: string
  recommendation: string
}

export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

// Types pour les API responses
export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Types pour les erreurs
export interface ApiError {
  message: string
  code: string
  details?: any
}
