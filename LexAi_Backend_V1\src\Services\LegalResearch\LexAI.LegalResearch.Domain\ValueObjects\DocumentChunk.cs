using LexAI.Shared.Domain.Common;

namespace LexAI.LegalResearch.Domain.ValueObjects;

/// <summary>
/// Represents a chunk of a legal document for vector search
/// </summary>
public class DocumentChunk : ValueObject
{
    /// <summary>
    /// Chunk unique identifier
    /// </summary>
    public Guid Id { get; private set; }

    /// <summary>
    /// Document ID this chunk belongs to
    /// </summary>
    public Guid DocumentId { get; private set; }

    /// <summary>
    /// Chunk content/text
    /// </summary>
    public string Content { get; private set; }

    /// <summary>
    /// Chunk type
    /// </summary>
    public ChunkType Type { get; private set; }

    /// <summary>
    /// Chunk sequence number within the document
    /// </summary>
    public int SequenceNumber { get; private set; }

    /// <summary>
    /// Start position in the original document
    /// </summary>
    public int StartPosition { get; private set; }

    /// <summary>
    /// End position in the original document
    /// </summary>
    public int EndPosition { get; private set; }

    /// <summary>
    /// Chunk length in characters
    /// </summary>
    public int Length { get; private set; }

    /// <summary>
    /// Embedding vector for semantic search
    /// </summary>
    public float[] EmbeddingVector { get; private set; }

    /// <summary>
    /// Chunk keywords for search
    /// </summary>
    public List<string> Keywords { get; private set; }

    /// <summary>
    /// Chunk metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; private set; }

    /// <summary>
    /// Chunk creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private DocumentChunk() 
    {
        Content = string.Empty;
        EmbeddingVector = Array.Empty<float>();
        Keywords = new List<string>();
        Metadata = new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates a new document chunk
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="content">Chunk content</param>
    /// <param name="type">Chunk type</param>
    /// <param name="sequenceNumber">Sequence number</param>
    /// <param name="startPosition">Start position</param>
    /// <param name="endPosition">End position</param>
    /// <returns>New document chunk</returns>
    public static DocumentChunk Create(
        Guid documentId,
        string content,
        ChunkType type,
        int sequenceNumber,
        int startPosition,
        int endPosition)
    {
        if (documentId == Guid.Empty)
            throw new ArgumentException("Document ID cannot be empty", nameof(documentId));

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        if (sequenceNumber < 0)
            throw new ArgumentException("Sequence number cannot be negative", nameof(sequenceNumber));

        if (startPosition < 0)
            throw new ArgumentException("Start position cannot be negative", nameof(startPosition));

        if (endPosition <= startPosition)
            throw new ArgumentException("End position must be greater than start position", nameof(endPosition));

        return new DocumentChunk
        {
            Id = Guid.NewGuid(),
            DocumentId = documentId,
            Content = content.Trim(),
            Type = type,
            SequenceNumber = sequenceNumber,
            StartPosition = startPosition,
            EndPosition = endPosition,
            Length = content.Trim().Length,
            EmbeddingVector = Array.Empty<float>(),
            Keywords = new List<string>(),
            Metadata = new Dictionary<string, object>(),
            CreatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Sets the embedding vector for the chunk
    /// </summary>
    /// <param name="embeddingVector">Embedding vector</param>
    public DocumentChunk WithEmbedding(float[] embeddingVector)
    {
        if (embeddingVector == null || embeddingVector.Length == 0)
            throw new ArgumentException("Embedding vector cannot be null or empty", nameof(embeddingVector));

        var chunk = Clone();
        chunk.EmbeddingVector = embeddingVector.ToArray();
        return chunk;
    }

    /// <summary>
    /// Sets keywords for the chunk
    /// </summary>
    /// <param name="keywords">Keywords</param>
    public DocumentChunk WithKeywords(IEnumerable<string> keywords)
    {
        if (keywords == null)
            throw new ArgumentNullException(nameof(keywords));

        var chunk = Clone();
        chunk.Keywords = keywords
            .Where(k => !string.IsNullOrWhiteSpace(k))
            .Select(k => k.Trim().ToLowerInvariant())
            .Distinct()
            .ToList();
        return chunk;
    }

    /// <summary>
    /// Adds metadata to the chunk
    /// </summary>
    /// <param name="key">Metadata key</param>
    /// <param name="value">Metadata value</param>
    public DocumentChunk WithMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        var chunk = Clone();
        chunk.Metadata[key] = value;
        return chunk;
    }

    /// <summary>
    /// Adds multiple metadata entries
    /// </summary>
    /// <param name="metadata">Metadata dictionary</param>
    public DocumentChunk WithMetadata(Dictionary<string, object> metadata)
    {
        if (metadata == null)
            throw new ArgumentNullException(nameof(metadata));

        var chunk = Clone();
        foreach (var kvp in metadata)
        {
            chunk.Metadata[kvp.Key] = kvp.Value;
        }
        return chunk;
    }

    /// <summary>
    /// Gets a preview of the chunk content
    /// </summary>
    /// <param name="maxLength">Maximum preview length</param>
    /// <returns>Content preview</returns>
    public string GetPreview(int maxLength = 200)
    {
        if (Content.Length <= maxLength)
            return Content;

        return Content.Substring(0, maxLength - 3) + "...";
    }

    /// <summary>
    /// Checks if the chunk has an embedding vector
    /// </summary>
    /// <returns>True if chunk has embedding</returns>
    public bool HasEmbedding()
    {
        return EmbeddingVector != null && EmbeddingVector.Length > 0;
    }

    /// <summary>
    /// Gets the embedding dimension
    /// </summary>
    /// <returns>Embedding dimension</returns>
    public int GetEmbeddingDimension()
    {
        return EmbeddingVector?.Length ?? 0;
    }

    /// <summary>
    /// Calculates cosine similarity with another chunk
    /// </summary>
    /// <param name="other">Other chunk</param>
    /// <returns>Cosine similarity score</returns>
    public double CalculateSimilarity(DocumentChunk other)
    {
        if (other == null)
            throw new ArgumentNullException(nameof(other));

        if (!HasEmbedding() || !other.HasEmbedding())
            return 0.0;

        if (EmbeddingVector.Length != other.EmbeddingVector.Length)
            return 0.0;

        return CalculateCosineSimilarity(EmbeddingVector, other.EmbeddingVector);
    }

    /// <summary>
    /// Calculates cosine similarity with a query vector
    /// </summary>
    /// <param name="queryVector">Query embedding vector</param>
    /// <returns>Cosine similarity score</returns>
    public double CalculateSimilarity(float[] queryVector)
    {
        if (queryVector == null)
            throw new ArgumentNullException(nameof(queryVector));

        if (!HasEmbedding())
            return 0.0;

        if (EmbeddingVector.Length != queryVector.Length)
            return 0.0;

        return CalculateCosineSimilarity(EmbeddingVector, queryVector);
    }

    /// <summary>
    /// Checks if the chunk contains any of the specified keywords
    /// </summary>
    /// <param name="searchKeywords">Keywords to search for</param>
    /// <returns>True if chunk contains any keyword</returns>
    public bool ContainsKeywords(IEnumerable<string> searchKeywords)
    {
        if (searchKeywords == null)
            return false;

        var normalizedSearchKeywords = searchKeywords
            .Where(k => !string.IsNullOrWhiteSpace(k))
            .Select(k => k.Trim().ToLowerInvariant())
            .ToHashSet();

        return Keywords.Any(k => normalizedSearchKeywords.Contains(k)) ||
               normalizedSearchKeywords.Any(k => Content.ToLowerInvariant().Contains(k));
    }

    /// <summary>
    /// Gets the chunk context (surrounding text)
    /// </summary>
    /// <param name="contextLength">Context length in characters</param>
    /// <returns>Chunk with context</returns>
    public string GetContextualContent(int contextLength = 100)
    {
        // This would typically require access to the full document
        // For now, return the chunk content
        return Content;
    }

    private static double CalculateCosineSimilarity(float[] vector1, float[] vector2)
    {
        if (vector1.Length != vector2.Length)
            return 0.0;

        double dotProduct = 0.0;
        double magnitude1 = 0.0;
        double magnitude2 = 0.0;

        for (int i = 0; i < vector1.Length; i++)
        {
            dotProduct += vector1[i] * vector2[i];
            magnitude1 += vector1[i] * vector1[i];
            magnitude2 += vector2[i] * vector2[i];
        }

        magnitude1 = Math.Sqrt(magnitude1);
        magnitude2 = Math.Sqrt(magnitude2);

        if (magnitude1 == 0.0 || magnitude2 == 0.0)
            return 0.0;

        return dotProduct / (magnitude1 * magnitude2);
    }

    private DocumentChunk Clone()
    {
        return new DocumentChunk
        {
            Id = Id,
            DocumentId = DocumentId,
            Content = Content,
            Type = Type,
            SequenceNumber = SequenceNumber,
            StartPosition = StartPosition,
            EndPosition = EndPosition,
            Length = Length,
            EmbeddingVector = EmbeddingVector?.ToArray() ?? Array.Empty<float>(),
            Keywords = new List<string>(Keywords),
            Metadata = new Dictionary<string, object>(Metadata),
            CreatedAt = CreatedAt
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Id;
        yield return DocumentId;
        yield return SequenceNumber;
        yield return StartPosition;
        yield return EndPosition;
    }
}
