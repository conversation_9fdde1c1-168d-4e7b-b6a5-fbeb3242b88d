using LexAI.Shared.Domain.Common;

namespace LexAI.LegalResearch.Domain.ValueObjects;

/// <summary>
/// Represents a date range for filtering
/// </summary>
public class DateRange : ValueObject
{
    /// <summary>
    /// Start date
    /// </summary>
    public DateTime? StartDate { get; private set; }

    /// <summary>
    /// End date
    /// </summary>
    public DateTime? EndDate { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private DateRange() { }

    /// <summary>
    /// Creates a new date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>New date range</returns>
    public static DateRange Create(DateTime? startDate, DateTime? endDate)
    {
        if (startDate.HasValue && endDate.HasValue && startDate > endDate)
            throw new ArgumentException("Start date cannot be after end date");

        return new DateRange
        {
            StartDate = startDate,
            EndDate = endDate
        };
    }

    /// <summary>
    /// Checks if a date falls within this range
    /// </summary>
    /// <param name="date">Date to check</param>
    /// <returns>True if date is within range</returns>
    public bool Contains(DateTime date)
    {
        if (StartDate.HasValue && date < StartDate.Value)
            return false;

        if (EndDate.HasValue && date > EndDate.Value)
            return false;

        return true;
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return StartDate ?? DateTime.MinValue;
        yield return EndDate ?? DateTime.MaxValue;
    }
}

/// <summary>
/// Represents search parameters
/// </summary>
public class SearchParameters : ValueObject
{
    /// <summary>
    /// Search method
    /// </summary>
    public SearchMethod Method { get; private set; }

    /// <summary>
    /// Maximum number of results
    /// </summary>
    public int Limit { get; private set; }

    /// <summary>
    /// Minimum relevance score
    /// </summary>
    public double MinRelevanceScore { get; private set; }

    /// <summary>
    /// Include highlights
    /// </summary>
    public bool IncludeHighlights { get; private set; }

    /// <summary>
    /// Include similar documents
    /// </summary>
    public bool IncludeSimilar { get; private set; }

    /// <summary>
    /// Search timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private SearchParameters() { }

    /// <summary>
    /// Creates new search parameters
    /// </summary>
    /// <param name="method">Search method</param>
    /// <param name="limit">Result limit</param>
    /// <param name="minRelevanceScore">Minimum relevance score</param>
    /// <param name="includeHighlights">Include highlights</param>
    /// <param name="includeSimilar">Include similar documents</param>
    /// <param name="timeoutSeconds">Timeout in seconds</param>
    /// <returns>New search parameters</returns>
    public static SearchParameters Create(
        SearchMethod method = SearchMethod.Hybrid,
        int limit = 20,
        double minRelevanceScore = 0.5,
        bool includeHighlights = true,
        bool includeSimilar = false,
        int timeoutSeconds = 30)
    {
        if (limit <= 0 || limit > 100)
            throw new ArgumentException("Limit must be between 1 and 100", nameof(limit));

        if (minRelevanceScore < 0.0 || minRelevanceScore > 1.0)
            throw new ArgumentException("Minimum relevance score must be between 0 and 1", nameof(minRelevanceScore));

        if (timeoutSeconds <= 0 || timeoutSeconds > 300)
            throw new ArgumentException("Timeout must be between 1 and 300 seconds", nameof(timeoutSeconds));

        return new SearchParameters
        {
            Method = method,
            Limit = limit,
            MinRelevanceScore = minRelevanceScore,
            IncludeHighlights = includeHighlights,
            IncludeSimilar = includeSimilar,
            TimeoutSeconds = timeoutSeconds
        };
    }

    /// <summary>
    /// Creates default search parameters
    /// </summary>
    /// <returns>Default search parameters</returns>
    public static SearchParameters CreateDefault()
    {
        return Create();
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Method;
        yield return Limit;
        yield return MinRelevanceScore;
        yield return IncludeHighlights;
        yield return IncludeSimilar;
        yield return TimeoutSeconds;
    }
}

/// <summary>
/// Represents text highlight information
/// </summary>
public class TextHighlight : ValueObject
{
    /// <summary>
    /// Highlighted text
    /// </summary>
    public string Text { get; private set; }

    /// <summary>
    /// Start position in document
    /// </summary>
    public int StartPosition { get; private set; }

    /// <summary>
    /// End position in document
    /// </summary>
    public int EndPosition { get; private set; }

    /// <summary>
    /// Highlight score
    /// </summary>
    public double Score { get; private set; }

    /// <summary>
    /// Highlight type
    /// </summary>
    public string Type { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private TextHighlight() 
    {
        Text = string.Empty;
        Type = string.Empty;
    }

    /// <summary>
    /// Creates a new text highlight
    /// </summary>
    /// <param name="text">Highlighted text</param>
    /// <param name="startPosition">Start position</param>
    /// <param name="endPosition">End position</param>
    /// <param name="score">Highlight score</param>
    /// <param name="type">Highlight type</param>
    /// <returns>New text highlight</returns>
    public static TextHighlight Create(
        string text,
        int startPosition,
        int endPosition,
        double score = 1.0,
        string type = "keyword")
    {
        if (string.IsNullOrWhiteSpace(text))
            throw new ArgumentException("Text cannot be empty", nameof(text));

        if (startPosition < 0)
            throw new ArgumentException("Start position cannot be negative", nameof(startPosition));

        if (endPosition <= startPosition)
            throw new ArgumentException("End position must be greater than start position", nameof(endPosition));

        if (score < 0.0 || score > 1.0)
            throw new ArgumentException("Score must be between 0 and 1", nameof(score));

        return new TextHighlight
        {
            Text = text.Trim(),
            StartPosition = startPosition,
            EndPosition = endPosition,
            Score = score,
            Type = type ?? "keyword"
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Text;
        yield return StartPosition;
        yield return EndPosition;
        yield return Type;
    }
}

/// <summary>
/// Represents a matched chunk in search results
/// </summary>
public class MatchedChunk : ValueObject
{
    /// <summary>
    /// Chunk ID
    /// </summary>
    public Guid ChunkId { get; private set; }

    /// <summary>
    /// Chunk content
    /// </summary>
    public string Content { get; private set; }

    /// <summary>
    /// Chunk type
    /// </summary>
    public ChunkType Type { get; private set; }

    /// <summary>
    /// Similarity score
    /// </summary>
    public double SimilarityScore { get; private set; }

    /// <summary>
    /// Start position in document
    /// </summary>
    public int StartPosition { get; private set; }

    /// <summary>
    /// End position in document
    /// </summary>
    public int EndPosition { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private MatchedChunk() 
    {
        Content = string.Empty;
    }

    /// <summary>
    /// Creates a new matched chunk
    /// </summary>
    /// <param name="chunkId">Chunk ID</param>
    /// <param name="content">Chunk content</param>
    /// <param name="type">Chunk type</param>
    /// <param name="similarityScore">Similarity score</param>
    /// <param name="startPosition">Start position</param>
    /// <param name="endPosition">End position</param>
    /// <returns>New matched chunk</returns>
    public static MatchedChunk Create(
        Guid chunkId,
        string content,
        ChunkType type,
        double similarityScore,
        int startPosition,
        int endPosition)
    {
        if (chunkId == Guid.Empty)
            throw new ArgumentException("Chunk ID cannot be empty", nameof(chunkId));

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        if (similarityScore < 0.0 || similarityScore > 1.0)
            throw new ArgumentException("Similarity score must be between 0 and 1", nameof(similarityScore));

        return new MatchedChunk
        {
            ChunkId = chunkId,
            Content = content.Trim(),
            Type = type,
            SimilarityScore = similarityScore,
            StartPosition = startPosition,
            EndPosition = endPosition
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return ChunkId;
        yield return SimilarityScore;
    }
}

/// <summary>
/// Represents user feedback on search results
/// </summary>
public class UserFeedback : ValueObject
{
    /// <summary>
    /// Overall rating (1-5)
    /// </summary>
    public int OverallRating { get; private set; }

    /// <summary>
    /// Relevance rating (1-5)
    /// </summary>
    public int RelevanceRating { get; private set; }

    /// <summary>
    /// Completeness rating (1-5)
    /// </summary>
    public int CompletenessRating { get; private set; }

    /// <summary>
    /// Usefulness rating (1-5)
    /// </summary>
    public int UsefulnessRating { get; private set; }

    /// <summary>
    /// Additional comments
    /// </summary>
    public string? Comments { get; private set; }

    /// <summary>
    /// Feedback timestamp
    /// </summary>
    public DateTime CreatedAt { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private UserFeedback() { }

    /// <summary>
    /// Creates new user feedback
    /// </summary>
    /// <param name="overallRating">Overall rating</param>
    /// <param name="relevanceRating">Relevance rating</param>
    /// <param name="completenessRating">Completeness rating</param>
    /// <param name="usefulnessRating">Usefulness rating</param>
    /// <param name="comments">Comments</param>
    /// <returns>New user feedback</returns>
    public static UserFeedback Create(
        int overallRating,
        int relevanceRating,
        int completenessRating,
        int usefulnessRating,
        string? comments = null)
    {
        ValidateRating(overallRating, nameof(overallRating));
        ValidateRating(relevanceRating, nameof(relevanceRating));
        ValidateRating(completenessRating, nameof(completenessRating));
        ValidateRating(usefulnessRating, nameof(usefulnessRating));

        return new UserFeedback
        {
            OverallRating = overallRating,
            RelevanceRating = relevanceRating,
            CompletenessRating = completenessRating,
            UsefulnessRating = usefulnessRating,
            Comments = comments?.Trim(),
            CreatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Gets the overall rating as a normalized score (0-1)
    /// </summary>
    /// <returns>Overall rating score</returns>
    public double GetOverallRating()
    {
        return (OverallRating + RelevanceRating + CompletenessRating + UsefulnessRating) / 20.0;
    }

    private static void ValidateRating(int rating, string paramName)
    {
        if (rating < 1 || rating > 5)
            throw new ArgumentException("Rating must be between 1 and 5", paramName);
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return OverallRating;
        yield return RelevanceRating;
        yield return CompletenessRating;
        yield return UsefulnessRating;
        yield return Comments ?? string.Empty;
    }
}

/// <summary>
/// Represents document metadata
/// </summary>
public class DocumentMetadata : ValueObject
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; private set; }

    /// <summary>
    /// Word count
    /// </summary>
    public int WordCount { get; private set; }

    /// <summary>
    /// Character count
    /// </summary>
    public int CharacterCount { get; private set; }

    /// <summary>
    /// Reading time in minutes
    /// </summary>
    public int ReadingTimeMinutes { get; private set; }

    /// <summary>
    /// Complexity score (0-1)
    /// </summary>
    public double ComplexityScore { get; private set; }

    /// <summary>
    /// Additional metadata properties
    /// </summary>
    public Dictionary<string, object> Properties { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private DocumentMetadata() 
    {
        Properties = new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates new document metadata
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <returns>New document metadata</returns>
    public static DocumentMetadata Create(Guid documentId)
    {
        if (documentId == Guid.Empty)
            throw new ArgumentException("Document ID cannot be empty", nameof(documentId));

        return new DocumentMetadata
        {
            DocumentId = documentId,
            Properties = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// Updates text statistics
    /// </summary>
    /// <param name="content">Document content</param>
    public DocumentMetadata WithTextStatistics(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return this;

        var metadata = Clone();
        metadata.CharacterCount = content.Length;
        metadata.WordCount = content.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;
        metadata.ReadingTimeMinutes = Math.Max(1, metadata.WordCount / 200); // Average reading speed
        
        return metadata;
    }

    /// <summary>
    /// Sets complexity score
    /// </summary>
    /// <param name="score">Complexity score</param>
    public DocumentMetadata WithComplexityScore(double score)
    {
        if (score < 0.0 || score > 1.0)
            throw new ArgumentException("Complexity score must be between 0 and 1", nameof(score));

        var metadata = Clone();
        metadata.ComplexityScore = score;
        return metadata;
    }

    private DocumentMetadata Clone()
    {
        return new DocumentMetadata
        {
            DocumentId = DocumentId,
            WordCount = WordCount,
            CharacterCount = CharacterCount,
            ReadingTimeMinutes = ReadingTimeMinutes,
            ComplexityScore = ComplexityScore,
            Properties = new Dictionary<string, object>(Properties)
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return DocumentId;
        yield return WordCount;
        yield return CharacterCount;
        yield return ComplexityScore;
    }
}

/// <summary>
/// Represents a document reference
/// </summary>
public class DocumentReference : ValueObject
{
    /// <summary>
    /// Referenced document ID
    /// </summary>
    public Guid ReferencedDocumentId { get; private set; }

    /// <summary>
    /// Reference type
    /// </summary>
    public string ReferenceType { get; private set; }

    /// <summary>
    /// Reference description
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private DocumentReference() 
    {
        ReferenceType = string.Empty;
    }

    /// <summary>
    /// Creates a new document reference
    /// </summary>
    /// <param name="referencedDocumentId">Referenced document ID</param>
    /// <param name="referenceType">Reference type</param>
    /// <param name="description">Reference description</param>
    /// <returns>New document reference</returns>
    public static DocumentReference Create(
        Guid referencedDocumentId,
        string referenceType,
        string? description = null)
    {
        if (referencedDocumentId == Guid.Empty)
            throw new ArgumentException("Referenced document ID cannot be empty", nameof(referencedDocumentId));

        if (string.IsNullOrWhiteSpace(referenceType))
            throw new ArgumentException("Reference type cannot be empty", nameof(referenceType));

        return new DocumentReference
        {
            ReferencedDocumentId = referencedDocumentId,
            ReferenceType = referenceType.Trim(),
            Description = description?.Trim()
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return ReferencedDocumentId;
        yield return ReferenceType;
        yield return Description ?? string.Empty;
    }
}
