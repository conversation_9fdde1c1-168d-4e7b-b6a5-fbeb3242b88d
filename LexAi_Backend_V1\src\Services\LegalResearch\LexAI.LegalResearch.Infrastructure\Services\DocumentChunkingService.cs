using LexAI.LegalResearch.Application.DTOs;
using LexAI.LegalResearch.Application.Interfaces;
using LexAI.LegalResearch.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace LexAI.LegalResearch.Infrastructure.Services;

/// <summary>
/// Document chunking service implementation
/// </summary>
public class DocumentChunkingService : IDocumentChunkingService
{
    private readonly ILogger<DocumentChunkingService> _logger;

    /// <summary>
    /// Initializes a new instance of the DocumentChunkingService
    /// </summary>
    /// <param name="logger">Logger</param>
    public DocumentChunkingService(ILogger<DocumentChunkingService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Splits document content into chunks for vector search
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="content">Document content</param>
    /// <param name="chunkSize">Maximum chunk size in characters</param>
    /// <param name="overlap">Overlap between chunks in characters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document chunks</returns>
    public async Task<IEnumerable<DocumentChunkDto>> ChunkDocumentAsync(
        Guid documentId, 
        string content, 
        int chunkSize = 1000, 
        int overlap = 200, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Chunking document {DocumentId} with chunk size {ChunkSize} and overlap {Overlap}", 
            documentId, chunkSize, overlap);

        await Task.CompletedTask; // Simulate async operation

        if (string.IsNullOrWhiteSpace(content))
        {
            _logger.LogWarning("Document content is empty for document {DocumentId}", documentId);
            return Enumerable.Empty<DocumentChunkDto>();
        }

        var chunks = new List<DocumentChunkDto>();
        var sequenceNumber = 0;

        // First, try to split by paragraphs
        var paragraphs = content.Split(new[] { "\n\n", "\r\n\r\n" }, StringSplitOptions.RemoveEmptyEntries);
        
        var currentChunk = string.Empty;
        var currentStartPosition = 0;

        foreach (var paragraph in paragraphs)
        {
            var trimmedParagraph = paragraph.Trim();
            if (string.IsNullOrEmpty(trimmedParagraph))
                continue;

            // If adding this paragraph would exceed chunk size, create a chunk
            if (currentChunk.Length + trimmedParagraph.Length > chunkSize && !string.IsNullOrEmpty(currentChunk))
            {
                var chunk = await CreateChunkAsync(documentId, currentChunk, currentStartPosition, sequenceNumber++);
                chunks.Add(chunk);

                // Start new chunk with overlap
                var overlapText = GetOverlapText(currentChunk, overlap);
                currentChunk = overlapText + trimmedParagraph;
                currentStartPosition = content.IndexOf(currentChunk, currentStartPosition);
            }
            else
            {
                if (!string.IsNullOrEmpty(currentChunk))
                    currentChunk += "\n\n";
                currentChunk += trimmedParagraph;
            }
        }

        // Add the last chunk if it has content
        if (!string.IsNullOrEmpty(currentChunk))
        {
            var lastChunk = await CreateChunkAsync(documentId, currentChunk, currentStartPosition, sequenceNumber);
            chunks.Add(lastChunk);
        }

        // If no paragraphs were found, split by sentences
        if (chunks.Count == 0)
        {
            chunks.AddRange(await ChunkBySentencesAsync(documentId, content, chunkSize, overlap));
        }

        _logger.LogInformation("Created {ChunkCount} chunks for document {DocumentId}", chunks.Count, documentId);
        return chunks;
    }

    /// <summary>
    /// Extracts keywords from text
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="maxKeywords">Maximum number of keywords</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extracted keywords</returns>
    public async Task<IEnumerable<string>> ExtractKeywordsAsync(string text, int maxKeywords = 20, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Extracting keywords from text of length {TextLength}", text?.Length ?? 0);

        await Task.CompletedTask; // Simulate async operation

        if (string.IsNullOrWhiteSpace(text))
            return Enumerable.Empty<string>();

        // Simple keyword extraction based on word frequency
        var words = Regex.Matches(text.ToLowerInvariant(), @"\b[a-zàâäéèêëïîôöùûüÿç]{3,}\b")
            .Cast<Match>()
            .Select(m => m.Value)
            .Where(word => !IsStopWord(word))
            .GroupBy(word => word)
            .OrderByDescending(g => g.Count())
            .Take(maxKeywords)
            .Select(g => g.Key);

        var keywords = words.ToList();
        _logger.LogDebug("Extracted {KeywordCount} keywords", keywords.Count);
        return keywords;
    }

    /// <summary>
    /// Identifies the type of text chunk
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="context">Surrounding context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Chunk type</returns>
    public async Task<ChunkType> IdentifyChunkTypeAsync(string text, string? context = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Identifying chunk type for text of length {TextLength}", text?.Length ?? 0);

        await Task.CompletedTask; // Simulate async operation

        if (string.IsNullOrWhiteSpace(text))
            return ChunkType.Paragraph;

        var lowerText = text.ToLowerInvariant();

        // Check for specific patterns
        if (Regex.IsMatch(lowerText, @"^(article|section|chapitre|titre)\s+\d+"))
            return ChunkType.Section;

        if (Regex.IsMatch(lowerText, @"^(considérant|attendu|vu|sur)"))
            return ChunkType.LegalReasoning;

        if (lowerText.Contains("définition") || lowerText.Contains("aux fins"))
            return ChunkType.Definition;

        if (lowerText.Contains("procédure") || lowerText.Contains("modalités"))
            return ChunkType.Procedure;

        if (lowerText.Contains("sanction") || lowerText.Contains("peine") || lowerText.Contains("amende"))
            return ChunkType.Sanction;

        return ChunkType.Paragraph;
    }

    private async Task<DocumentChunkDto> CreateChunkAsync(Guid documentId, string content, int startPosition, int sequenceNumber)
    {
        var chunkType = await IdentifyChunkTypeAsync(content);
        var keywords = await ExtractKeywordsAsync(content, 10);

        return new DocumentChunkDto
        {
            Id = Guid.NewGuid(),
            DocumentId = documentId,
            Content = content.Trim(),
            Type = chunkType,
            SequenceNumber = sequenceNumber,
            StartPosition = startPosition,
            EndPosition = startPosition + content.Length,
            Keywords = keywords.ToList(),
            Metadata = new Dictionary<string, object>
            {
                { "length", content.Length },
                { "wordCount", content.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length }
            }
        };
    }

    private async Task<IEnumerable<DocumentChunkDto>> ChunkBySentencesAsync(Guid documentId, string content, int chunkSize, int overlap)
    {
        var chunks = new List<DocumentChunkDto>();
        var sentences = Regex.Split(content, @"(?<=[.!?])\s+")
            .Where(s => !string.IsNullOrWhiteSpace(s))
            .ToList();

        var currentChunk = string.Empty;
        var currentStartPosition = 0;
        var sequenceNumber = 0;

        foreach (var sentence in sentences)
        {
            if (currentChunk.Length + sentence.Length > chunkSize && !string.IsNullOrEmpty(currentChunk))
            {
                var chunk = await CreateChunkAsync(documentId, currentChunk, currentStartPosition, sequenceNumber++);
                chunks.Add(chunk);

                var overlapText = GetOverlapText(currentChunk, overlap);
                currentChunk = overlapText + sentence;
                currentStartPosition = content.IndexOf(currentChunk, currentStartPosition);
            }
            else
            {
                if (!string.IsNullOrEmpty(currentChunk))
                    currentChunk += " ";
                currentChunk += sentence;
            }
        }

        if (!string.IsNullOrEmpty(currentChunk))
        {
            var lastChunk = await CreateChunkAsync(documentId, currentChunk, currentStartPosition, sequenceNumber);
            chunks.Add(lastChunk);
        }

        return chunks;
    }

    private static string GetOverlapText(string text, int overlapSize)
    {
        if (text.Length <= overlapSize)
            return text;

        return text.Substring(text.Length - overlapSize);
    }

    private static bool IsStopWord(string word)
    {
        var stopWords = new HashSet<string>
        {
            "le", "de", "et", "à", "un", "il", "être", "et", "en", "avoir", "que", "pour",
            "dans", "ce", "son", "une", "sur", "avec", "ne", "se", "pas", "tout", "plus",
            "par", "grand", "en", "une", "être", "et", "à", "il", "avoir", "ne", "je", "son",
            "que", "se", "qui", "ce", "dans", "en", "du", "elle", "au", "de", "le", "un", "à"
        };

        return stopWords.Contains(word);
    }
}
