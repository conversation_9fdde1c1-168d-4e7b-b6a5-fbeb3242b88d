# LexAi - Assistant <PERSON>rid<PERSON> Intelligent

LexAi est une plateforme d'assistance juridique intelligente qui automatise et optimise les tâches principales des professionnels du droit : recherche juridique, rédaction, suivi des dossiers, veille, gestion documentaire et relation client.

## 🚀 Fonctionnalités

### MVP (Version 1.0)
- **🔐 Authentification & RBAC** - Gestion des rôles (Avocat, Assistant, Client)
- **🔍 Recherche Juridique Assistée (RAG)** - Recherche intelligente avec citations
- **🤖 Assistant IA / Chatbot** - Interface conversationnelle 24/7
- **📄 Analyse de Documents** - Analyse automatique des contrats et documents
- **📝 Générateur de Documents** - Génération de documents juridiques personnalisés
- **📊 Dashboard Avocat** - Vue d'ensemble de l'activité
- **👥 Portail Client** - Espace sécurisé pour les clients

### Modules Futurs
- Veille Juridique Automatisée
- Gestion de Dossiers & Clients (CRM)
- Agenda & Notifications
- Signature Électronique
- Facturation & Paiement

## 🛠️ Stack Technique

### Frontend
- **React 18** avec TypeScript
- **Vite** pour le build et le développement
- **TailwindCSS** pour le styling
- **Shadcn/ui** pour les composants UI
- **Zustand** pour la gestion d'état
- **React Router v6** pour le routage
- **React Hook Form + Zod** pour les formulaires
- **Axios** pour les appels API

### Tests
- **Vitest** pour les tests unitaires
- **React Testing Library** pour les tests de composants
- **@testing-library/jest-dom** pour les assertions

### Outils de Développement
- **ESLint** pour le linting
- **TypeScript** pour le typage statique
- **PostCSS** avec Autoprefixer

## 📁 Structure du Projet

```
src/
├── components/          # Composants réutilisables
│   ├── ui/             # Composants UI de base (Button, Input, Card...)
│   ├── layout/         # Composants de layout (Sidebar, Header, Layout)
│   └── features/       # Composants spécifiques aux modules
├── pages/              # Pages de l'application
│   ├── auth/          # Pages d'authentification
│   └── ...            # Autres pages
├── hooks/              # Custom hooks React
├── store/              # Stores Zustand
├── services/           # Services API
├── lib/                # Utilitaires et helpers
├── types/              # Types TypeScript
├── test/               # Configuration des tests
└── constants/          # Constantes de l'application
```

## 🚀 Installation et Démarrage

### Prérequis
- Node.js 18+
- npm ou yarn

### Installation
```bash
# Cloner le repository
git clone <repository-url>
cd LexAi_Frontend_V1

# Installer les dépendances
npm install

# Démarrer le serveur de développement
npm run dev
```

### Scripts Disponibles
```bash
npm run dev          # Démarrer le serveur de développement
npm run build        # Build de production
npm run preview      # Prévisualiser le build de production
npm run test         # Lancer les tests
npm run test:ui      # Interface graphique pour les tests
npm run test:coverage # Tests avec couverture de code
npm run lint         # Linter le code
```
