using LexAI.AIAssistant.Application.DTOs;
using LexAI.AIAssistant.Application.Interfaces;
using LexAI.AIAssistant.Domain.ValueObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace LexAI.AIAssistant.Infrastructure.Services;

/// <summary>
/// Service d'intégration avec le service Legal Research
/// </summary>
public class LegalResearchIntegrationService : ILegalResearchIntegrationService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<LegalResearchIntegrationService> _logger;
    private readonly string _legalResearchBaseUrl;

    public LegalResearchIntegrationService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<LegalResearchIntegrationService> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _legalResearchBaseUrl = configuration["Services:LegalResearch:BaseUrl"] ?? "http://localhost:5002";
    }

    /// <summary>
    /// Recherche des documents juridiques pertinents
    /// </summary>
    public async Task<IEnumerable<LegalDocumentSummaryDto>> SearchLegalDocumentsAsync(
        string query,
        LegalDomain? domain = null,
        int limit = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Searching legal documents for query: {Query}, domain: {Domain}, limit: {Limit}",
                query, domain, limit);

            var searchRequest = new
            {
                Query = query,
                Domain = domain?.ToString(),
                Limit = limit,
                IncludeContent = false,
                IncludeSummary = true
            };

            var json = JsonSerializer.Serialize(searchRequest);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_legalResearchBaseUrl}/api/search", content, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var searchResponse = JsonSerializer.Deserialize<SearchResponseDto>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return searchResponse?.Results?.Select(MapToLegalDocumentSummary) ?? Enumerable.Empty<LegalDocumentSummaryDto>();
            }
            else
            {
                _logger.LogWarning("Legal research service returned error: {StatusCode}", response.StatusCode);
                return Enumerable.Empty<LegalDocumentSummaryDto>();
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogWarning(ex, "Legal research service is not available. Using fallback.");
            return GetFallbackResults(query, domain, limit);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching legal documents for query: {Query}", query);
            return Enumerable.Empty<LegalDocumentSummaryDto>();
        }
    }

    /// <summary>
    /// Obtient un document juridique par son ID
    /// </summary>
    public async Task<LegalDocumentSummaryDto?> GetLegalDocumentAsync(
        Guid documentId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_legalResearchBaseUrl}/api/documents/{documentId}", cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var document = JsonSerializer.Deserialize<LegalDocumentSummaryDto>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return document;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting legal document for ID: {DocumentId}", documentId);
            return null;
        }
    }

    /// <summary>
    /// Trouve des documents similaires
    /// </summary>
    public async Task<IEnumerable<LegalDocumentSummaryDto>> FindSimilarDocumentsAsync(
        Guid documentId,
        int limit = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_legalResearchBaseUrl}/api/documents/{documentId}/similar?limit={limit}", cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var documents = JsonSerializer.Deserialize<IEnumerable<LegalDocumentSummaryDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return documents ?? Enumerable.Empty<LegalDocumentSummaryDto>();
            }

            return Enumerable.Empty<LegalDocumentSummaryDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding similar documents for ID: {DocumentId}", documentId);
            return Enumerable.Empty<LegalDocumentSummaryDto>();
        }
    }

    /// <summary>
    /// Obtient des détails sur un document juridique spécifique
    /// </summary>
    public async Task<LegalDocumentDetailDto?> GetDocumentDetailsAsync(
        Guid documentId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_legalResearchBaseUrl}/api/documents/{documentId}", cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var document = JsonSerializer.Deserialize<LegalDocumentDetailDto>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return document;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document details for ID: {DocumentId}", documentId);
            return null;
        }
    }

    /// <summary>
    /// Analyse une requête juridique
    /// </summary>
    public async Task<QueryAnalysisDto> AnalyzeQueryAsync(
        string query,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var analysisRequest = new { Query = query };
            var json = JsonSerializer.Serialize(analysisRequest);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_legalResearchBaseUrl}/api/analyze", content, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var analysis = JsonSerializer.Deserialize<QueryAnalysisDto>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return analysis ?? CreateFallbackAnalysis(query);
            }

            return CreateFallbackAnalysis(query);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing query: {Query}", query);
            return CreateFallbackAnalysis(query);
        }
    }

    private LegalDocumentSummaryDto MapToLegalDocumentSummary(dynamic result)
    {
        return new LegalDocumentSummaryDto
        {
            Id = Guid.TryParse(result.Id?.ToString(), out var id) ? id : Guid.NewGuid(),
            Title = result.Title?.ToString() ?? "Document sans titre",
            Summary = result.Summary?.ToString() ?? "",
            Source = result.Source?.ToString() ?? "Source inconnue",
            Url = result.Url?.ToString(),
            PublicationDate = DateTime.TryParse(result.PublicationDate?.ToString(), out var date) ? date : null,
            RelevanceScore = double.TryParse(result.RelevanceScore?.ToString(), out var score) ? score : 0.0,
            Domain = Enum.TryParse<LegalDomain>(result.Domain?.ToString(), out var domain) ? domain : LegalDomain.Other
        };
    }

    private IEnumerable<LegalDocumentSummaryDto> GetFallbackResults(string query, LegalDomain? domain, int limit)
    {
        // Retourne des résultats de fallback quand le service n'est pas disponible
        _logger.LogInformation("Using fallback results for query: {Query}", query);

        return new List<LegalDocumentSummaryDto>
        {
            new LegalDocumentSummaryDto
            {
                Id = Guid.NewGuid(),
                Title = $"Document de référence pour: {query}",
                Summary = "Ce document contient des informations pertinentes pour votre recherche. Service de recherche temporairement indisponible.",
                Source = "Base de données locale",
                RelevanceScore = 0.8,
                Domain = domain ?? LegalDomain.Other,
                PublicationDate = DateTime.Now.AddDays(-30)
            }
        };
    }

    private QueryAnalysisDto CreateFallbackAnalysis(string query)
    {
        return new QueryAnalysisDto
        {
            OriginalQuery = query,
            Intent = "information_request",
            Entities = new List<string>(),
            ExpandedTerms = new List<string> { query },
            ProcessedQuery = query
        };
    }
}

/// <summary>
/// DTO pour les réponses de recherche du service Legal Research
/// </summary>
public class SearchResponseDto
{
    public IEnumerable<dynamic>? Results { get; set; }
    public int TotalCount { get; set; }
    public string? Query { get; set; }
}

/// <summary>
/// DTO pour les détails d'un document juridique
/// </summary>
public class LegalDocumentDetailDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public string? Url { get; set; }
    public DateTime? PublicationDate { get; set; }
    public LegalDomain Domain { get; set; }
    public List<string> Keywords { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// DTO pour l'analyse de requête
/// </summary>
public class QueryAnalysisDto
{
    public string OriginalQuery { get; set; } = string.Empty;
    public string Intent { get; set; } = string.Empty;
    public List<string> Entities { get; set; } = new();
    public List<string> ExpandedTerms { get; set; } = new();
    public string ProcessedQuery { get; set; } = string.Empty;
}
