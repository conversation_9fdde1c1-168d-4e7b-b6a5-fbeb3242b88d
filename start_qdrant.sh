#!/bin/bash

echo "========================================"
echo "  Qdrant Vector Database - LexAI"
echo "========================================"
echo

# Vérifier si Docker est installé
if ! command -v docker &> /dev/null; then
    echo "ERREUR: Docker n'est pas installé"
    echo "Veuillez installer Docker depuis https://docker.com"
    exit 1
fi

echo "✅ Docker détecté"

# Vérifier si Docker Compose est disponible
if ! docker compose version &> /dev/null; then
    echo "ERREUR: Docker Compose n'est pas disponible"
    echo "Veuillez mettre à jour Docker"
    exit 1
fi

echo "✅ Docker Compose détecté"

echo
echo "🚀 Démarrage de Qdrant..."
echo "📍 HTTP API: http://localhost:6333"
echo "📍 gRPC API: localhost:6334"
echo "📍 Dashboard: http://localhost:6333/dashboard"
echo

# Démarrer Qdrant avec Docker Compose
docker compose -f docker-compose.qdrant.yml up -d

if [ $? -eq 0 ]; then
    echo
    echo "✅ Qdrant démarré avec succès !"
    echo
    echo "📊 Vérification de l'état..."
    sleep 5
    
    # Vérifier la santé du service
    if curl -s http://localhost:6333/health > /dev/null 2>&1; then
        echo "✅ Qdrant est opérationnel !"
        echo "🌐 Dashboard disponible: http://localhost:6333/dashboard"
    else
        echo "⏳ Qdrant démarre... (peut prendre quelques secondes)"
        echo "🔄 Vérifiez manuellement: http://localhost:6333/health"
    fi
    
    echo
    echo "📋 Commandes utiles:"
    echo "  - Arrêter: docker compose -f docker-compose.qdrant.yml down"
    echo "  - Logs: docker compose -f docker-compose.qdrant.yml logs -f"
    echo "  - Redémarrer: docker compose -f docker-compose.qdrant.yml restart"
    echo
else
    echo "❌ Erreur lors du démarrage de Qdrant"
    echo "Vérifiez les logs: docker compose -f docker-compose.qdrant.yml logs"
fi
