import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import {
  Upload,
  FileText,
  Search,
  Filter,
  Trash2,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'
import { Card, CardContent } from '../components/ui/Card'
import { useDocumentsStore } from '../store/documentsStore'
import { DocumentStatus } from '../types/index'
import {
  getStatusLabel,
  isProcessingStatus,
  isCompletedStatus,
  isPendingStatus,
  isFailedStatus
} from '../utils/statusUtils'

export function DocumentsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<DocumentStatus | 'all' | 'processing'>('all')

  const {
    documents,
    isLoading,
    getUserDocuments,
    deleteDocument
  } = useDocumentsStore()

  useEffect(() => {
    getUserDocuments()
  }, [getUserDocuments])

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch =
      (doc.originalFileName?.toLowerCase() ?? '').includes(searchTerm.toLowerCase()) ||
      (doc.metadata?.title?.toLowerCase() ?? '').includes(searchTerm.toLowerCase());

    let matchesStatus = false;
    if (statusFilter === 'all') {
      matchesStatus = true;
    } else if (statusFilter === 'processing') {
      matchesStatus = isProcessingStatus(doc.status);
    } else {
      matchesStatus = doc.status === statusFilter;
    }

    return matchesSearch && matchesStatus;
  })

  const getStatusIcon = (status: DocumentStatus) => {
    if (isProcessingStatus(status)) {
      return <AlertCircle className="h-4 w-4 text-yellow-500" />
    }
    if (isCompletedStatus(status)) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    }
    if (isFailedStatus(status)) {
      return <XCircle className="h-4 w-4 text-red-500" />
    }
    if (isPendingStatus(status) || status === DocumentStatus.Uploaded) {
      return <Clock className="h-4 w-4 text-blue-500" />
    }
    return <FileText className="h-4 w-4 text-gray-500" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDeleteDocument = async (id: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) {
      try {
        await deleteDocument(id)
      } catch (error) {
        console.error('Erreur lors de la suppression:', error)
      }
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Mes Documents</h1>
          <p className="text-gray-600">Gérez vos documents et leur traitement</p>
        </div>
        <Link to="/documents/upload">
          <Button>
            <Upload className="h-4 w-4 mr-2" />
            Télécharger un document
          </Button>
        </Link>
      </div>

      {/* Filtres et recherche */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Rechercher par nom de fichier ou titre..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as DocumentStatus | 'all' | 'processing')}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tous les statuts</option>
                <option value={DocumentStatus.Uploaded}>Téléchargé</option>
                <option value="processing">En traitement</option>
                <option value={DocumentStatus.Completed}>Traité</option>
                <option value={DocumentStatus.Failed}>Échec</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Liste des documents */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Chargement des documents...</p>
          </div>
        </div>
      ) : filteredDocuments.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                {documents.length === 0 ? 'Aucun document' : 'Aucun document trouvé'}
              </h3>
              <p className="mt-2 text-gray-600">
                {documents.length === 0
                  ? 'Commencez par télécharger votre premier document.'
                  : 'Essayez de modifier vos critères de recherche.'
                }
              </p>
              {documents.length === 0 && (
                <Link to="/documents/upload">
                  <Button className="mt-4">
                    <Upload className="h-4 w-4 mr-2" />
                    Télécharger un document
                  </Button>
                </Link>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {filteredDocuments.map((document) => (
            <Card key={document.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 flex-1">
                    <div className="flex-shrink-0">
                      <FileText className="h-8 w-8 text-blue-600" />
                    </div>

                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {document.metadata?.title || document.originalFileName}
                      </h3>
                      <div className="flex items-center space-x-4 mt-1">
                        <span className="text-sm text-gray-500">
                          {formatFileSize(document.fileSize)}
                        </span>
                        <span className="text-sm text-gray-500">
                          {new Date(document.uploadedAt || document.createdAt).toLocaleDateString('fr-FR')}
                        </span>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(document.status)}
                          <span className="text-sm text-gray-600">
                            {getStatusLabel(document.status)}
                          </span>
                        </div>
                      </div>

                      {document.metadata && (
                        <div className="mt-2 flex flex-wrap gap-2">
                          {document.metadata.pageCount && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                              {document.metadata.pageCount} pages
                            </span>
                          )}
                          {document.metadata.wordCount && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                              {document.metadata.wordCount.toLocaleString()} mots
                            </span>
                          )}
                          {document.metadata.keywords && document.metadata.keywords.length > 0 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                              {document.metadata.keywords.length} mots-clés
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Link to={`/documents/${document.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        Voir
                      </Button>
                    </Link>

                    {document.status === DocumentStatus.Uploaded && (
                      <Link to={`/documents/${document.id}/process`}>
                        <Button size="sm">
                          Traiter
                        </Button>
                      </Link>
                    )}

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteDocument(document.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Statistiques */}
      {documents.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {documents.length}
                </div>
                <div className="text-sm text-gray-600">Total documents</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {documents.filter(d => isCompletedStatus(d.status)).length}
                </div>
                <div className="text-sm text-gray-600">Traités</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {documents.filter(d => isProcessingStatus(d.status)).length}
                </div>
                <div className="text-sm text-gray-600">En traitement</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {documents.filter(d => d.status === DocumentStatus.Uploaded).length}
                </div>
                <div className="text-sm text-gray-600">En attente</div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
