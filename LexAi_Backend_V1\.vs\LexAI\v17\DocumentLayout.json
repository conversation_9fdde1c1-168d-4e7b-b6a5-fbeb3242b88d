{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\services\\localembeddingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\services\\localembeddingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7FB6747D-3AA4-1F0A-E89E-EA1BC5786CF2}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\LexAI.DataPreprocessing.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.application\\interfaces\\iinfrastructureservices.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7FB6747D-3AA4-1F0A-E89E-EA1BC5786CF2}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\LexAI.DataPreprocessing.Application.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.application\\interfaces\\iinfrastructureservices.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.Development.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.Development.json", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.Development.json", "ViewState": "AgIAACQAAAAAAAAAAAAAAC8AAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-02T12:49:54.869Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.json", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-02T12:49:49.65Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IInfrastructureServices.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Interfaces\\IInfrastructureServices.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Interfaces\\IInfrastructureServices.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Interfaces\\IInfrastructureServices.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Interfaces\\IInfrastructureServices.cs", "ViewState": "AgIAAMsBAAAAAAAAAAAYwNYBAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T12:41:13.221Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "LocalEmbeddingService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LocalEmbeddingService.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LocalEmbeddingService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LocalEmbeddingService.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LocalEmbeddingService.cs", "ViewState": "AgIAAIUAAAAAAAAAAAAmwFgAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T12:40:38.558Z", "EditorCaption": ""}]}]}]}