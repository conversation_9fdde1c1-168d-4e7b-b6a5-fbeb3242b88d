{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\docker-compose.dev.yml||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:docker-compose.dev.yml||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.infrastructure\\data\\identitydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|solutionrelative:src\\services\\identity\\lexai.identity.infrastructure\\data\\identitydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1CFED82B-BB52-4162-98D4-463014013FF5}|src\\Services\\Identity\\LexAI.Identity.Application\\LexAI.Identity.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.application\\validators\\registerusercommandvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1CFED82B-BB52-4162-98D4-463014013FF5}|src\\Services\\Identity\\LexAI.Identity.Application\\LexAI.Identity.Application.csproj|solutionrelative:src\\services\\identity\\lexai.identity.application\\validators\\registerusercommandvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1CFED82B-BB52-4162-98D4-463014013FF5}|src\\Services\\Identity\\LexAI.Identity.Application\\LexAI.Identity.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.application\\commands\\registerusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1CFED82B-BB52-4162-98D4-463014013FF5}|src\\Services\\Identity\\LexAI.Identity.Application\\LexAI.Identity.Application.csproj|solutionrelative:src\\services\\identity\\lexai.identity.application\\commands\\registerusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.infrastructure\\repositories\\userrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|solutionrelative:src\\services\\identity\\lexai.identity.infrastructure\\repositories\\userrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1CFED82B-BB52-4162-98D4-463014013FF5}|src\\Services\\Identity\\LexAI.Identity.Application\\LexAI.Identity.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.application\\commands\\authenticationcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1CFED82B-BB52-4162-98D4-463014013FF5}|src\\Services\\Identity\\LexAI.Identity.Application\\LexAI.Identity.Application.csproj|solutionrelative:src\\services\\identity\\lexai.identity.application\\commands\\authenticationcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8A995A0E-8D75-4175-B267-341F64F27B12}|src\\Services\\Identity\\LexAI.Identity.Domain\\LexAI.Identity.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.domain\\entities\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8A995A0E-8D75-4175-B267-341F64F27B12}|src\\Services\\Identity\\LexAI.Identity.Domain\\LexAI.Identity.Domain.csproj|solutionrelative:src\\services\\identity\\lexai.identity.domain\\entities\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2D333B21-37FA-4201-A868-5A56261A570A}|src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\shared\\lexai.shared.domain\\common\\baseentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2D333B21-37FA-4201-A868-5A56261A570A}|src\\Shared\\LexAI.Shared.Domain\\LexAI.Shared.Domain.csproj|solutionrelative:src\\shared\\lexai.shared.domain\\common\\baseentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "docker-compose.dev.yml", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\docker-compose.dev.yml", "RelativeDocumentMoniker": "docker-compose.dev.yml", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\docker-compose.dev.yml", "RelativeToolTip": "docker-compose.dev.yml", "ViewState": "AgIAAAIAAAAAAAAAAAAAAA4AAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003775|", "WhenOpened": "2025-06-01T13:06:44.987Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "RegisterUserCommandValidator.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Application\\Validators\\RegisterUserCommandValidator.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Application\\Validators\\RegisterUserCommandValidator.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Application\\Validators\\RegisterUserCommandValidator.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Application\\Validators\\RegisterUserCommandValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-01T11:59:36.554Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "RegisterUserCommand.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\RegisterUserCommand.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\RegisterUserCommand.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\RegisterUserCommand.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\RegisterUserCommand.cs", "ViewState": "AgIAALYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-01T11:59:00.177Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "AuthController.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\Controllers\\AuthController.cs", "ViewState": "AgIAACUAAAAAAAAAAAAmwDMAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T22:22:32.525Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-31T22:20:33.115Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\appsettings.Development.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\appsettings.Development.json", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-31T22:20:22.549Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "AuthenticationCommands.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\AuthenticationCommands.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\AuthenticationCommands.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\AuthenticationCommands.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Application\\Commands\\AuthenticationCommands.cs", "ViewState": "AgIAAEUAAAAAAAAAAADgv08AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T21:46:47.93Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "IdentityDbContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "ViewState": "AgIAAG8AAAAAAAAAAADgv4AAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T21:44:35.982Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "BaseEntity.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\Common\\BaseEntity.cs", "RelativeDocumentMoniker": "src\\Shared\\LexAI.Shared.Domain\\Common\\BaseEntity.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Domain\\Common\\BaseEntity.cs", "RelativeToolTip": "src\\Shared\\LexAI.Shared.Domain\\Common\\BaseEntity.cs", "ViewState": "AgIAACEAAAAAAAAAAAA7wDUAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T21:30:14.809Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "User.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Domain\\Entities\\User.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Domain\\Entities\\User.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Domain\\Entities\\User.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Domain\\Entities\\User.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T21:30:05.499Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.Development.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.Development.json", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.Development.json", "ViewState": "AgIAADMAAAAAAAAAAAAAAEcAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-31T20:39:34.976Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\Program.cs", "ViewState": "AgIAAJ8AAAAAAAAAAIAwwLMAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T19:17:41.104Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "UserRepository.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Repositories\\UserRepository.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Repositories\\UserRepository.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Repositories\\UserRepository.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Repositories\\UserRepository.cs", "ViewState": "AgIAALYAAAAAAAAAAAAAAMIAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T19:17:41.235Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.json", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAABiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-31T17:16:48.764Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Program.cs", "ViewState": "AgIAABkBAAAAAAAAAAAAACkBAABtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T17:15:20.235Z", "EditorCaption": ""}]}]}]}