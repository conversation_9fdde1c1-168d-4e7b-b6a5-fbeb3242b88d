{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\services\\llmclassificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\services\\llmclassificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\repositories\\conversationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\repositories\\conversationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\classificationagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\classificationagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\services\\llmqualityassuranceservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\services\\llmqualityassuranceservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\orchestrationagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\orchestrationagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ConversationRepository.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "ViewState": "AgIAACoAAAAAAAAAAAAYwDIAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T20:19:34.021Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ClassificationAgent.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ClassificationAgent.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ClassificationAgent.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ClassificationAgent.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\ClassificationAgent.cs", "ViewState": "AgIAABQAAAAAAAAAAAAxwCQAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T20:17:03.548Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "LLMClassificationService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LLMClassificationService.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LLMClassificationService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LLMClassificationService.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LLMClassificationService.cs", "ViewState": "AgIAAOwAAAAAAAAAAAAAAA0AAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T20:13:39.375Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "LLMQualityAssuranceService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LLMQualityAssuranceService.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LLMQualityAssuranceService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LLMQualityAssuranceService.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Services\\LLMQualityAssuranceService.cs", "ViewState": "AgIAAAEBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T20:13:08.962Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "OrchestrationAgent.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "ViewState": "AgIAAI4CAAAAAAAAAAAAAKQCAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T19:26:50.868Z", "EditorCaption": ""}]}]}]}