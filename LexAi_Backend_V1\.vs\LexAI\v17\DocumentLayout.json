{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\designtimedatapreprocessingdbcontextfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\designtimedatapreprocessingdbcontextfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.infrastructure\\data\\designtimedbcontextfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|solutionrelative:src\\services\\identity\\lexai.identity.infrastructure\\data\\designtimedbcontextfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\data\\datapreprocessingdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\data\\datapreprocessingdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\controllers\\documentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\controllers\\documentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\orchestrationagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\agents\\orchestrationagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7FB6747D-3AA4-1F0A-E89E-EA1BC5786CF2}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\LexAI.DataPreprocessing.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.application\\commands\\documentcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7FB6747D-3AA4-1F0A-E89E-EA1BC5786CF2}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\LexAI.DataPreprocessing.Application.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.application\\commands\\documentcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "DesignTimeDbContextFactory.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\DesignTimeDbContextFactory.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\DesignTimeDbContextFactory.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\DesignTimeDbContextFactory.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\DesignTimeDbContextFactory.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAA8AAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T12:02:58.269Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DataPreprocessingDbContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Data\\DataPreprocessingDbContext.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Data\\DataPreprocessingDbContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Data\\DataPreprocessingDbContext.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Data\\DataPreprocessingDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T11:28:03.807Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "DesignTimeDataPreprocessingDbContextFactory.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\DesignTimeDataPreprocessingDbContextFactory.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\DesignTimeDataPreprocessingDbContextFactory.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\DesignTimeDataPreprocessingDbContextFactory.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\DesignTimeDataPreprocessingDbContextFactory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T11:27:28.069Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "OrchestrationAgent.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Agents\\OrchestrationAgent.cs", "ViewState": "AgIAAIUBAAAAAAAAAAAAAIsBAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T11:07:31.997Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DocumentCommands.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Commands\\DocumentCommands.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Commands\\DocumentCommands.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Commands\\DocumentCommands.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Application\\Commands\\DocumentCommands.cs", "ViewState": "AgIAAIIAAAAAAAAAAAAAAIYAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T09:52:19.937Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DocumentsController.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Controllers\\DocumentsController.cs", "ViewState": "AgIAAIQAAAAAAAAAAAAIwI4AAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T08:46:49.939Z", "EditorCaption": ""}]}]}]}