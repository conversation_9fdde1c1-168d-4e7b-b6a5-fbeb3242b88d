2025-06-04 11:40:06.391 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 11:40:06.470 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 11:40:06.486 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-04 11:40:08.893 +04:00 [INF] Executed DbCommand (212ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-04 11:40:08.919 +04:00 [INF] LexAI Identity Service started successfully
2025-06-04 11:40:09.001 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 11:40:10.333 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-04 11:40:10.335 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-04 11:40:10.557 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 11:40:10.560 +04:00 [INF] Hosting environment: Development
2025-06-04 11:40:10.563 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-04 11:40:12.795 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-04 11:40:13.446 +04:00 [INF] Request GET / started with correlation ID 45dbcea3-906d-49e5-9ac1-f4abc4d9d968
2025-06-04 11:40:15.242 +04:00 [INF] Request GET / completed in 1784ms with status 404 (Correlation ID: 45dbcea3-906d-49e5-9ac1-f4abc4d9d968)
2025-06-04 11:40:15.256 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 2491.2218ms
2025-06-04 11:40:15.267 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-04 11:46:13.611 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 11:46:13.642 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 11:46:13.647 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-04 11:46:14.097 +04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-04 11:46:14.115 +04:00 [INF] LexAI Identity Service started successfully
2025-06-04 11:46:14.151 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 11:46:14.378 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-04 11:46:14.380 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-04 11:46:14.420 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 11:46:14.423 +04:00 [INF] Hosting environment: Development
2025-06-04 11:46:14.425 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-04 11:46:15.624 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-04 11:46:15.800 +04:00 [INF] Request GET / started with correlation ID 5a9fc9fd-bd11-41bb-8582-1450b6670ac9
2025-06-04 11:46:15.911 +04:00 [INF] Request GET / completed in 100ms with status 404 (Correlation ID: 5a9fc9fd-bd11-41bb-8582-1450b6670ac9)
2025-06-04 11:46:15.922 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 313.8492ms
2025-06-04 11:46:15.929 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-04 11:47:13.871 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-04 11:47:13.886 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 8f7dad7a-3bb9-488a-adff-110517a0feca
2025-06-04 11:47:13.894 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:47:13.900 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 8ms with status 204 (Correlation ID: 8f7dad7a-3bb9-488a-adff-110517a0feca)
2025-06-04 11:47:13.906 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 35.3861ms
2025-06-04 11:47:13.909 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-04 11:47:13.938 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 1a5afe43-2c3b-4ca3-803e-e1b2b7f04bd7
2025-06-04 11:47:13.943 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:47:13.948 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-04 11:47:14.017 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-04 11:47:14.106 +04:00 [INF] Token refresh attempt
2025-06-04 11:47:14.226 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-04 11:47:15.344 +04:00 [INF] Executed DbCommand (67ms) [Parameters=[@__token_0='XTpCuQd2hdAxzZ5Ts3zGGZv9wzDnNlm8PbaIKXAiZiJNOl4NHGdfZakyo3m15Ss+lMUnVZsb8scDN4OG+SaDww=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-04 11:47:15.821 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-04 11:47:15.887 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__id_0='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-04 11:47:15.936 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='XTpCuQd2hdAxzZ5Ts3zGGZv9wzDnNlm8PbaIKXAiZiJNOl4NHGdfZakyo3m15Ss+lMUnVZsb8scDN4OG+SaDww=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-04 11:47:16.230 +04:00 [INF] Executed DbCommand (18ms) [Parameters=[@p16='77722b4f-e2fe-4dfd-b8e7-7f1b8b99702c', @p0='2025-06-02T20:16:51.6657010Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-09T20:16:51.6637010Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-04T07:47:15.9437245Z' (Nullable = true) (DbType = DateTime), @p10='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p11='XTpCuQd2hdAxzZ5Ts3zGGZv9wzDnNlm8PbaIKXAiZiJNOl4NHGdfZakyo3m15Ss+lMUnVZsb8scDN4OG+SaDww==' (Nullable = false), @p12='2025-06-04T07:47:16.0590941Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p17='819' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-04 11:47:16.269 +04:00 [INF] Refresh token updated successfully: "77722b4f-e2fe-4dfd-b8e7-7f1b8b99702c"
2025-06-04 11:47:16.392 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='9fe0ce38-b537-446f-b2c1-467bd37eef01', @p1='2025-06-04T07:47:16.3750259Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-11T07:47:16.3474434Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='/9qzp1tbZeCGiEjKV/tMITBYd/U+Ltym+FTgfA7/EBT0irof3QYlMZyOBGsil6PAqLoln7/iFAsGiX+kua5Kpw==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-04 11:47:16.401 +04:00 [INF] Refresh token added successfully: "9fe0ce38-b537-446f-b2c1-467bd37eef01"
2025-06-04 11:47:16.403 +04:00 [INF] Token refreshed successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 11:47:16.409 +04:00 [INF] Token refresh successful
2025-06-04 11:47:16.425 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-04 11:47:16.499 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 2467.6502ms
2025-06-04 11:47:16.505 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-04 11:47:16.508 +04:00 [INF] Request POST /api/auth/refresh completed in 2566ms with status 200 (Correlation ID: 1a5afe43-2c3b-4ca3-803e-e1b2b7f04bd7)
2025-06-04 11:47:16.522 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 200 null application/json; charset=utf-8 2612.609ms
